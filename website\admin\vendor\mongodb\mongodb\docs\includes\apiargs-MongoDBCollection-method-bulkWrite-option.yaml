source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: bypassDocumentValidation
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.13
---
source:
  file: apiargs-common-option.yaml
  ref: let
post: |
  .. versionadded:: 1.13
---
arg_name: option
name: ordered
type: boolean
description: |
  If ``true``: when a single write fails, the operation will stop without
  performing the remaining writes and throw an exception.

  If ``false``: when a single write fails, the operation will continue with the
  remaining writes, if any, and throw an exception.

  The default is ``true``.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: writeConcern
...
