<?php
/**
 * Account Portal - Punishments Page
 * Shows user's in-game punishments (only available when Xbox account is linked)
 */

require_once __DIR__ . '/../includes/core.php';
require_once __DIR__ . '/../includes/player-auth.php';
require_once __DIR__ . '/../includes/db_access.php';

// Require player authentication
require_player_auth();

// Get player data
$player_data = get_player_data();

// Get linked accounts to check if Xbox is linked
$linked_accounts = [];
$xbox_username = null;
try {
    $dbAccess = new DatabaseAccess();
    $db = $dbAccess->db;

    if ($db) {
        $linkedAccountsData = $db->linked_accounts->find([
            'discord_id' => $player_data['discord_id'],
            'status' => 'verified'
        ]);

        foreach ($linkedAccountsData as $account) {
            $linked_accounts[$account['platform']] = [
                'username' => $account['username'],
                'verified_at' => $account['verified_at'] ?? null,
                'xbox_id' => $account['xbox_id'] ?? null
            ];
        }
        
        // Get Xbox username if linked
        if (isset($linked_accounts['xbox'])) {
            $xbox_username = $linked_accounts['xbox']['username'];
        }
    }
} catch (Exception $e) {
    secure_log("Error fetching linked accounts for punishments", "error", [
        'discord_id' => $player_data['discord_id'],
        'error' => $e->getMessage()
    ]);
}

// If no Xbox account is linked, redirect to settings
if (!$xbox_username) {
    $_SESSION['error'] = 'You must link your Xbox account to view your punishments.';
    header('Location: /account/settings');
    exit;
}

// Get punishments for the Xbox username using the same method as player.php
$punishments = [];
try {
    if ($dbAccess && $dbAccess->db) {
        // Use the same method that player.php uses for consistency
        $punishments = $dbAccess->get_player_punishments($xbox_username);

        // Ensure it's an array
        if (!is_array($punishments)) {
            $punishments = [];
        }

        secure_log("Punishment search completed", "info", [
            'discord_id' => $player_data['discord_id'],
            'xbox_username' => $xbox_username,
            'punishments_found' => count($punishments)
        ]);
    }
} catch (Exception $e) {
    secure_log("Error fetching punishments", "error", [
        'discord_id' => $player_data['discord_id'],
        'xbox_username' => $xbox_username,
        'error' => $e->getMessage()
    ]);
}

// Handle session messages
$error = $_SESSION['error'] ?? null;
$success = $_SESSION['success'] ?? null;
unset($_SESSION['error']);
unset($_SESSION['success']);

require_once __DIR__ . '/../includes/layout-unified.php';

$pageTitle = "My Punishments - MassacreMC Account Portal";
renderHeader($pageTitle, ['/css/services.css', '/css/account-portal.css']);
renderNavbar();
?>

<style>
/* Punishment Cards Styling */
.punishment-card {
    transition: all 0.3s ease;
    background: rgba(33, 37, 41, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.punishment-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
}

.punishment-card .card-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 600;
}

.punishment-card .card-body {
    background: transparent !important;
}

.punishment-detail {
    padding: 0.5rem 0;
}

.punishment-detail small.text-muted {
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.7rem;
    letter-spacing: 0.5px;
}

/* Mobile Fixes */
@media (max-width: 768px) {
    body {
        background: #1a1d23 !important;
    }

    .container-fluid {
        background: #1a1d23 !important;
    }

    .punishment-card {
        background: rgba(33, 37, 41, 0.98) !important;
        margin-bottom: 1rem;
    }

    .punishment-card .card-header {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .punishment-card .card-body {
        padding: 1rem;
        background: transparent !important;
    }

    .punishment-detail {
        padding: 0.25rem 0;
    }

    .punishment-detail small {
        font-size: 0.75rem;
    }

    .row.g-3 {
        margin: 0;
    }

    .col-12 {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}

/* Extra small screens */
@media (max-width: 576px) {
    .punishment-card .card-header {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;
    }

    .punishment-card .card-header .d-flex:first-child {
        width: 100%;
    }

    .punishment-card .card-body {
        padding: 0.75rem;
    }

    .row.g-2 .col-6 {
        margin-bottom: 0.5rem;
    }
}

/* Dark theme enforcement */
.card, .card-body, .card-header {
    background: rgba(33, 37, 41, 0.95) !important;
    color: white !important;
}

.text-white {
    color: white !important;
}

.text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Badge styling */
.badge {
    font-weight: 500;
    padding: 0.35rem 0.65rem;
}

/* Code styling */
code.text-light {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.8rem;
}
</style>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-gavel me-3 text-warning"></i>My Punishments
                </h1>
                <p class="lead text-muted">View your in-game punishment history for <strong><?php echo htmlspecialchars($xbox_username); ?></strong></p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

                        <!-- Info Alert -->
            <div class="alert alert-info mt-4" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Note:</strong> This shows punishments for your linked Xbox account (<?php echo htmlspecialchars($xbox_username); ?>). 
                If you believe any punishment was issued in error, you can submit an appeal through our 
                <a href="/account/appeals" class="alert-link">appeals system</a>.
            </div>

            <!-- Punishments List -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-list me-2"></i>Punishment History
                        <span class="badge bg-dark ms-2"><?php echo count($punishments); ?></span>
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (empty($punishments)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                            <h4 class="text-success">Clean Record!</h4>
                            <p class="text-muted">You have no punishments on record. Keep up the good behavior!</p>
                        </div>
                    <?php else: ?>
                        <div class="punishment-cards-container">
                            <div class="row g-3">
                                <?php foreach ($punishments as $punishment): ?>
                                    <?php
                                    $type = $punishment['type'] ?? 'Unknown';
                                    $typeClass = match(strtolower($type)) {
                                        'ban' => 'danger',
                                        'tempban' => 'warning',
                                        'mute' => 'info',
                                        'kick' => 'secondary',
                                        'warn' => 'primary',
                                        default => 'dark'
                                    };

                                    $typeIcon = match(strtolower($type)) {
                                        'ban' => 'fa-ban',
                                        'tempban' => 'fa-clock',
                                        'mute' => 'fa-microphone-slash',
                                        'kick' => 'fa-door-open',
                                        'warn' => 'fa-exclamation-triangle',
                                        default => 'fa-question-circle'
                                    };

                                    // Get punishment ID
                                    $punishmentId = $punishment['_id'] ?? $punishment['id'] ?? $punishment['punishment_id'] ?? 'Unknown';
                                    if (is_object($punishmentId) && method_exists($punishmentId, '__toString')) {
                                        $punishmentId = (string)$punishmentId;
                                    }

                                    // Get status
                                    $status = $punishment['status'] ?? 'active';
                                    $statusClass = match(strtolower($status)) {
                                        'active' => 'danger',
                                        'expired' => 'secondary',
                                        'pardoned' => 'success',
                                        'appealed' => 'info',
                                        default => 'dark'
                                    };
                                    ?>

                                    <div class="col-12 col-md-6 col-lg-4">
                                        <div class="card punishment-card border-0 shadow-sm h-100" style="background: rgba(33, 37, 41, 0.95); border: 1px solid rgba(255, 255, 255, 0.1) !important;">
                                            <div class="card-header bg-<?php echo $typeClass; ?> text-white d-flex align-items-center justify-content-between">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas <?php echo $typeIcon; ?> me-2"></i>
                                                    <span class="fw-bold"><?php echo ucfirst($type); ?></span>
                                                </div>
                                                <span class="badge bg-<?php echo $statusClass; ?> text-white">
                                                    <?php echo htmlspecialchars(ucfirst($status)); ?>
                                                </span>
                                            </div>
                                            <div class="card-body text-white">
                                                <div class="mb-3">
                                                    <h6 class="text-muted mb-1">
                                                        <i class="fas fa-comment-alt me-1"></i>Reason
                                                    </h6>
                                                    <p class="mb-0 text-break">
                                                        <?php echo htmlspecialchars($punishment['reason'] ?? 'No reason provided'); ?>
                                                    </p>
                                                </div>

                                                <div class="row g-2 mb-3">
                                                    <div class="col-6">
                                                        <div class="punishment-detail">
                                                            <small class="text-muted d-block">
                                                                <i class="fas fa-hashtag me-1"></i>Punishment ID
                                                            </small>
                                                            <code class="text-light small">
                                                                <?php
                                                                if (strlen($punishmentId) > 12) {
                                                                    echo htmlspecialchars(substr($punishmentId, 0, 8) . '...');
                                                                } else {
                                                                    echo htmlspecialchars($punishmentId);
                                                                }
                                                                ?>
                                                            </code>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="punishment-detail">
                                                            <small class="text-muted d-block">
                                                                <i class="fas fa-calendar me-1"></i>Date Issued
                                                            </small>
                                                            <small class="text-white">
                                                                <?php
                                                                // Try multiple date field names and formats
                                                                $date = null;
                                                                $dateFields = ['created_at', 'date', 'timestamp', 'time', 'issued_at'];

                                                                foreach ($dateFields as $field) {
                                                                    if (isset($punishment[$field])) {
                                                                        $dateValue = $punishment[$field];

                                                                        try {
                                                                            if (is_object($dateValue) && method_exists($dateValue, 'toDateTime')) {
                                                                                // MongoDB UTCDateTime
                                                                                $date = $dateValue->toDateTime();
                                                                                break;
                                                                            } elseif (is_string($dateValue)) {
                                                                                // String date
                                                                                $date = new DateTime($dateValue);
                                                                                break;
                                                                            } elseif (is_numeric($dateValue)) {
                                                                                // Unix timestamp
                                                                                $date = new DateTime();
                                                                                $date->setTimestamp($dateValue);
                                                                                break;
                                                                            }
                                                                        } catch (Exception $e) {
                                                                            // Continue to next field
                                                                            continue;
                                                                        }
                                                                    }
                                                                }

                                                                if ($date) {
                                                                    // Convert to EST
                                                                    $date->setTimezone(new DateTimeZone('America/New_York'));
                                                                    echo $date->format('M j, Y');
                                                                    echo '<br>' . $date->format('g:i A') . ' EST';
                                                                } else {
                                                                    echo 'Unknown';
                                                                }
                                                                ?>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="punishment-detail">
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-clock me-1"></i>Expires
                                                    </small>
                                                    <small class="text-white">
                                                        <?php
                                                        // Try to find duration in different possible fields
                                                        $duration = null;
                                                        $durationFields = ['duration', 'length', 'expires_at', 'expiry', 'end_time', 'until'];

                                                        foreach ($durationFields as $field) {
                                                            if (isset($punishment[$field]) && $punishment[$field] !== null && $punishment[$field] !== 0) {
                                                                $duration = $punishment[$field];
                                                                break;
                                                            }
                                                        }

                                                        // Check if punishment type suggests it's temporary
                                                        $typeCheck = strtolower($punishment['type'] ?? '');
                                                        $isTemporary = in_array($typeCheck, ['tempban', 'tempmute', 'timeout']);

                                                        // Check if duration indicates permanent
                                                        $isPermanent = false;
                                                        if ($duration) {
                                                            $durationStr = strtolower(trim($duration));
                                                            $isPermanent = in_array($durationStr, ['permanent', 'perm', 'forever', 'never', '-1', 'indefinite']);
                                                        }

                                                        if ($isPermanent) {
                                                            echo '<span class="text-warning">Permanent</span>';
                                                        } elseif ($duration && $duration > 0 && !$isPermanent) {
                                                            // Try to get creation date
                                                            $createdAt = null;
                                                            $dateFields = ['created_at', 'date', 'timestamp', 'time', 'issued_at'];

                                                            foreach ($dateFields as $field) {
                                                                if (isset($punishment[$field])) {
                                                                    try {
                                                                        if (is_object($punishment[$field]) && method_exists($punishment[$field], 'toDateTime')) {
                                                                            $createdAt = $punishment[$field]->toDateTime();
                                                                            break;
                                                                        } elseif (is_string($punishment[$field])) {
                                                                            $createdAt = new DateTime($punishment[$field]);
                                                                            break;
                                                                        } elseif (is_numeric($punishment[$field])) {
                                                                            $createdAt = new DateTime();
                                                                            $createdAt->setTimestamp($punishment[$field]);
                                                                            break;
                                                                        }
                                                                    } catch (Exception $e) {
                                                                        continue;
                                                                    }
                                                                }
                                                            }

                                                            if ($createdAt) {
                                                                // Calculate expiration time
                                                                $expiresAt = clone $createdAt;

                                                                try {
                                                                    // Handle different duration formats
                                                                    if (is_numeric($duration)) {
                                                                        // Assume seconds if it's a plain number
                                                                        $expiresAt->add(new DateInterval('PT' . $duration . 'S'));
                                                                    } elseif (is_string($duration)) {
                                                                        // Handle string formats like "10d", "5h", "30m"
                                                                        if (preg_match('/^(\d+)([dhms])$/i', $duration, $matches)) {
                                                                            $value = $matches[1];
                                                                            $unit = strtolower($matches[2]);

                                                                            switch ($unit) {
                                                                                case 'd':
                                                                                    $expiresAt->add(new DateInterval('P' . $value . 'D'));
                                                                                    break;
                                                                                case 'h':
                                                                                    $expiresAt->add(new DateInterval('PT' . $value . 'H'));
                                                                                    break;
                                                                        case 'm':
                                                                            $expiresAt->add(new DateInterval('PT' . $value . 'M'));
                                                                            break;
                                                                        case 's':
                                                                            $expiresAt->add(new DateInterval('PT' . $value . 'S'));
                                                                            break;
                                                                    }
                                                                } else {
                                                                    // Fallback: assume it's seconds
                                                                    $numericDuration = intval($duration);
                                                                    if ($numericDuration > 0) {
                                                                        $expiresAt->add(new DateInterval('PT' . $numericDuration . 'S'));
                                                                    } else {
                                                                        throw new Exception('Invalid duration format');
                                                                    }
                                                                }
                                                            } else {
                                                                throw new Exception('Invalid duration type');
                                                            }

                                                            // Convert to EST
                                                            $expiresAt->setTimezone(new DateTimeZone('America/New_York'));

                                                            // Check if punishment has expired
                                                            $now = new DateTime('now', new DateTimeZone('America/New_York'));
                                                            if ($expiresAt < $now) {
                                                                echo '<span class="text-secondary">Expired</span>';
                                                            } else {
                                                                echo $expiresAt->format('M j, Y g:i A') . ' EST';
                                                            }
                                                        } catch (Exception $e) {
                                                            // If duration parsing fails, show as temporary
                                                            echo '<span class="text-info">Temporary</span>';
                                                        }
                                                    } else {
                                                        echo '<span class="text-info">Temporary</span>';
                                                    }
                                                } elseif ($isTemporary) {
                                                    echo '<span class="text-info">Temporary</span>';
                                                } else {
                                                    echo '<span class="text-warning">Permanent</span>';
                                                }
                                                ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4">
                <a href="/account/" class="btn btn-secondary me-3">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
                <a href="/account/settings" class="btn btn-outline-primary">
                    <i class="fas fa-cog me-2"></i>Account Settings
                </a>
            </div>
        </div>
    </div>
</div>

<?php
renderFooter(['/js/account-portal.js', '/js/account-portal-mobile.js']);
?>
