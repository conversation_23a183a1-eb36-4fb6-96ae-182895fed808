<?php
/**
 * Player Account Portal - Tickets
 * View and manage support tickets
 */

require_once __DIR__ . '/../includes/core.php';
require_once __DIR__ . '/../includes/player-auth.php';
require_once __DIR__ . '/../includes/layout-unified.php';

// Require player authentication
require_player_auth();

$player_data = get_player_data();
$submissions = get_player_submissions($player_data['discord_id']);
$tickets = $submissions['tickets'];

$pageTitle = "My Tickets - MassacreMC";
renderHeader($pageTitle, ['/css/services.css', '/css/account-portal.css']);
renderNavbar();
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-5 fw-bold">
                        <i class="fas fa-ticket-alt me-3 text-primary"></i>My Support Tickets
                    </h1>
                    <p class="lead text-muted">View and manage your support tickets</p>
                </div>
                <a href="/help" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>New Ticket
                </a>
            </div>

            <?php if (empty($tickets)): ?>
            <!-- No Tickets -->
            <div class="text-center py-5">
                <i class="fas fa-ticket-alt fa-5x text-muted mb-4"></i>
                <h3>No Support Tickets</h3>
                <p class="text-muted mb-4">You haven't submitted any support tickets yet.</p>
                <a href="/help" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Submit Your First Ticket
                </a>
            </div>
            <?php else: ?>
            
            <!-- Tickets List -->
            <div class="row">
                <?php foreach ($tickets as $ticket): ?>
                <div class="col-12 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1"><?php echo htmlspecialchars($ticket['subject']); ?></h5>
                                <small class="text-muted">
                                    Ticket ID: <?php echo htmlspecialchars($ticket['ticket_id']); ?> • 
                                    Created: <?php echo $ticket['created_at']->toDateTime()->format('M j, Y g:i A'); ?>
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php
                                    echo $ticket['status'] === 'open' ? 'success' :
                                        ($ticket['status'] === 'pending' ? 'warning' :
                                        ($ticket['status'] === 'resolved' ? 'secondary' : 'info'));
                                ?> fs-6">
                                    <?php echo ucfirst($ticket['status']); ?>
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <p class="mb-2"><strong>Category:</strong> 
                                        <span class="badge bg-light text-dark">
                                            <?php 
                                            $categories = [
                                                'technical' => 'Technical Support',
                                                'billing' => 'Billing & Payments',
                                                'general' => 'General Inquiry',
                                                'bug' => 'Bug Report',
                                                'feature' => 'Feature Request',
                                                'privacy' => 'GDPR Request'
                                            ];
                                            echo $categories[$ticket['category']] ?? ucfirst($ticket['category']);
                                            ?>
                                        </span>
                                    </p>
                                    <p class="mb-3"><?php echo nl2br(htmlspecialchars(substr($ticket['description'], 0, 200))); ?>
                                        <?php if (strlen($ticket['description']) > 200): ?>...<?php endif; ?>
                                    </p>
                                </div>
                                <div class="col-md-4">
                                    <?php if (isset($ticket['last_activity'])): ?>
                                    <p class="mb-1"><strong>Last Activity:</strong>
                                        <?php echo $ticket['last_activity']->toDateTime()->format('M j, Y g:i A'); ?>
                                    </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <?php if (isset($ticket['conversation_token'])): ?>
                            <div class="text-end">
                                <a href="https://help.massacremc.net/conversations?id=<?php echo urlencode($ticket['ticket_id']); ?>&token=<?php echo urlencode($ticket['conversation_token']); ?>"
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-comments me-2"></i>View Conversation
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination would go here if needed -->
            
            <?php endif; ?>

            <!-- Help Section -->
            <div class="row mt-5">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-question-circle fa-3x text-info mb-3"></i>
                            <h5>Need Help?</h5>
                            <p class="text-muted">Check our knowledge base for common questions and solutions.</p>
                            <a href="/rules" class="btn btn-outline-info">View Knowledge Base</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fab fa-discord fa-3x text-primary mb-3"></i>
                            <h5>Discord Community</h5>
                            <p class="text-muted">Join our Discord server for real-time support and community chat.</p>
                            <a href="https://discord.gg/mBjd5cXSxR" class="btn btn-outline-primary" target="_blank">
                                Join Discord
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Back to Dashboard -->
            <div class="text-center mt-4">
                <a href="/account/" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<?php
renderFooter(['/js/account-portal.js', '/js/account-portal-mobile.js']);
?>
