=====================================
MongoDB\\MapReduceResult::getCounts()
=====================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\MapReduceResult::getCounts()

   Returns count statistics for the map-reduce operation.

   .. code-block:: php

      function getCounts(): array

Return Values
-------------

An array of count statistics for the map-reduce operation.

Examples
--------

This example reports the count statistics for a map-reduce operation.

.. code-block:: php

   <?php

   $collection = (new MongoDB\Client)->test->zips;

   $map = new MongoDB\BSON\Javascript('function() { emit(this.state, this.pop); }');
   $reduce = new MongoDB\BSON\Javascript('function(key, values) { return Array.sum(values) }');
   $out = ['inline' => 1];

   $result = $collection->mapReduce($map, $reduce, $out);

   var_dump($result->getCounts());

The output would then resemble::

   array(4) {
     ["input"]=>
     int(29353)
     ["emit"]=>
     int(29353)
     ["reduce"]=>
     int(180)
     ["output"]=>
     int(51)
   }

See Also
--------

- :phpmethod:`MongoDB\\Collection::mapReduce()`
- :manual:`mapReduce </reference/command/mapReduce>` command reference in the
  MongoDB manual
