=================================
MongoDB\\GridFS\\Bucket::rename()
=================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::rename()

   Selects a GridFS file by its ``_id`` and alters its ``filename``.

   .. code-block:: php

      function rename($id, string $newFilename): void

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-rename-param.rst

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-gridfs-filenotfoundexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $stream = fopen('php://temp', 'w+b');
   fwrite($stream, "foobar");
   rewind($stream);

   $id = $bucket->uploadFromStream('a', $stream);

   $bucket->rename($id, 'b');

   var_dump(stream_get_contents($bucket->openDownloadStreamByName('b')));

The output would then resemble::

   string(6) "foobar"
