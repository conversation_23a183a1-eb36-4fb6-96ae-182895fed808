<?php
/**
 * Player Account Portal Dashboard
 * Main dashboard for authenticated players
 */

require_once __DIR__ . '/../includes/core.php';
require_once __DIR__ . '/../includes/player-auth.php';
require_once __DIR__ . '/../includes/layout-unified.php';

// Require player authentication
require_player_auth();

$player_data = get_player_data();
$submissions = get_player_submissions($player_data['discord_id']);

// Check if user has Xbox account linked and get punishments
$xbox_username = null;
$user_punishments = [];
try {
    $dbAccess = new DatabaseAccess();
    if ($dbAccess && $dbAccess->db) {
        $xbox_link = $dbAccess->db->linked_accounts->findOne([
            'discord_id' => $player_data['discord_id'],
            'platform' => 'xbox',
            'status' => 'verified'
        ]);
        if ($xbox_link) {
            $xbox_username = $xbox_link['username'];

            // Get user's punishments
            $user_punishments = $dbAccess->get_player_punishments($xbox_username);
            if (!is_array($user_punishments)) {
                $user_punishments = [];
            }
        }
    }
} catch (Exception $e) {
    secure_log("Error checking Xbox link for dashboard", "error", [
        'discord_id' => $player_data['discord_id'],
        'error' => $e->getMessage()
    ]);
}

$pageTitle = "Account Dashboard - MassacreMC";
renderHeader($pageTitle, ['/css/account-dashboard-simple.css']);
renderNavbar();
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Welcome Header -->
            <div class="welcome-section mb-5">
                <div class="d-flex align-items-center">
                    <div class="welcome-avatar me-4">
                        <?php
                        // Construct avatar URL - handle null/empty avatars
                        if (!empty($player_data['discord_avatar']) && $player_data['discord_avatar'] !== 'null') {
                            $avatar_url = "https://cdn.discordapp.com/avatars/{$player_data['discord_id']}/{$player_data['discord_avatar']}.png?size=128";
                        } else {
                            // Use default avatar - calculate based on user ID
                            $default_avatar_id = intval($player_data['discord_id']) % 5;
                            $avatar_url = "https://cdn.discordapp.com/embed/avatars/{$default_avatar_id}.png";
                        }
                        ?>
                        <img src="<?php echo $avatar_url; ?>" alt="Avatar" class="rounded-circle" width="80" height="80">
                    </div>
                    <div>
                        <h1 class="h2 mb-2">Welcome back, <?php echo htmlspecialchars($player_data['discord_username']); ?>!</h1>
                        <p class="text-muted mb-2">Account Dashboard</p>
                        <div class="d-flex gap-3 text-sm">
                            <span><i class="fas fa-calendar-alt me-1"></i>Member since <?php echo date('M Y', strtotime($player_data['created_at'] ?? 'now')); ?></span>
                            <?php if ($xbox_username): ?>
                            <span><i class="fab fa-xbox me-1"></i>Xbox: <?php echo htmlspecialchars($xbox_username); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mb-5">
                <h2 class="h4 mb-4">Quick Actions</h2>
                <div class="row g-3">
                    <div class="col-md-6 col-lg-3">
                        <a href="/help" class="btn btn-outline-primary w-100 p-3 text-decoration-none">
                            <i class="fas fa-plus-circle fa-2x mb-2 d-block"></i>
                            <div class="fw-semibold">Create Ticket</div>
                            <small class="text-muted">Get help with issues</small>
                        </a>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <a href="/appeal" class="btn btn-outline-warning w-100 p-3 text-decoration-none">
                            <i class="fas fa-balance-scale fa-2x mb-2 d-block"></i>
                            <div class="fw-semibold">Submit Appeal</div>
                            <small class="text-muted">Appeal a punishment</small>
                        </a>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <a href="/report" class="btn btn-outline-danger w-100 p-3 text-decoration-none">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2 d-block"></i>
                            <div class="fw-semibold">Report Player</div>
                            <small class="text-muted">Report rule violations</small>
                        </a>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <a href="/staff-application" class="btn btn-outline-info w-100 p-3 text-decoration-none">
                            <i class="fas fa-user-tie fa-2x mb-2 d-block"></i>
                            <div class="fw-semibold">Staff Application</div>
                            <small class="text-muted">Join our team</small>
                        </a>
                    </div>
                </div>
            </div>


            <!-- Recent Activity -->
            <div class="row g-4">
                <!-- Recent Tickets -->
                <?php if (!empty($submissions['tickets'])): ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header d-flex align-items-center">
                            <i class="fas fa-ticket-alt me-2 text-primary"></i>
                            <h5 class="mb-0">Recent Tickets</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach (array_slice($submissions['tickets'], 0, 3) as $ticket): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <div class="fw-semibold"><?php echo htmlspecialchars($ticket['subject']); ?></div>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($ticket['ticket_id']); ?> •
                                        <?php echo $ticket['created_at']->toDateTime()->format('M j, Y'); ?>
                                    </small>
                                </div>
                                <span class="badge <?php echo $ticket['status'] === 'open' ? 'bg-success' : ($ticket['status'] === 'pending' ? 'bg-warning' : 'bg-secondary'); ?>">
                                    <?php echo ucfirst($ticket['status']); ?>
                                </span>
                            </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="/account/tickets" class="btn btn-outline-primary btn-sm">
                                    View All Tickets
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Recent Appeals -->
                <?php if (!empty($submissions['appeals'])): ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header d-flex align-items-center">
                            <i class="fas fa-gavel me-2 text-warning"></i>
                            <h5 class="mb-0">Recent Appeals</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach (array_slice($submissions['appeals'], 0, 3) as $appeal): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <div class="fw-semibold"><?php
                                        $punishment_type = ucfirst(strtolower($appeal['punishment_type']));
                                        echo htmlspecialchars($punishment_type);
                                    ?> Appeal</div>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($appeal['id']); ?> •
                                        <?php echo $appeal['created_at']->toDateTime()->format('M j, Y'); ?>
                                    </small>
                                </div>
                                <span class="badge <?php echo $appeal['status'] === 'Pending' ? 'bg-secondary' : ($appeal['status'] === 'Approved' ? 'bg-success' : 'bg-danger'); ?>">
                                    <?php echo $appeal['status']; ?>
                                </span>
                            </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="/account/appeals" class="btn btn-outline-warning btn-sm">
                                    View All Appeals
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Recent Reports -->
                <?php if (!empty($submissions['reports'])): ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header d-flex align-items-center">
                            <i class="fas fa-flag me-2 text-danger"></i>
                            <h5 class="mb-0">Recent Reports</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach (array_slice($submissions['reports'], 0, 3) as $report): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <div class="fw-semibold">Player Report</div>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($report['_id'] ?? $report['report_id'] ?? 'Unknown'); ?> •
                                        <?php echo $report['created_at']->toDateTime()->format('M j, Y'); ?>
                                    </small>
                                </div>
                                <span class="badge <?php echo $report['status'] === 'Pending' ? 'bg-secondary' : (($report['status'] === 'Resolved' || $report['status'] === 'Accepted') ? 'bg-success' : 'bg-danger'); ?>">
                                    <?php echo $report['status']; ?>
                                </span>
                            </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="/account/reports" class="btn btn-outline-danger btn-sm">
                                    View All Reports
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Recent Applications -->
                <?php if (!empty($submissions['applications'])): ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header d-flex align-items-center">
                            <i class="fas fa-user-tie me-2 text-info"></i>
                            <h5 class="mb-0">Recent Applications</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach (array_slice($submissions['applications'], 0, 3) as $application): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <div class="fw-semibold"><?php echo htmlspecialchars(ucfirst($application['position'])); ?> Application</div>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($application['application_id']); ?> •
                                        <?php echo $application['submitted_at']->toDateTime()->format('M j, Y'); ?>
                                    </small>
                                </div>
                                <span class="badge <?php
                                    $status = $application['status'];
                                    echo $status === 'pending' ? 'bg-secondary' :
                                         ($status === 'approved' ? 'bg-success' :
                                         ($status === 'under_review' ? 'bg-info' : 'bg-danger'));
                                ?>">
                                    <?php echo htmlspecialchars(ucfirst(str_replace('_', ' ', $status))); ?>
                                </span>
                            </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="/account/staff-applications" class="btn btn-outline-info btn-sm">
                                    View All Applications
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

            </div>

            <!-- Punishments (only show if Xbox is linked) -->
            <?php if ($xbox_username): ?>
            <div class="mt-4">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <i class="fas fa-ban me-2 text-warning"></i>
                        <h5 class="mb-0">My Punishments</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($user_punishments)): ?>
                            <?php foreach (array_slice($user_punishments, 0, 3) as $punishment): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <div class="fw-semibold"><?php
                                        $type = ucfirst($punishment['type'] ?? 'Unknown');
                                        echo htmlspecialchars($type);
                                    ?></div>
                                    <small class="text-muted">
                                        <?php
                                        $punishment_id = $punishment['punishment_id'] ?? $punishment['id'] ?? 'Unknown ID';
                                        if (is_object($punishment_id) && method_exists($punishment_id, '__toString')) {
                                            $punishment_id = (string)$punishment_id;
                                        }
                                        echo htmlspecialchars($punishment_id) . ' • ';

                                        $date = 'Unknown date';
                                        if (isset($punishment['date']) && !empty($punishment['date'])) {
                                            try {
                                                $dateObj = new DateTime($punishment['date']);
                                                $date = $dateObj->format('M j, Y');
                                            } catch (Exception $e) {
                                                $date = 'Unknown date';
                                            }
                                        }
                                        echo htmlspecialchars($date);
                                        ?>
                                    </small>
                                </div>
                                <span class="badge <?php echo ($punishment['active'] ?? false) ? 'bg-danger' : 'bg-secondary'; ?>">
                                    <?php echo ($punishment['active'] ?? false) ? 'Active' : 'Expired'; ?>
                                </span>
                            </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="/account/punishments" class="btn btn-outline-warning btn-sm">
                                    View All Punishments
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h5>No Punishments</h5>
                                <p class="text-muted">You have a clean record!</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
renderFooter(['/js/account-portal.js', '/js/account-portal-mobile.js']);
?>
