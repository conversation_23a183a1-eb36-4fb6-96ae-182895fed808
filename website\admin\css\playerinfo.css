/* Custom styles for the Player Info page */

/* Tab Panel Styles */
.tab-panel {
    display: none;
    padding: 1.5rem 0;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-panel.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading spinner */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    width: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s;
}

.loading-spinner.active {
    opacity: 1;
    visibility: visible;
}

/* Enhanced search container */
.search-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    .search-container {
        flex-direction: column;
    }
}

/* Search history */
.search-history {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.search-history-title {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 12px;
    font-weight: 500;
}

.history-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.history-tag {
    display: inline-flex;
    align-items: center;
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 6px 12px;
    font-size: 0.8125rem;
    color: var(--text-dark);
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-tag:hover {
    background-color: var(--bg-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.history-tag i {
    font-size: 0.75rem;
    margin-right: 6px;
}

.history-tag.player i {
    color: var(--accent-color);
}

.history-tag.faction i {
    color: var(--danger-color);
}

/* Player/Faction card */
.info-card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.info-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

.info-card .card-header {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 1.25rem 1.5rem;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-card .card-header .header-actions {
    display: flex;
    gap: 0.5rem;
}

.info-card .card-header .header-actions .btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--text-light);
    transition: all 0.2s ease;
}

.info-card .card-header .header-actions .btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Player avatar */
.player-avatar {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    margin-right: 1.25rem;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 2rem;
}

.player-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Rank badge */
.rank-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 1rem;
    font-weight: 500;
    font-size: 0.75rem;
    box-shadow: var(--shadow-sm);
    background-color: var(--accent-color);
    color: var(--text-light);
}

/* Stats display */
.stat-card {
    background-color: var(--bg-light);
    border-radius: var(--border-radius-sm);
    padding: 1.25rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
}

.stat-card:hover {
    background-color: var(--bg-hover);
    transform: translateY(-3px);
    box-shadow: var(--shadow-sm);
}

.stat-card .stat-title {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.stat-card .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0;
}

.stat-card .stat-icon {
    font-size: 2rem;
    color: var(--accent-color);
    opacity: 0.8;
}

.player-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Nav tabs */
.nav-tabs {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
}

.nav-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.nav-tabs .nav-link {
    margin-bottom: -1px;
    border: 1px solid transparent;
    border-top-left-radius: var(--border-radius-sm);
    border-top-right-radius: var(--border-radius-sm);
    color: var(--text-secondary);
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.nav-tabs .nav-link:hover {
    border-color: var(--border-color) var(--border-color) var(--border-color);
    background-color: var(--bg-hover);
    color: var(--accent-color);
}

.nav-tabs .nav-link.active {
    color: var(--accent-color);
    background-color: var(--bg-card);
    border-color: var(--border-color) var(--border-color) var(--bg-card);
    font-weight: 600;
}

.nav-tabs .nav-link i {
    margin-right: 0.5rem;
}

/* Data presentation */
.tab-content {
    padding: 1.5rem 0;
}

.tab-content strong {
    font-weight: 600;
    color: var(--text-dark);
}

.tab-pane {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Punishment cards - Note: Most styles are now in punishment.css */
.punishment-card {
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.25rem;
    transition: all 0.3s ease;
    overflow: hidden;
}

.punishment-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.punishment-card .card-header {
    padding: 1rem 1.25rem;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.punishment-card.ban .card-header {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.punishment-card.mute .card-header {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.punishment-card.warning .card-header {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--info-color);
}

.punishment-card .card-body {
    padding: 1.25rem;
}

.punishment-card .punishment-reason {
    font-style: italic;
    color: var(--text-secondary);
    padding: 0.75rem 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius-sm);
    margin-bottom: 1rem;
    border-left: 3px solid rgba(0, 0, 0, 0.1);
}

.punishment-card .punishment-meta {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.punishment-card .punishment-meta div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.punishment-card .punishment-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* Faction members list */
.members-list {
    list-style: none;
    padding: 0;
    margin: 0;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.members-list li {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    background-color: var(--bg-card);
}

.members-list li:hover {
    background-color: var(--bg-hover);
}

.members-list li:last-child {
    border-bottom: none;
}

.members-list .member-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 0.875rem;
}

.members-list .member-info {
    flex: 1;
}

.members-list .member-name {
    font-weight: 500;
    margin-bottom: 0.125rem;
}

.members-list .member-role {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    margin-left: 0.5rem;
    font-weight: 500;
}

.members-list .member-role.owner {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.members-list .member-role.officer {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.members-list .member-role.member {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--info-color);
}

.members-list .member-status {
    margin-left: auto;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.members-list .member-status.online {
    background-color: var(--success-color);
}

.members-list .member-status.offline {
    background-color: var(--text-muted);
}

/* Search bar enhancements */
.search-input-container {
    position: relative;
    width: 100%;
}

.search-input-container .form-control {
    height: 48px;
    border-radius: 0;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    border-left: none;
    border-right: none;
    box-shadow: none;
}

.search-input-container .form-control:focus {
    box-shadow: none;
    border-color: var(--accent-color);
}

.search-modern {
    position: relative;
}

.search-modern i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    z-index: 10;
}

.search-modern input {
    padding-left: 2.75rem;
}

.search-input-container .form-select {
    height: 48px;
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    padding-left: 1rem;
    font-weight: 500;
    width: 120px !important;
    flex-grow: 0 !important;
    border: 1px solid var(--border-color);
    background-color: var(--bg-light);
}

.search-input-container .btn-primary {
    height: 48px;
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-weight: 500;
}

/* Search suggestions dropdown */
#searchSuggestions {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
    background-color: var(--bg-card);
    z-index: 1050;
}

#searchSuggestions .dropdown-item {
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
    color: var(--text-dark);
    display: flex;
    align-items: center;
}

#searchSuggestions .dropdown-item:hover {
    background-color: var(--bg-hover);
}

#searchSuggestions .dropdown-item.active,
#searchSuggestions .dropdown-item:active {
    background-color: var(--accent-color);
    color: var(--text-light);
}

#searchSuggestions .dropdown-item-icon {
    margin-right: 0.75rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--bg-light);
    color: var(--text-secondary);
}

#searchSuggestions .dropdown-item.player .dropdown-item-icon {
    color: var(--accent-color);
}

#searchSuggestions .dropdown-item.faction .dropdown-item-icon {
    color: var(--danger-color);
}

/* Best match styling - removed as per requirements */

#searchSuggestions .badge {
    font-size: 0.7rem;
    padding: 0.25em 0.6em;
    margin-left: 0.5rem;
}

/* Highlighted text in search suggestions */
#searchSuggestions .highlight {
    background-color: rgba(58, 134, 255, 0.15);
    padding: 0.1em 0.2em;
    border-radius: 2px;
    color: var(--accent-color);
    font-weight: 600;
}

#searchSuggestions .dropdown-item.active .highlight {
    color: var(--text-light);
    background-color: rgba(255, 255, 255, 0.3);
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    color: var(--text-muted);
    background-color: var(--bg-card);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    margin-top: 1rem;
}

.empty-state i {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-muted);
    opacity: 0.3;
}

.empty-state h4 {
    margin-bottom: 0.75rem;
    color: var(--text-dark);
    font-weight: 600;
}

.empty-state p {
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Faction display styles */
.member-item, .ally-item, .request-item {
    padding: 0.75rem 1rem;
    margin-bottom: 0.75rem;
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-light);
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
}

.member-item:hover, .ally-item:hover, .request-item:hover {
    background-color: var(--bg-hover);
    transform: translateX(5px);
    box-shadow: var(--shadow-sm);
}

.member-item.owner {
    background-color: rgba(231, 76, 60, 0.1);
    border-left: 3px solid var(--danger-color);
}

.member-item.officer {
    background-color: rgba(243, 156, 18, 0.1);
    border-left: 3px solid var(--warning-color);
}

.member-item.member {
    background-color: rgba(52, 152, 219, 0.1);
    border-left: 3px solid var(--info-color);
}

.faction-member-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
}

.faction-member-info {
    flex: 1;
}

.faction-member-name {
    font-weight: 600;
    margin-bottom: 0.125rem;
}

.faction-member-role {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.allies-list, .requests-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    background-color: var(--bg-card);
}

/* Card styling for faction display */
.faction-card .card-header {
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faction-card .card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    color: var(--text-dark);
}

.faction-card .card-body {
    padding: 1.25rem;
}

.faction-info {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.faction-info-item {
    padding: 1rem;
    background-color: var(--bg-light);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
}

.faction-info-item .info-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.faction-info-item .info-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
}