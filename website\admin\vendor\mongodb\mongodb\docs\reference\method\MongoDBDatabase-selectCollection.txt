=====================================
MongoDB\\Database::selectCollection()
=====================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Database::selectCollection()

   Selects a collection within the database.

   .. code-block:: php

      function selectCollection($collectionName, array $options = []): MongoDB\Collection

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBDatabase-method-selectCollection-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBDatabase-method-selectCollection-option.rst

Return Values
-------------

A :phpclass:`MongoDB\\Collection` object.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-invalidargumentexception.rst

Behavior
--------

The selected collection inherits options such as read preference and type
mapping from the :phpclass:`Database <MongoDB\\Database>` object. Options may be
overridden via the ``$options`` parameter.

Example
-------

The following example selects the ``users`` collection in the ``test`` database:

.. code-block:: php

   <?php

   $db = (new MongoDB\Client)->test;

   $collection = $db->selectCollection('users');

The following example selects the ``users`` collection in the ``test``
database with a custom read preference:

.. code-block:: php

   <?php

   $db = (new MongoDB\Client)->test;

   $users = $db->selectCollection(
       'users',
       [
           'readPreference' => new MongoDB\Driver\ReadPreference(MongoDB\Driver\ReadPreference::RP_SECONDARY),
       ]
   );

See Also
--------

- :phpmethod:`MongoDB\\Database::__get()`
- :phpmethod:`MongoDB\\Client::selectCollection()`
- :phpmethod:`MongoDB\\Collection::__construct()`
