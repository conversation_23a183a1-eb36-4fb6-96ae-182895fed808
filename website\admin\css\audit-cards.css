/* Card-based Audit Logs Layout */

/* Audit Log Card Container */
.audit-card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
}

/* Audit Log Card */
.audit-card {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    height: 100%;
    margin-bottom: 0.5rem;
}

.audit-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.audit-card .card-header {
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.audit-card .card-body {
    padding: 1.25rem;
}

.audit-card .card-footer {
    background-color: rgba(0, 0, 0, 0.02);
    padding: 0.75rem 1.25rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Audit Type Badges */
.audit-type-badge {
    padding: 0.35rem 0.65rem;
    border-radius: 50rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.audit-type-login {
    background-color: rgba(52, 152, 219, 0.15);
    color: #3498db;
}

.audit-type-report {
    background-color: rgba(46, 204, 113, 0.15);
    color: #2ecc71;
}

.audit-type-note {
    background-color: rgba(155, 89, 182, 0.15);
    color: #9b59b6;
}

.audit-type-punishment {
    background-color: rgba(231, 76, 60, 0.15);
    color: #e74c3c;
}

.audit-type-punishment_removal {
    background-color: rgba(231, 76, 60, 0.15);
    color: #e74c3c;
}

.audit-type-appeal {
    background-color: rgba(243, 156, 18, 0.15);
    color: #f39c12;
}

.audit-type-settings {
    background-color: rgba(52, 73, 94, 0.15);
    color: #34495e;
}

.audit-type-unknown {
    background-color: rgba(189, 195, 199, 0.15);
    color: #bdc3c7;
}

/* Staff Info */
.staff-info {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.staff-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    margin-right: 0.75rem;
}

.staff-info .staff-name {
    font-weight: 600;
    margin-bottom: 0;
}

/* Audit Action */
.audit-action {
    font-weight: 500;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    color: var(--text-dark);
}

/* Audit Target */
.audit-target {
    margin-bottom: 0.75rem;
    padding: 0.5rem 0.75rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    border-left: 3px solid #3498db;
}

/* Audit Details */
.audit-details {
    max-width: 100%;
    white-space: normal;
    word-break: break-word;
    background-color: rgba(0, 0, 0, 0.03);
    padding: 0.75rem;
    border-radius: var(--border-radius-sm);
    border-left: 3px solid #ccc;
    font-family: monospace;
    font-size: 0.85rem;
    margin-top: 0.75rem;
    max-height: 150px;
    overflow-y: auto;
    line-height: 1.5;
}

/* Audit Timestamp */
.audit-timestamp {
    white-space: nowrap;
    font-size: 0.875rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
}

.audit-timestamp i {
    margin-right: 0.5rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    margin: 1.5rem 0;
    border: 1px solid var(--border-color);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3.5rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    opacity: 0.3;
}

.empty-state h5 {
    margin-bottom: 0.75rem;
    color: var(--text-dark);
    font-weight: 600;
}

.empty-state p {
    color: var(--text-muted);
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 3rem 1.5rem;
}

/* Pagination */
.audit-pagination {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
}

/* Dark Mode Styles */
body.dark-mode .audit-card {
    background-color: var(--secondary-color);
}

body.dark-mode .audit-card .card-header {
    border-bottom-color: var(--border-color-dark);
}

body.dark-mode .audit-card .card-footer {
    background-color: rgba(255, 255, 255, 0.03);
    border-top-color: var(--border-color-dark);
}

body.dark-mode .audit-action {
    color: var(--text-light);
}

body.dark-mode .audit-target {
    background-color: rgba(255, 255, 255, 0.05);
    border-left-color: #3498db;
}

body.dark-mode .audit-details {
    background-color: rgba(255, 255, 255, 0.05);
    border-left-color: #555;
}

body.dark-mode .empty-state {
    background-color: var(--bg-darker);
    border-color: var(--border-color-dark);
}

body.dark-mode .empty-state h5 {
    color: var(--text-light);
}

/* Mobile Responsiveness */
@media (max-width: 767.98px) {
    .audit-card-container {
        grid-template-columns: 1fr;
    }

    .audit-card {
        margin-bottom: 1rem;
    }

    .audit-card .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .audit-card .card-header .audit-timestamp {
        margin-top: 0.5rem;
    }

    .audit-filters .row > div {
        margin-bottom: 1rem;
    }

    .audit-details {
        max-height: 120px;
    }
}
