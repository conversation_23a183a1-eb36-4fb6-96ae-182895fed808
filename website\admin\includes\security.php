<?php
/**
 * Security Module
 * Implements security headers, input validation, and other security measures
 */


require_once __DIR__ . '/config.php';

/**
 * Set security headers for all responses
 */
function set_security_headers() {

    if (CSP_ENABLED) {
        // Generate a nonce for inline scripts
        $nonce = bin2hex(random_bytes(16));

        $csp = "default-src 'self'; " .
               "script-src 'self' 'nonce-$nonce' https://browser.sentry-cdn.com https://code.jquery.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://js-de.sentry-cdn.com https://cdn.datatables.net https://static.cloudflareinsights.com; " .
               "style-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://cdn.datatables.net 'nonce-$nonce'; " .
               "img-src 'self' https://cdn.discordapp.com https://api.mineatar.io data: blob:; " .
               "font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
               "connect-src 'self' https://sentry.io https://*.ingest.sentry.io https://o4508487756480512.ingest.de.sentry.io https://cloudflareinsights.com https://minecraftpocket-servers.com https://cloud.umami.is; " .
               "worker-src 'self' blob:; " .
               "frame-src 'none'; " .
               "object-src 'none'; " .
               "base-uri 'self'; " .
               "form-action 'self'; " .
               "upgrade-insecure-requests; " .
               "block-all-mixed-content;";

        if (CSP_REPORT_ONLY) {
            header("Content-Security-Policy-Report-Only: $csp");
        } else {
            header("Content-Security-Policy: $csp");
        }

        // Return the nonce for use in inline scripts
        return $nonce;
    }


    header("X-Content-Type-Options: nosniff");
    header("X-Frame-Options: DENY");
    header("X-XSS-Protection: 1; mode=block");
    header("Referrer-Policy: strict-origin-when-cross-origin");
    header("Permissions-Policy: geolocation=(), microphone=(), camera=()");


    if (getenv('APP_ENV') === 'production' && (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off')) {
        header("Strict-Transport-Security: max-age=31536000; includeSubDomains; preload");
    }
}

/**
 * Sanitize input to prevent XSS
 *
 * @param string $input Input to sanitize
 * @return string Sanitized input
 */
function sanitize_input($input) {
    if (is_array($input)) {
        $sanitized = [];
        foreach ($input as $key => $value) {
            $sanitized[$key] = sanitize_input($value);
        }
        return $sanitized;
    }
    return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
}

/**
 * Sanitize output for HTML display
 *
 * @param string $output Output to sanitize
 * @return string Sanitized output
 */
function sanitize_output($output) {
    if (is_array($output)) {
        $sanitized = [];
        foreach ($output as $key => $value) {
            $sanitized[$key] = sanitize_output($value);
        }
        return $sanitized;
    }
    return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
}

/**
 * Validate and sanitize input based on type
 *
 * @param mixed $input Input to validate
 * @param string $type Type of validation (email, username, text, number, date, etc.)
 * @param array $options Additional validation options
 * @return array Validation result with 'valid' and 'value' keys
 */
function validate_input($input, $type, $options = []) {
    $result = [
        'valid' => false,
        'value' => null,
        'error' => null
    ];


    if (is_string($input)) {
        $input = trim($input);
    }


    if (isset($options['required']) && $options['required'] && (is_null($input) || $input === '')) {
        $result['error'] = 'This field is required';
        return $result;
    }


    if ((is_null($input) || $input === '') && (!isset($options['required']) || !$options['required'])) {
        $result['valid'] = true;
        return $result;
    }


    switch ($type) {
        case 'email':
            if (!filter_var($input, FILTER_VALIDATE_EMAIL)) {
                $result['error'] = 'Invalid email format';
                return $result;
            }
            $result['value'] = filter_var($input, FILTER_SANITIZE_EMAIL);
            break;

        case 'username':

            if (!preg_match('/^[a-zA-Z0-9_-]{3,32}$/', $input)) {
                $result['error'] = 'Username must be 3-32 characters and contain only letters, numbers, underscores, and hyphens';
                return $result;
            }
            $result['value'] = $input;
            break;

        case 'text':

            $min_length = $options['min_length'] ?? 0;
            $max_length = $options['max_length'] ?? 1000;

            if (strlen($input) < $min_length) {
                $result['error'] = "Text must be at least $min_length characters";
                return $result;
            }

            if (strlen($input) > $max_length) {
                $result['error'] = "Text cannot exceed $max_length characters";
                return $result;
            }

            $result['value'] = sanitize_input($input);
            break;

        case 'number':

            if (!is_numeric($input)) {
                $result['error'] = 'Value must be a number';
                return $result;
            }

            $min = $options['min'] ?? null;
            $max = $options['max'] ?? null;

            if ($min !== null && $input < $min) {
                $result['error'] = "Value must be at least $min";
                return $result;
            }

            if ($max !== null && $input > $max) {
                $result['error'] = "Value cannot exceed $max";
                return $result;
            }

            $result['value'] = is_int($input) ? intval($input) : floatval($input);
            break;

        case 'date':

            $date = date_create($input);
            if (!$date) {
                $result['error'] = 'Invalid date format';
                return $result;
            }

            $min_date = isset($options['min_date']) ? date_create($options['min_date']) : null;
            $max_date = isset($options['max_date']) ? date_create($options['max_date']) : null;

            if ($min_date && $date < $min_date) {
                $result['error'] = 'Date is before minimum allowed date';
                return $result;
            }

            if ($max_date && $date > $max_date) {
                $result['error'] = 'Date is after maximum allowed date';
                return $result;
            }

            $result['value'] = date_format($date, 'Y-m-d');
            break;

        case 'boolean':

            if (is_bool($input)) {
                $result['value'] = $input;
            } elseif (is_string($input)) {
                $lower = strtolower($input);
                if (in_array($lower, ['true', '1', 'yes', 'y', 'on'])) {
                    $result['value'] = true;
                } elseif (in_array($lower, ['false', '0', 'no', 'n', 'off'])) {
                    $result['value'] = false;
                } else {
                    $result['error'] = 'Invalid boolean value';
                    return $result;
                }
            } elseif (is_numeric($input)) {
                $result['value'] = (bool)$input;
            } else {
                $result['error'] = 'Invalid boolean value';
                return $result;
            }
            break;

        case 'url':

            if (!filter_var($input, FILTER_VALIDATE_URL)) {
                $result['error'] = 'Invalid URL format';
                return $result;
            }
            $result['value'] = filter_var($input, FILTER_SANITIZE_URL);
            break;

        case 'discord_id':

            if (!preg_match('/^\d{17,19}$/', $input)) {
                $result['error'] = 'Invalid Discord ID format';
                return $result;
            }
            $result['value'] = $input;
            break;

        case 'enum':

            $allowed = $options['allowed'] ?? [];
            if (!in_array($input, $allowed)) {
                $result['error'] = 'Value is not in the list of allowed values';
                return $result;
            }
            $result['value'] = $input;
            break;

        case 'array':

            if (!is_array($input)) {
                $result['error'] = 'Value must be an array';
                return $result;
            }

            $min_items = $options['min_items'] ?? null;
            $max_items = $options['max_items'] ?? null;

            if ($min_items !== null && count($input) < $min_items) {
                $result['error'] = "Array must contain at least $min_items items";
                return $result;
            }

            if ($max_items !== null && count($input) > $max_items) {
                $result['error'] = "Array cannot contain more than $max_items items";
                return $result;
            }


            if (isset($options['item_type'])) {
                $validated_array = [];
                foreach ($input as $key => $value) {
                    $item_result = validate_input($value, $options['item_type'], $options['item_options'] ?? []);
                    if (!$item_result['valid']) {
                        $result['error'] = "Invalid item at index $key: " . $item_result['error'];
                        return $result;
                    }
                    $validated_array[$key] = $item_result['value'];
                }
                $result['value'] = $validated_array;
            } else {
                $result['value'] = $input;
            }
            break;

        default:

            if (is_string($input)) {
                $result['value'] = sanitize_input($input);
            } else {
                $result['value'] = $input;
            }
            break;
    }

    $result['valid'] = true;
    return $result;
}

/**
 * Generate a new CSRF token
 *
 * @return string CSRF token
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_tokens'])) {
        $_SESSION['csrf_tokens'] = [];
    }


    $token = bin2hex(random_bytes(32));


    $_SESSION['csrf_tokens'][$token] = time();


    $expiry = time() - 7200; // 2 hours
    foreach ($_SESSION['csrf_tokens'] as $t => $timestamp) {
        if ($timestamp < $expiry) {
            unset($_SESSION['csrf_tokens'][$t]);
        }
    }

    return $token;
}

/**
 * Verify a CSRF token
 *
 * @param string $token Token to verify
 * @return bool Whether token is valid
 */
function verify_csrf_token($token) {
    if (!isset($_SESSION['csrf_tokens']) || !isset($_SESSION['csrf_tokens'][$token])) {
        return false;
    }


    unset($_SESSION['csrf_tokens'][$token]);

    return true;
}

/**
 * Get CSRF token from request
 *
 * @return string|null CSRF token or null if not found
 */
function get_csrf_token_from_request() {

    if (isset($_POST['csrf_token'])) {
        return $_POST['csrf_token'];
    }


    $headers = getallheaders();


    foreach ($headers as $key => $value) {
        if (strtolower($key) === 'x-csrf-token') {
            return $value;
        }
    }

    return null;
}

/**
 * Generate HTML for CSRF token input field
 *
 * @return string HTML input field
 */
function csrf_token_input() {
    $token = generate_csrf_token();
    return '<input type="hidden" name="csrf_token" value="' . $token . '">';
}

/**
 * Add CSRF token to URL
 *
 * @param string $url URL to add token to
 * @return string URL with token
 */
function add_csrf_token_to_url($url) {
    $token = generate_csrf_token();
    $separator = (strpos($url, '?') !== false) ? '&' : '?';
    return $url . $separator . 'csrf_token=' . $token;
}

/**
 * Get CSRF token for AJAX requests
 *
 * @return string CSRF token
 */
function get_csrf_token_for_ajax() {
    return generate_csrf_token();
}

/**
 * Require CSRF token for non-GET requests
 *
 * @param bool $api_mode Whether to return JSON error for API requests
 * @param bool $strict Whether to enforce CSRF check for all request types (including GET)
 * @return bool Whether CSRF check passed
 */
function require_csrf_check($api_mode = false, $strict = false) {
    // Skip CSRF check for GET requests unless strict mode is enabled
    if ($_SERVER['REQUEST_METHOD'] === 'GET' && !$strict) {
        return true;
    }

    // Always enforce CSRF checks in all environments
    $token = get_csrf_token_from_request();

    if (!$token) {
        secure_log("CSRF token missing in " . $_SERVER['REQUEST_METHOD'] . " request to " . $_SERVER['REQUEST_URI'], "error");

        if ($api_mode) {
            header('Content-Type: application/json');
            http_response_code(403);
            echo json_encode(['error' => 'CSRF token missing', 'status' => 'error']);
            exit;
        } else {
            http_response_code(403);
            echo '<h1>403 Forbidden</h1><p>CSRF token missing</p>';
            exit;
        }
    }

    if (!verify_csrf_token($token)) {
        secure_log("CSRF token validation failed for " . $_SERVER['REQUEST_METHOD'] . " request to " . $_SERVER['REQUEST_URI'], "error");

        if ($api_mode) {
            header('Content-Type: application/json');
            http_response_code(403);
            echo json_encode(['error' => 'CSRF token invalid', 'status' => 'error']);
            exit;
        } else {
            http_response_code(403);
            echo '<h1>403 Forbidden</h1><p>CSRF token invalid</p>';
            exit;
        }
    }

    return true;
}

/**
 * Rate limiting for API requests
 *
 * @param string $key Unique key for rate limit (e.g., IP address, user ID)
 * @param int $limit Maximum number of requests
 * @param int $window Time window in seconds
 * @return bool Whether request is allowed
 */
function check_rate_limit($key, $limit = API_RATE_LIMIT, $window = API_RATE_WINDOW) {

    if (function_exists('apcu_fetch')) {
        $count_key = "rate_limit:{$key}";
        $count = apcu_fetch($count_key);

        if ($count === false) {
            apcu_store($count_key, 1, $window);
            return true;
        }

        if ($count >= $limit) {
            return false;
        }

        apcu_inc($count_key);
        return true;
    }


    if (!isset($_SESSION['rate_limits'])) {
        $_SESSION['rate_limits'] = [];
    }

    $now = time();
    $rate_key = "rate:{$key}";

    if (!isset($_SESSION['rate_limits'][$rate_key])) {
        $_SESSION['rate_limits'][$rate_key] = [
            'count' => 1,
            'reset_time' => $now + $window
        ];
        return true;
    }

    $limit_data = &$_SESSION['rate_limits'][$rate_key];


    if ($now > $limit_data['reset_time']) {
        $limit_data['count'] = 1;
        $limit_data['reset_time'] = $now + $window;
        return true;
    }


    if ($limit_data['count'] >= $limit) {
        return false;
    }


    $limit_data['count']++;
    return true;
}

/**
 * Apply rate limiting to current request
 *
 * @param string $key Unique key for rate limit (e.g., IP address, user ID)
 * @param int $limit Maximum number of requests
 * @param int $window Time window in seconds
 */
function apply_rate_limit($key = null, $limit = API_RATE_LIMIT, $window = API_RATE_WINDOW) {
    if ($key === null) {

        $key = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    if (!check_rate_limit($key, $limit, $window)) {
        header('HTTP/1.1 429 Too Many Requests');
        header('Retry-After: ' . $window);

        if (strpos($_SERVER['REQUEST_URI'], '/adminapi/') !== false) {

            header('Content-Type: application/json');
            echo json_encode(['error' => 'Rate limit exceeded. Please try again later.']);
        } else {

            echo '<h1>429 Too Many Requests</h1>';
            echo '<p>You have made too many requests. Please try again later.</p>';
        }
        exit;
    }
}

/**
 * Secure logging function that sanitizes sensitive information
 *
 * @param string $message Log message
 * @param string $level Log level (error, warning, info, debug)
 * @param array $context Additional context data
 * @return void
 */
function secure_log($message, $level = 'info', $context = []) {

    $patterns = [

        '/(api[_-]?key|token|secret|password|pwd|auth)["\']?\s*[:=]\s*["\']?([^"\'\s,\{\}]+)/i' => '$1: [REDACTED]',

        '/(mongodb(?:\+srv)?:\/\/)[^@]+@/' => '$1[REDACTED]@',

        '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/' => '[EMAIL REDACTED]',

        '/\b(?:\d{1,3}\.){3}\d{1,3}\b/' => '[IP REDACTED]',

        '/\b\d{17,19}\b/' => '[DISCORD_ID]',

        '/\b(?:\d{4}[- ]?){3}\d{4}\b/' => '[CARD REDACTED]',

        '/\b(?:\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b/' => '[PHONE REDACTED]',

        '/\b\d{3}-\d{2}-\d{4}\b/' => '[SSN REDACTED]',
    ];


    $sanitized_message = preg_replace(array_keys($patterns), array_values($patterns), $message);


    $sanitized_context = [];
    foreach ($context as $key => $value) {
        if (is_string($value)) {
            $sanitized_context[$key] = preg_replace(array_keys($patterns), array_values($patterns), $value);
        } else {
            $sanitized_context[$key] = $value;
        }
    }


    return;
}


set_security_headers();
