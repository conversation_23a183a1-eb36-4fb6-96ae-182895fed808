==============================================
MongoDB\\Model\\CollectionInfo::getCappedMax()
==============================================

.. deprecated:: 1.9

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\CollectionInfo::getCappedMax()

   Return the document limit for the capped collection. This correlates with the
   ``max`` option for :phpmethod:`MongoDB\\Database::createCollection()`.

   .. code-block:: php

      function getCappedMax(): integer|null

Return Values
-------------

The document limit for the capped collection. If the collection is not capped,
``null`` will be returned.

This method is deprecated in favor of using
:phpmethod:`MongoDB\\Model\\CollectionInfo::getOptions()` and accessing the
``max`` key.

Examples
--------

.. code-block:: php

   <?php

   $info = new CollectionInfo([
       'name' => 'foo',
       'options' => [
           'capped' => true,
           'size' => 1048576,
           'max' => 100,
       ]
   ]);

   var_dump($info->getCappedMax());

The output would then resemble::

   int(100)

See Also
--------

- :phpmethod:`MongoDB\\Model\\CollectionInfo::getCappedSize()`
- :phpmethod:`MongoDB\\Model\\CollectionInfo::isCapped()`
- :phpmethod:`MongoDB\\Database::createCollection()`
- :manual:`Capped Collections </core/capped-collections>` in the MongoDB manual
- :manual:`listCollections </reference/command/listCollections>` command
  reference in the MongoDB manual
