/**
 * Punishment Types Management
 * Handles the predefined punishment types and their UI
 */


let predefinedPunishmentTypes = {};

/**
 * Initialize punishment types
 */
async function initPunishmentTypes() {
    try {

        const response = await fetch('/adminapi/punishments.php?predefined_types=1', {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                'X-Discord-ID': window.AUTH_DATA?.user_id || '',
                'X-Discord-Username': window.AUTH_DATA?.username || '',
                'X-Discord-Role': window.AUTH_DATA?.role || '',
                'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        predefinedPunishmentTypes = await response.json();


        setupPunishmentTypeUI();
    } catch (error) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to load predefined punishment types. Please refresh the page or contact an administrator.'
        });
    }
}

/**
 * Set up the punishment type UI
 */
function setupPunishmentTypeUI() {
    const punishmentTypeSelect = document.getElementById('punishmentTypeSelect');
    if (!punishmentTypeSelect) return;


    punishmentTypeSelect.innerHTML = '<option value="">Select a violation type...</option>';


    Object.entries(predefinedPunishmentTypes).forEach(([key, type]) => {
        const option = document.createElement('option');
        option.value = key;
        option.textContent = type.label;
        option.dataset.type = type.type; // Store the punishment type (ban/mute)
        punishmentTypeSelect.appendChild(option);
    });


    punishmentTypeSelect.addEventListener('change', handlePunishmentTypeChange);


    const submitBtn = document.getElementById('submitPunishment');
    if (submitBtn) {

        const newSubmitBtn = submitBtn.cloneNode(true);
        submitBtn.parentNode.replaceChild(newSubmitBtn, submitBtn);


        newSubmitBtn.addEventListener('click', function() {
            issuePunishmentWithType();
        });


        newSubmitBtn.disabled = false;
    }
}

/**
 * Handle punishment type selection change
 */
function handlePunishmentTypeChange(event) {
    const selectedType = event.target.value;
    const punishmentTypeInput = document.getElementById('punishmentType');
    const durationInput = document.getElementById('punishmentDuration');
    const durationUnitInput = document.getElementById('punishmentDurationUnit');

    if (!selectedType) return;

    const typeInfo = predefinedPunishmentTypes[selectedType];


    if (punishmentTypeInput) {
        punishmentTypeInput.value = typeInfo.type;
    }


    const firstOffenseDuration = typeInfo.durations[1];
    if (firstOffenseDuration && durationInput && durationUnitInput) {
        if (firstOffenseDuration === 'permanent') {
            durationInput.value = 'permanent';
            durationUnitInput.value = 'permanent';
        } else {

            const match = firstOffenseDuration.match(/^(\d+)([hdwm])$/);
            if (match) {
                const value = match[1];
                const unit = match[2];

                durationInput.value = value;


                const unitMap = {
                    'h': 'hours',
                    'd': 'days',
                    'w': 'weeks',
                    'm': 'months'
                };

                durationUnitInput.value = unitMap[unit] || 'days';
            }
        }
    }


    showOffenseProgression(selectedType);
}

/**
 * Show offense progression information
 */
function showOffenseProgression(offenseType) {
    const progressionInfo = document.getElementById('offenseProgressionInfo');
    if (!progressionInfo) return;

    const typeInfo = predefinedPunishmentTypes[offenseType];
    if (!typeInfo) return;


    function formatDuration(duration) {
        if (duration === 'permanent') return 'Permanent';

        const match = duration.match(/^(\d+)([hdwm]o?)$/);
        if (!match) return duration;

        const value = match[1];
        const unit = match[2];

        const unitMap = {
            'h': 'hour',
            'd': 'day',
            'w': 'week',
            'm': 'minute',
            'mo': 'month'
        };

        const unitName = unitMap[unit] || unit;
        return `${value} ${unitName}${value > 1 ? 's' : ''}`;
    }

    let progressionHtml = `<div class="mt-3 p-3 bg-secondary bg-opacity-10 rounded border">
        <h6 class="mb-2"><i class="fas fa-info-circle me-2"></i>Offense Progression for ${typeInfo.label}</h6>
        <p class="text-muted small mb-2">Our fair punishment system uses a graduated approach with multiple warnings before permanent actions.</p>
        <ul class="list-group list-group-flush">`;

    Object.entries(typeInfo.durations).forEach(([offenseNumber, duration]) => {
        const formattedDuration = formatDuration(duration);
        const badgeClass = duration === 'permanent' ? 'bg-danger' :
                          (offenseNumber > 3 ? 'bg-warning text-dark' : 'bg-primary');

        progressionHtml += `<li class="list-group-item d-flex justify-content-between align-items-center bg-transparent">
            <strong>Offense #${offenseNumber}</strong>
            <span class="badge ${badgeClass} rounded-pill">${formattedDuration} ${typeInfo.type}</span>
        </li>`;
    });

    progressionHtml += `</ul>
        <p class="text-muted small mt-2 mb-0">Note: Offense counts reset after 90 days of good behavior.</p>
        </div>`;

    progressionInfo.innerHTML = progressionHtml;
}

/**
 * Prepare punishment data for submission
 */
function preparePunishmentData() {
    const form = document.getElementById('punishmentForm');
    if (!form) return null;


    let playerName = '';
    const playerNameInput = document.getElementById('playerName');
    const playerNameHidden = document.getElementById('punishmentPlayerName');

    if (playerNameInput) {
        playerName = playerNameInput.value.trim();
    } else if (playerNameHidden) {
        playerName = playerNameHidden.value.trim();
    }


    const offenseTypeSelect = document.getElementById('punishmentTypeSelect');
    const offenseType = offenseTypeSelect ? offenseTypeSelect.value : '';


    if (!offenseType) {
        Swal.fire({
            icon: 'warning',
            title: 'Missing Information',
            text: 'Please select a violation type'
        });
        return null;
    }


    let punishmentType = '';
    const typeInput = document.getElementById('punishmentType');
    if (typeInput) {
        punishmentType = typeInput.value;
    }


    if (offenseType && predefinedPunishmentTypes[offenseType]) {
        punishmentType = predefinedPunishmentTypes[offenseType].type;
    }


    let duration = 'permanent';
    const durationInput = document.getElementById('punishmentDuration');
    const durationUnitInput = document.getElementById('punishmentDurationUnit');

    if (durationInput && durationUnitInput) {
        const durationValue = durationInput.value.trim();
        const durationUnit = durationUnitInput.value;

        if (durationUnit === 'permanent') {
            duration = 'permanent';
        } else {

            const unitMap = {
                'minutes': 'm',
                'hours': 'h',
                'days': 'd',
                'weeks': 'w',
                'months': 'mo'
            };

            const unitAbbr = unitMap[durationUnit] || durationUnit.charAt(0);
            duration = durationValue + unitAbbr;
        }
    }


    const evidence = document.getElementById('punishmentEvidence')?.value.trim() || '';
    const notes = document.getElementById('punishmentNotes')?.value.trim() || '';


    if (!playerName) {
        Swal.fire({
            icon: 'warning',
            title: 'Missing Information',
            text: 'Please enter a player name'
        });
        return null;
    }

    if (!punishmentType) {
        Swal.fire({
            icon: 'warning',
            title: 'Missing Information',
            text: 'Please select a punishment type'
        });
        return null;
    }


    const confirmCheckbox = document.getElementById('punishmentConfirm');
    if (confirmCheckbox && !confirmCheckbox.checked) {
        Swal.fire({
            icon: 'warning',
            title: 'Confirmation Required',
            text: 'Please confirm that this punishment is appropriate and follows server guidelines'
        });
        return null;
    }


    const playerXuid = document.getElementById('punishmentPlayerXuid')?.value || '';


    let reason = '';
    if (offenseType && predefinedPunishmentTypes[offenseType]) {
        reason = predefinedPunishmentTypes[offenseType].label;
    }


    const data = {
        player: playerName,
        reason: reason,
        duration: duration,
        offense_type: offenseType,
        public: true
    };


    if (playerXuid) {
        data.player_xuid = playerXuid;
    }


    if (evidence) {
        data.evidence = evidence;
    }

    if (notes) {
        data.staff_notes = notes;
    }

    return data;
}

/**
 * Issue a punishment with predefined type
 */
async function issuePunishmentWithType() {

    const submitBtn = document.getElementById('submitPunishment');


    if (submitBtn && submitBtn.disabled) {
        return;
    }


    if (submitBtn) {
        submitBtn.disabled = true;
    }


    const data = preparePunishmentData();
    if (!data) {

        if (submitBtn) {
            submitBtn.disabled = false;
        }
        return;
    }


    let punishmentType = '';


    const typeElements = document.getElementsByName('punishmentType');
    if (typeElements.length > 0) {
        for (const element of typeElements) {
            if (element.checked) {
                punishmentType = element.value;
                break;
            }
        }
    } else {

        const selectedTypeOption = document.querySelector('.punishment-type-option.selected');
        if (selectedTypeOption) {
            punishmentType = selectedTypeOption.dataset.type;
        }


        const typeSelect = document.getElementById('punishmentType');
        if (typeSelect && typeSelect.value) {
            punishmentType = typeSelect.value;
        }
    }


    if (data.offense_type && predefinedPunishmentTypes[data.offense_type]) {
        punishmentType = predefinedPunishmentTypes[data.offense_type].type;
    }

    try {

        Swal.fire({
            title: 'Processing...',
            text: 'Issuing punishment...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });


        const response = await fetch(`/adminapi/punishments.php/${punishmentType}s/add`, {
            method: 'POST',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                'X-Discord-ID': window.AUTH_DATA?.user_id || '',
                'X-Discord-Username': window.AUTH_DATA?.username || '',
                'X-Discord-Role': window.AUTH_DATA?.role || '',
                'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.ok && result.success) {

            const punishmentModal = bootstrap.Modal.getInstance(document.getElementById('punishmentModal'));
            if (punishmentModal) {
                punishmentModal.hide();
            }

            const addPunishmentModal = bootstrap.Modal.getInstance(document.getElementById('addPunishmentModal'));
            if (addPunishmentModal) {
                addPunishmentModal.hide();
            }


            Swal.fire({
                icon: 'success',
                title: 'Punishment Applied',
                text: result.message || 'The punishment has been successfully applied',
                confirmButtonText: 'OK'
            }).then(() => {

                document.getElementById('punishmentForm').reset();


                const progressionInfo = document.getElementById('offenseProgressionInfo');
                if (progressionInfo) {
                    progressionInfo.innerHTML = '';
                }


                const punishmentTypeSelect = document.getElementById('punishmentTypeSelect');
                if (punishmentTypeSelect) {
                    punishmentTypeSelect.value = '';
                }


                const typeOptions = document.querySelectorAll('.punishment-type-option');
                typeOptions.forEach(option => option.classList.remove('selected'));

                const durationOptions = document.querySelectorAll('.duration-option');
                durationOptions.forEach(option => option.classList.remove('selected'));


                if (typeof loadPunishments === 'function') {
                    loadPunishments();
                }


                if (typeof loadPlayerData === 'function') {
                    loadPlayerData();
                } else {

                    if (window.location.pathname.includes('playerinfo.php')) {
                        const searchInput = document.getElementById('searchInput');
                        if (searchInput && searchInput.value) {
                            performSearch();
                        }
                    }
                }
            });
        } else {
            throw new Error(result.message || 'Failed to issue punishment');
        }
    } catch (error) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: error.message || 'An error occurred while issuing the punishment'
        });


        const submitBtn = document.getElementById('submitPunishment');
        if (submitBtn) {
            submitBtn.disabled = false;
        }


        document.querySelectorAll('#submitPunishment').forEach(btn => {
            btn.disabled = false;
        });
    }
}


document.addEventListener('DOMContentLoaded', initPunishmentTypes);
