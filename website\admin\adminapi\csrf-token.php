<?php
/**
 * CSRF Token API Endpoint
 * 
 * Provides fresh CSRF tokens for AJAX requests
 */

// Set content type
header('Content-Type: application/json');

// Include required files
require_once '../includes/config.php';
require_once '../includes/auth.php';
require_once '../includes/security.php';

// Start session
session_start();

try {
    // Check if user is authenticated
    if (!is_authenticated()) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }

    // Generate and return fresh CSRF token
    $token = generate_csrf_token();
    
    echo json_encode([
        'token' => $token,
        'status' => 'success'
    ]);

} catch (Exception $e) {
    error_log("CSRF Token API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
