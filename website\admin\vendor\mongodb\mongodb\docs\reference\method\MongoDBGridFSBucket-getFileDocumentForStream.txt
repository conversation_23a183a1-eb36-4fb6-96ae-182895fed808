===================================================
MongoDB\\GridFS\\Bucket::getFileDocumentForStream()
===================================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::getFileDocumentForStream()

   Gets the file document of the GridFS file associated with a stream.

   .. code-block:: php

      function getFileDocumentForStream($stream): array|object

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-getFileDocumentForStream-param.rst

Return Values
-------------

The metadata document associated with the GridFS stream. The return type will
depend on the bucket's ``typeMap`` option.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $stream = $bucket->openUploadStream('filename');

   $fileDocument = $bucket->getFileDocumentForStream($stream);

   var_dump($fileDocument);

   fclose($stream);

The output would then resemble::

   object(MongoDB\Model\BSONDocument)#4956 (1) {
     ["storage":"ArrayObject":private]=>
     array(3) {
       ["_id"]=>
       object(MongoDB\BSON\ObjectId)#4955 (1) {
         ["oid"]=>
         string(24) "5acfb05b7e21e83b5a29037c"
       }
       ["chunkSize"]=>
       int(261120)
       ["filename"]=>
       string(8) "filename"
     }
   }

See Also
--------

- :phpmethod:`MongoDB\\GridFS\\Bucket::getFileIdForStream()`
