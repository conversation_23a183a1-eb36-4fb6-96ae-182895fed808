# HTTP -> HTTPS Redirect
<VirtualHost *:80>
    ServerName admin.massacremc.net
    DocumentRoot /var/www/html/Massacre-Website/admin
    RewriteEngine On
    RewriteRule ^ https://%{SERVER_NAME}%{REQUEST_URI} [END,NE,R=permanent]
</VirtualHost>

# Main HTTPS Configuration
<VirtualHost *:443>
    ServerName admin.massacremc.net
    DocumentRoot "/var/www/html/Massacre-Website/admin"
    ServerAdmin <EMAIL>

    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/massacremc.net.pem
    SSLCertificateKeyFile /etc/ssl/certs/massacremc.net.key
    SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
    SSLHonorCipherOrder off
    SSLSessionTickets off

    # Global Settings
    RewriteEngine On
    ProxyPreserveHost On
    ProxyRequests Off

php_value session.gc_maxlifetime 86400
    
    # Set permissions for session directory
    <Directory "/var/lib/php/sessions">
     Require all granted
    </Directory>

    # Error Reporting for Debugging
    php_flag display_errors Off
    php_flag display_startup_errors Off
    php_value error_reporting E_ALL

    # Security Headers
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "DENY"
    Header set X-XSS-Protection "1; mode=block"
    Header set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # CORS Headers
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Authorization, Content-Type"
    Header always set Access-Control-Allow-Credentials "true"

<Directory "/var/www/html/Massacre-Website/admin">
    Options +FollowSymLinks
    AllowOverride All
    Require all granted
    
    # Enable rewrite debugging in Apache error log
    #LogLevel warn rewrite:trace3
</Directory>

<Directory "/var/www/html/Massacre-Website/admin/dashboard">
    Options +FollowSymLinks
    AllowOverride All
    Require all granted
    DirectoryIndex reports.php
</Directory>

<FilesMatch "\.(js|css)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</FilesMatch>

    # Static Files Directory
    <Directory "/var/www/html/Massacre-Website/admin/js">
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        <FilesMatch "\.(js|css)$">
        Header set Cache-Control "no-cache, must-revalidate, max-age=0"
        </FilesMatch>
    </Directory>

<Directory "/var/www/html/Massacre-Website/admin/auth">
    Options +FollowSymLinks
    AllowOverride All
    Require all granted
</Directory>

<FilesMatch "\.env$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Deny access to all Python files
<FilesMatch "\.py$">
    Order deny,allow
    Deny from all
</FilesMatch>

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle /adminapi/appeals/{id}/approve|deny
    RewriteRule ^adminapi/appeals/([^/]+)/(approve|deny)$ adminapi/appeals.php [L,QSA]
    
    # Handle /adminapi/reports/{id}/accept|reject
    RewriteRule ^adminapi/reports/([^/]+)/(accept|reject)$ adminapi/reports.php [L,QSA]
    
    # Handle /adminapi/punishments/type/add and /adminapi/punishments/type/{id}/remove
    RewriteRule ^adminapi/punishments/([^/]+)/add$ adminapi/punishments.php [L,QSA]
    RewriteRule ^adminapi/punishments/([^/]+)/([^/]+)/remove$ adminapi/punishments.php [L,QSA]
    
    # Handle simple endpoints
    RewriteRule ^adminapi/appeals$ adminapi/appeals.php [L,QSA]
    RewriteRule ^adminapi/reports$ adminapi/reports.php [L,QSA]
    RewriteRule ^adminapi/search$ adminapi/search.php [L,QSA]
    RewriteRule ^adminapi/punishments$ adminapi/punishments.php [L,QSA]
    RewriteRule ^adminapi/dashboard/stats$ adminapi/dashboard/stats.php [L,QSA]
    RewriteRule ^adminapi/dashboard/staff/online$ adminapi/dashboard/staff/online.php [L,QSA]
</IfModule>

# Deny access to sensitive files by name
<FilesMatch "^(app|admin)\.py$">
    Order deny,allow
    Deny from all
</FilesMatch>

    # Logging - Reduced SSL logging for production
    #ErrorLog ${APACHE_LOG_DIR}/admin_error.log
    #CustomLog ${APACHE_LOG_DIR}/admin_access.log combined
    LogLevel warn

    # Reduce SSL module logging specifically
    LogLevel ssl:warn
    LogLevel ssl_module:warn
</VirtualHost>
