<?php
/**
 * Search API Endpoint
 * Handles player and faction searches
 */


header('Content-Type: application/json');


require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/db_access.php';
require_once __DIR__ . '/../includes/auth.php';


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


apply_rate_limit($_SERVER['REMOTE_ADDR'], 60, 60); // 60 requests per minute

try {



    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? null;
        if (!$token || !verify_csrf_token($token)) {
            error_log("Search API: CSRF token validation failed");
            http_response_code(403);
            echo json_encode(['error' => 'CSRF token invalid or missing']);
            exit;
        }
    }


    if (!is_authenticated()) {
        $reason = !isset($_SESSION['discord_user_id']) ? "discord_user_id not set" :
                 (empty($_SESSION['discord_user_id']) ? "discord_user_id empty" :
                 (session_expired() ? "session expired" : "unknown reason"));


        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }


        $headers = getallheaders();
        if (isset($headers['X-Discord-ID']) && !empty($headers['X-Discord-ID'])) {

            $_SESSION['discord_user_id'] = $headers['X-Discord-ID'];
            $_SESSION['discord_username'] = $headers['X-Discord-Username'] ?? 'Unknown User';
            $_SESSION['discord_user_role'] = $headers['X-Discord-Role'] ?? ROLE_TRAINEE;
            $_SESSION['auth_time'] = time();
        } else {
            http_response_code(401);
            echo json_encode(['error' => 'Not authenticated', 'reason' => $reason]);
            exit;
        }
    }


    if (!has_role(ROLE_TRAINEE)) {
        error_log("Search API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied. You do not have the required role to access search functionality.']);
        exit;
    }


    refresh_session();


    $headers = getallheaders();
    update_staff_activity(
        $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
        $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
        $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
        $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
    );


    if ($_SERVER['REQUEST_METHOD'] === 'GET') {

        $query = isset($_GET['query']) ? sanitize_input($_GET['query']) : null;
        $type = isset($_GET['type']) ? sanitize_input($_GET['type']) : 'player';


        if (empty($query)) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing search query']);
            exit;
        }

        if (!in_array($type, ['player', 'faction'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid search type']);
            exit;
        }


        $result = search_info($query, $type);


        if ($result) {
            echo json_encode($result);
        } else {
            http_response_code(404);
            echo json_encode(['error' => "No {$type} found with that name"]);
        }
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    // Log the error securely without exposing sensitive information
    secure_log("Search API Error", "error", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);

    // Always return a generic error message in production
    http_response_code(500);
    echo json_encode([
        'error' => 'An internal server error occurred',
        'status' => 'error'
    ]);
}
?>