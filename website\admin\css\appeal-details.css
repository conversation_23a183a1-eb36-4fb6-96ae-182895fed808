/* Appeal Details Styling */

/* Appeal Details Panel */
#appealDetails {
    position: fixed;
    top: 0;
    right: -500px;
    width: 500px;
    max-width: 100%;
    height: 100vh;
    background-color: #fff;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    overflow-y: auto;
    transition: right 0.3s ease;
    padding: 0;
}

#appealDetails.active {
    right: 0;
}

/* Close button hover effect */
.btn-close:hover {
    opacity: 1;
}

/* Status banner */
.status-banner {
    border-radius: 6px;
    font-weight: 500;
}

.bg-warning-subtle {
    background-color: rgba(255, 193, 7, 0.15);
}

.bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.15);
}

.bg-danger-subtle {
    background-color: rgba(220, 53, 69, 0.15);
}

/* Info boxes */
.info-box {
    background-color: #fff;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.info-box:hover {
    box-shadow: 0 3px 8px rgba(0,0,0,0.1);
}

/* Player name */
.player-name {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: -0.5px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

/* Timeline list */
.timeline-list li {
    padding: 6px 0;
    border-bottom: 1px dashed #e9ecef;
}

.timeline-list li:last-child {
    border-bottom: none;
}

/* Appeal reason box */
.reason-box {
    max-height: 300px;
    overflow-y: auto;
    background-color: #f8f9fa;
}

.reason-content {
    white-space: pre-wrap;
    word-break: break-word;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 0.9rem;
}

/* Status badges */
.badge {
    font-weight: 500;
    padding: 0.5em 0.8em;
    letter-spacing: normal;
}

/* Resolution box */
.appeal-resolution {
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* Action buttons */
.appeal-actions .btn {
    min-width: 150px;
    transition: all 0.2s;
}

.appeal-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Punishment details */
.punishment-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.punishment-details h5 {
    color: #495057;
    margin-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 8px;
}

.punishment-details .badge {
    margin-left: 5px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #appealDetails {
        width: 100%;
        right: -100%;
    }
    
    .appeal-actions {
        flex-direction: column;
        gap: 10px !important;
    }
    
    .appeal-actions .btn {
        width: 100%;
    }
}
