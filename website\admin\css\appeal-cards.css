/* Appeal Cards Styling */

/* Card styling */
.appeal-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.appeal-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.appeal-card .card-header {
    background-color: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1rem;
}

.appeal-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.appeal-card .card-body {
    padding: 1.25rem;
}

.appeal-card .card-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1rem;
}

/* Badge styling */
.appeal-card .badge {
    font-weight: 500;
    padding: 0.5em 0.7em;
    font-size: 0.75rem;
}

/* Filter controls */
.filter-controls {
    margin-bottom: 1.5rem;
}

.filter-controls .form-select,
.filter-controls .form-control {
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
}

.filter-controls .input-group-text {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

/* Load more button */
#loadMoreBtn {
    transition: all 0.2s ease;
}

#loadMoreBtn:hover {
    transform: translateY(-2px);
}

/* Empty state */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
}

.empty-state i {
    font-size: 3rem;
    color: #adb5bd;
    margin-bottom: 1rem;
}

.empty-state h5 {
    font-weight: 500;
    color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .appeal-card {
        margin-bottom: 1rem;
    }
    
    .filter-controls .col-md-4,
    .filter-controls .col-md-3,
    .filter-controls .col-md-2 {
        margin-bottom: 0.75rem;
    }
    
    .filter-controls .col-md-2 {
        text-align: center !important;
    }
    
    #refreshAppeals {
        width: 100%;
    }
}

/* Dark mode support */
body.dark-mode .appeal-card {
    background-color: #2a2a2a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

body.dark-mode .appeal-card .card-header {
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

body.dark-mode .appeal-card .card-title {
    color: #e9ecef;
}

body.dark-mode .appeal-card .card-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

body.dark-mode .filter-controls .form-select,
body.dark-mode .filter-controls .form-control {
    background-color: #2a2a2a;
    border-color: #444;
    color: #e9ecef;
}

body.dark-mode .filter-controls .input-group-text {
    background-color: #343a40;
    border-color: #444;
    color: #e9ecef;
}

/* Animation for new cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 20px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.appeal-card {
    animation: fadeInUp 0.4s ease-out forwards;
}

/* Staggered animation for multiple cards */
.col-md-6:nth-child(1) .appeal-card { animation-delay: 0.05s; }
.col-md-6:nth-child(2) .appeal-card { animation-delay: 0.1s; }
.col-md-6:nth-child(3) .appeal-card { animation-delay: 0.15s; }
.col-md-6:nth-child(4) .appeal-card { animation-delay: 0.2s; }
.col-md-6:nth-child(5) .appeal-card { animation-delay: 0.25s; }
.col-md-6:nth-child(6) .appeal-card { animation-delay: 0.3s; }
.col-md-6:nth-child(7) .appeal-card { animation-delay: 0.35s; }
.col-md-6:nth-child(8) .appeal-card { animation-delay: 0.4s; }
.col-md-6:nth-child(9) .appeal-card { animation-delay: 0.45s; }
