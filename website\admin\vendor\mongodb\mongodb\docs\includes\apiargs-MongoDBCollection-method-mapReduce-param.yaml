arg_name: param
name: $map
type: :php:`MongoDB\\BSON\\Javascript <mongodb-bson-javascript>`
description: |
  A JavaScript function that associates or "maps" a value with a key and emits
  the key and value pair.

  .. note::

     Passing a Javascript instance with a scope is deprecated. Put all scope
     variables in the ``scope`` option of the MapReduce operation.
interface: phpmethod
operation: ~
optional: false
---
arg_name: param
name: $reduce
type: :php:`MongoDB\\BSON\\Javascript <class.mongodb-bson-javascript>`
description: |
  A JavaScript function that "reduces" to a single object all the values
  associated with a particular key.

  .. note::

     Passing a Javascript instance with a scope is deprecated. Put all scope
     variables in the ``scope`` option of the MapReduce operation.
interface: phpmethod
operation: ~
optional: false
---
arg_name: param
name: $out
type: string|array|object
description: |
  Specifies where to output the result of the map-reduce operation. You can
  either output to a collection or return the result inline. On a primary member
  of a replica set you can output either to a collection or inline, but on a
  secondary, only inline output is possible.
interface: phpmethod
operation: ~
optional: false
---
source:
  file: apiargs-common-param.yaml
  ref: $options
...
