/* Dark mode fixes for punishment details UI */

/* Fix for punishment details in dark mode */
body.dark-mode .punishment-details .card-header {
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--text-light);
    border-bottom-color: var(--border-color-dark);
}

body.dark-mode .punishment-details .card-body {
    background-color: var(--secondary-color);
    color: var(--text-light);
}

/* Fix for modal content in dark mode */
body.dark-mode #punishmentDetailsModal .modal-content {
    background-color: var(--bg-dark);
    color: var(--text-light);
}

/* Fix for modal body in dark mode */
body.dark-mode #punishmentDetailsModal .modal-body {
    background-color: var(--bg-dark);
    color: var(--text-light);
}

/* Fix for rounded sections in punishment details */
body.dark-mode .punishment-reason,
body.dark-mode .punishment-evidence,
body.dark-mode .punishment-notes {
    background-color: rgba(255, 255, 255, 0.05) !important;
    color: var(--text-light);
    border-color: var(--border-color-dark);
}

/* Fix for empty sections in punishment details */
body.dark-mode .card {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
}

/* Fix for specific sections in punishment details */
body.dark-mode #punishmentDetailsModal .punishment-evidence,
body.dark-mode #punishmentDetailsModal .punishment-reason,
body.dark-mode #punishmentDetailsModal .punishment-notes {
    background-color: rgba(30, 42, 56, 0.5) !important;
    color: var(--text-light);
    border: 1px solid var(--border-color-dark);
}

/* Fix for text colors in punishment details */
body.dark-mode .punishment-details strong {
    color: var(--text-light);
}

body.dark-mode .punishment-details .text-muted {
    color: var(--text-muted) !important;
}

/* Fix for links in punishment details */
body.dark-mode .punishment-details a {
    color: var(--accent-color);
}

body.dark-mode .punishment-details a:hover {
    color: var(--accent-hover);
}

/* Fix for badges in punishment details */
body.dark-mode .punishment-details .badge.bg-light {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: var(--text-light) !important;
}

/* Fix for modal header and footer in dark mode */
body.dark-mode #punishmentDetailsModal .modal-header,
body.dark-mode #punishmentDetailsModal .modal-footer {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
    color: var(--text-light);
}

/* Fix for modal backdrop in dark mode */
body.dark-mode .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
}

body.dark-mode #punishmentDetailsModal .modal-title {
    color: var(--text-light);
}

/* Fix for remove button in dark mode */
body.dark-mode #removePunishmentBtn {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--text-light);
}

body.dark-mode #removePunishmentBtn:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}
