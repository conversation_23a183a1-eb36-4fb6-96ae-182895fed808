arg_name: option
name: allowDiskUse
type: boolean
description: |
  Enables writing to temporary files. When set to ``true``, aggregation stages
  can write data to the ``_tmp`` sub-directory in the ``dbPath`` directory.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: batchSize
type: integer
description: |
  Specifies the batch size for the cursor, which will apply to both the initial
  ``aggregate`` command and any subsequent ``getMore`` commands. This determines
  the maximum number of documents to return in each response from the server.

  A batchSize of ``0`` is special in that and will only apply to the initial
  ``aggregate`` command; subsequent ``getMore`` commands will use the server's
  default batch size. This may be useful for quickly returning a cursor or
  failure from ``aggregate`` without doing significant server-side work.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: bypassDocumentValidation
post: |
  This only applies when using the :ref:`$out <agg-out>` and
  :ref:`$out <agg-merge>` stages.
---
arg_name: option
name: explain
type: boolean
description: |
  Specifies whether or not to return the information on the processing of the
  pipeline.
interface: phpmethod
operation: ~
optional: true
...
