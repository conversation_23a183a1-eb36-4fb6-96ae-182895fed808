/* Fix for punishment details modal in dark mode */

/* Ensure the modal content has dark background */
body.dark-mode #punishmentDetailsModal .modal-content {
    background-color: var(--bg-dark) !important;
    color: var(--text-light) !important;
    border-color: var(--border-color-dark) !important;
}

/* Fix for modal header in dark mode */
body.dark-mode #punishmentDetailsModal .modal-header {
    background-color: var(--secondary-color) !important;
    border-color: var(--border-color-dark) !important;
    color: var(--text-light) !important;
}

/* Fix for modal body in dark mode */
body.dark-mode #punishmentDetailsModal .modal-body {
    background-color: var(--bg-dark) !important;
    color: var(--text-light) !important;
}

/* Fix for modal footer in dark mode */
body.dark-mode #punishmentDetailsModal .modal-footer {
    background-color: var(--secondary-color) !important;
    border-color: var(--border-color-dark) !important;
}

/* Fix for close button in dark mode */
body.dark-mode #punishmentDetailsModal .btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Fix for punishment details content */
body.dark-mode #punishmentDetailsContent {
    background-color: var(--bg-dark) !important;
    color: var(--text-light) !important;
}

/* Fix for punishment details card */
body.dark-mode #punishmentDetailsContent .card {
    background-color: var(--secondary-color) !important;
    border-color: var(--border-color-dark) !important;
    color: var(--text-light) !important;
}

/* Fix for text colors */
body.dark-mode #punishmentDetailsContent .text-muted {
    color: var(--text-muted) !important;
}

/* Fix for punishment details sections */
body.dark-mode #punishmentDetailsContent .punishment-evidence,
body.dark-mode #punishmentDetailsContent .punishment-reason,
body.dark-mode #punishmentDetailsContent .punishment-notes {
    background-color: rgba(30, 42, 56, 0.5) !important;
    color: var(--text-light) !important;
    border: 1px solid var(--border-color-dark) !important;
}

/* Fix for buttons in dark mode */
body.dark-mode #punishmentDetailsModal .btn-secondary {
    background-color: var(--secondary-btn-color) !important;
    border-color: var(--secondary-btn-color) !important;
    color: var(--text-light) !important;
}

body.dark-mode #punishmentDetailsModal .btn-secondary:hover {
    background-color: var(--secondary-btn-hover) !important;
    border-color: var(--secondary-btn-hover) !important;
}

/* Fix for punishment details modal backdrop */
body.dark-mode .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7) !important;
}
