<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 


require_auth(ROLE_DEVELOPER); // DEVELOPER_ROLE or higher
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>Settings | MassacreMC Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Sentry error tracking -->
    <script src="https://js-de.sentry-cdn.com/9a6bbd71c1cbe403f1fd74ccad8cee64.min.js" crossorigin="anonymous"></script>
    <script>
window.AUTH_DATA = {
    authenticated: <?php echo is_authenticated() ? 'true' : 'false'; ?>,
    userId: "<?php echo htmlspecialchars($_SESSION['discord_user_id'] ?? ''); ?>",
    username: "<?php echo htmlspecialchars($_SESSION['discord_username'] ?? ''); ?>",
    role: "<?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? ''); ?>",
    hasRequiredRole: <?php echo has_role(ROLE_ADMIN) ? 'true' : 'false'; ?>,
    sessionId: "<?php echo session_id(); ?>"
};
</script>
    <link href="/css/admin-common.css" rel="stylesheet">
    <link href="/css/modern-theme.css" rel="stylesheet">
    <link href="/css/mobile.css" rel="stylesheet">
    <style>
        .settings-section {
            margin-bottom: 2rem;
        }

        .settings-section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .settings-section-title i {
            margin-right: 0.75rem;
            color: var(--accent-color);
        }

        .settings-card {
            background-color: var(--bg-card);
            border-radius: var(--border-radius-md);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            margin-bottom: 1rem;
        }

        .settings-item {
            margin-bottom: 1.5rem;
        }

        .settings-item:last-child {
            margin-bottom: 0;
        }

        .settings-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .settings-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
        }

        .settings-control {
            margin-top: 0.5rem;
        }

        .color-swatch {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: inline-block;
            margin-right: 0.5rem;
            border: 1px solid var(--border-color);
        }

        .nav-tabs .nav-link {
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            color: var(--text-secondary);
            border: none;
            border-bottom: 2px solid transparent;
        }

        .nav-tabs .nav-link.active {
            color: var(--accent-color);
            background-color: transparent;
            border-bottom: 2px solid var(--accent-color);
        }

        .nav-tabs .nav-link:hover:not(.active) {
            border-bottom: 2px solid var(--border-color);
        }

        .cursor-pointer {
            cursor: pointer;
        }

        .cursor-pointer:hover {
            opacity: 0.7;
        }

        .notes-content {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- Include standard navbar -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Include standard sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div id="content" class="main-content">
        <div class="page-container">
            <!-- Content Header -->
            <div class="content-header">
                <h1>Settings</h1>
                <p class="text-muted">Configure system settings and preferences</p>
            </div>

            <!-- Settings Navigation -->
            <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                        <i class="fas fa-cog me-2"></i> General
                    </button>
                </li>

                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
                        <i class="fas fa-shield-alt me-2"></i> Security
                    </button>
                </li>

                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="player-services-tab" data-bs-toggle="tab" data-bs-target="#player-services" type="button" role="tab" aria-controls="player-services" aria-selected="false">
                        <i class="fas fa-user-slash me-2"></i> Player Services
                    </button>
                </li>
            </ul>

            <!-- Settings Content -->
            <div class="tab-content" id="settingsTabContent">
                <!-- General Settings -->
                <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                    <div class="settings-section">
                        <div class="settings-section-title">
                            <i class="fas fa-server"></i> System Settings
                        </div>
                        <div class="settings-card">

                            <div class="settings-item">
                                <div class="settings-label">Maintenance Mode</div>
                                <div class="settings-description">When enabled, the admin panel will be accessible only to administrators.</div>
                                <div class="settings-control">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                        <label class="form-check-label" for="maintenanceMode">Enable maintenance mode</label>
                                    </div>

                                    <div id="maintenanceMessageContainer" class="mt-3" style="display: none;">
                                        <div class="mb-3">
                                            <label for="maintenanceMessage" class="form-label">Maintenance Message</label>
                                            <textarea class="form-control" id="maintenanceMessage" rows="3" placeholder="The system is currently undergoing maintenance. Please try again later."></textarea>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="notifyUsers">
                                            <label class="form-check-label" for="notifyUsers">
                                                Send notification to all users
                                            </label>
                                        </div>

                                        <div class="d-flex gap-2">
                                            <button class="btn btn-primary" id="saveMaintenanceBtn">
                                                <i class="fas fa-save me-2"></i> Save Maintenance Settings
                                            </button>
                                            <button class="btn btn-danger" id="resetMaintenanceBtn" title="Use this if maintenance mode is stuck">
                                                <i class="fas fa-exclamation-triangle me-2"></i> Force Reset
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="settings-item">
                                <div class="settings-label">Default Timezone</div>
                                <div class="settings-description">The default timezone used for displaying dates and times.</div>
                                <div class="settings-control">
                                    <select class="form-select" id="timezone">
                                        <option value="America/New_York" selected>Eastern Time (ET)</option>
                                        <option value="America/Chicago">Central Time (CT)</option>
                                        <option value="America/Denver">Mountain Time (MT)</option>
                                        <option value="America/Los_Angeles">Pacific Time (PT)</option>
                                        <option value="UTC">UTC</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>





                <!-- Security Settings -->
                <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
                    <div class="settings-section">
                        <div class="settings-section-title">
                            <i class="fas fa-shield-alt"></i> Security Settings
                        </div>
                        <div class="settings-card">
                            <div class="settings-item">
                                <div class="settings-label">Session Timeout</div>
                                <div class="settings-description">How long until inactive sessions are automatically logged out.</div>
                                <div class="settings-control">
                                    <select class="form-select" id="sessionTimeout">
                                        <option value="15">15 minutes</option>
                                        <option value="30">30 minutes</option>
                                        <option value="60">1 hour</option>
                                        <option value="120">2 hours</option>
                                        <option value="240">4 hours</option>
                                        <option value="480">8 hours</option>
                                        <option value="720">12 hours</option>
                                        <option value="1440">24 hours</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings-item">
                                <div class="settings-label">Login Attempts</div>
                                <div class="settings-description">Maximum number of failed login attempts before temporary lockout.</div>
                                <div class="settings-control">
                                    <select class="form-select" id="loginAttempts">
                                        <option value="3">3 attempts</option>
                                        <option value="5">5 attempts</option>
                                        <option value="10">10 attempts</option>
                                        <option value="15">15 attempts</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings-item">
                                <div class="settings-label">IP Whitelist</div>
                                <div class="settings-description">Restrict admin panel access to specific IP addresses. Your current IP will always be included to prevent lockout.</div>
                                <div class="settings-control">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enableIpWhitelist">
                                        <label class="form-check-label" for="enableIpWhitelist">Enable IP whitelist</label>
                                    </div>
                                    <textarea class="form-control" id="ipWhitelist" rows="3" placeholder="Enter IP addresses, one per line"></textarea>
                                    <div class="form-text">Your current IP: <span id="currentIp">Loading...</span></div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-primary" id="saveSecuritySettings">
                                    <i class="fas fa-save me-2"></i> Save Security Settings
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <div class="settings-section-title">
                            <i class="fas fa-history"></i> Audit Log
                        </div>
                        <div class="settings-card">
                            <div class="settings-item">
                                <div class="settings-label">View Audit Log</div>
                                <div class="settings-description">View a log of all administrative actions.</div>
                                <div class="settings-control">
                                    <a href="/dashboard/audit-logs" class="btn btn-primary">
                                        <i class="fas fa-external-link-alt me-2"></i> View Audit Log
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Player Services Settings -->
                <div class="tab-pane fade" id="player-services" role="tabpanel" aria-labelledby="player-services-tab">
                    <div class="settings-section">
                        <div class="settings-section-title">
                            <i class="fas fa-user-slash"></i> Blocked from Player Services
                        </div>
                        <div class="settings-card">
                            <div class="settings-item">
                                <div class="settings-label">Block Discord Users</div>
                                <div class="settings-description">Block specific Discord users from accessing certain player services like appeals, reports, tickets, or all services.</div>

                                <!-- Add New Block Form -->
                                <div class="card mt-3 mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-plus me-2"></i>Add New Block</h6>
                                    </div>
                                    <div class="card-body">
                                        <form id="addBlockForm">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <label for="blockDiscordId" class="form-label">Discord ID</label>
                                                    <input type="text" class="form-control" id="blockDiscordId" placeholder="Enter Discord ID" required>
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="blockServices" class="form-label">Blocked Services</label>
                                                    <select class="form-select" id="blockServices" required>
                                                        <option value="">Select services to block</option>
                                                        <option value="all">All Services</option>
                                                        <option value="appeals">Appeals Only</option>
                                                        <option value="reports">Reports Only</option>
                                                        <option value="tickets">Tickets Only</option>
                                                        <option value="applications">Applications Only</option>
                                                        <option value="appeals,reports">Appeals & Reports</option>
                                                        <option value="appeals,tickets">Appeals & Tickets</option>
                                                        <option value="appeals,applications">Appeals & Applications</option>
                                                        <option value="reports,tickets">Reports & Tickets</option>
                                                        <option value="reports,applications">Reports & Applications</option>
                                                        <option value="tickets,applications">Tickets & Applications</option>
                                                        <option value="appeals,reports,tickets">Appeals, Reports & Tickets</option>
                                                        <option value="appeals,reports,applications">Appeals, Reports & Applications</option>
                                                        <option value="appeals,tickets,applications">Appeals, Tickets & Applications</option>
                                                        <option value="reports,tickets,applications">Reports, Tickets & Applications</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="blockReason" class="form-label">Reason</label>
                                                    <input type="text" class="form-control" id="blockReason" placeholder="Reason for block" required>
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-12">
                                                    <label for="blockNotes" class="form-label">Additional Notes (Optional)</label>
                                                    <textarea class="form-control" id="blockNotes" rows="2" placeholder="Additional notes about this block"></textarea>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <button type="submit" class="btn btn-danger">
                                                    <i class="fas fa-ban me-2"></i>Add Block
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <!-- Current Blocks List -->
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Current Blocks</h6>
                                        <button class="btn btn-sm btn-outline-primary" id="refreshBlocksBtn">
                                            <i class="fas fa-sync-alt me-1"></i>Refresh
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="blocksTableContainer">
                                            <div class="text-center py-4">
                                                <i class="fas fa-spinner fa-spin fa-2x text-muted mb-3"></i>
                                                <p class="text-muted">Loading blocks...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/admin-common.min.js?v=3"></script>
    <script>

        document.addEventListener('DOMContentLoaded', function() {
            // Load current settings
            loadSettings();

            // Setup maintenance mode toggle
            const maintenanceMode = document.getElementById('maintenanceMode');
            const maintenanceMessageContainer = document.getElementById('maintenanceMessageContainer');

            if (maintenanceMode && maintenanceMessageContainer) {
                maintenanceMode.addEventListener('change', function() {
                    maintenanceMessageContainer.style.display = this.checked ? 'block' : 'none';
                });

                const saveMaintenanceBtn = document.getElementById('saveMaintenanceBtn');
                if (saveMaintenanceBtn) {
                    saveMaintenanceBtn.addEventListener('click', function() {
                        toggleMaintenanceMode();
                    });
                }

                // Setup maintenance mode reset button
                const resetMaintenanceBtn = document.getElementById('resetMaintenanceBtn');
                if (resetMaintenanceBtn) {
                    resetMaintenanceBtn.addEventListener('click', function() {
                        if (confirm('Are you sure you want to force reset maintenance mode? This will completely remove the maintenance mode setting from the database.')) {
                            resetMaintenanceMode();
                        }
                    });
                }
            }

            // Setup security settings save button
            const saveSecurityBtn = document.getElementById('saveSecuritySettings');
            if (saveSecurityBtn) {
                saveSecurityBtn.addEventListener('click', function() {
                    saveSecuritySettings();
                });
            }



            // Setup player services functionality
            setupPlayerServicesTab();
        });

        // Player Services Tab Functions
        function setupPlayerServicesTab() {
            // Load blocks when tab is shown
            const playerServicesTab = document.getElementById('player-services-tab');
            if (playerServicesTab) {
                playerServicesTab.addEventListener('shown.bs.tab', function() {
                    loadBlocks();
                });
            }

            // Setup add block form
            const addBlockForm = document.getElementById('addBlockForm');
            if (addBlockForm) {
                addBlockForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    addBlock();
                });
            }

            // Setup refresh button
            const refreshBlocksBtn = document.getElementById('refreshBlocksBtn');
            if (refreshBlocksBtn) {
                refreshBlocksBtn.addEventListener('click', function() {
                    loadBlocks();
                });
            }
        }

        function loadBlocks() {
            const container = document.getElementById('blocksTableContainer');
            if (!container) return;

            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x text-muted mb-3"></i>
                    <p class="text-muted">Loading blocks...</p>
                </div>
            `;

            fetch('/adminapi/player-services/blocks', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayBlocks(data.blocks);
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Failed to load blocks: ${data.message || 'Unknown error'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading blocks:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load blocks. Please try again.
                    </div>
                `;
            });
        }

        function displayBlocks(blocks) {
            const container = document.getElementById('blocksTableContainer');
            if (!container) return;

            if (blocks.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                        <p class="text-muted">No blocked users found.</p>
                    </div>
                `;
                return;
            }

            const tableHTML = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Discord ID</th>
                                <th>Blocked Services</th>
                                <th>Reason</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${blocks.map(block => `
                                <tr>
                                    <td>
                                        <code>${escapeHtml(block.discord_id)}</code>
                                    </td>
                                    <td>
                                        ${formatBlockedServices(block.blocked_services)}
                                    </td>
                                    <td>
                                        <span>${escapeHtml(block.reason)}</span>
                                        ${block.notes ? `<i class="fas fa-info-circle text-info ms-1 cursor-pointer" onclick="showNotesModal('${escapeHtml(block.notes)}', '${escapeHtml(block.discord_id)}')" title="Click to view additional notes"></i>` : ''}
                                    </td>
                                    <td>
                                        <small class="text-muted">${block.created_at}</small>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" onclick="removeBlock('${block.discord_id}')">
                                            <i class="fas fa-trash me-1"></i>Remove
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = tableHTML;
        }

        function formatBlockedServices(services) {
            if (services === 'all') {
                return '<span class="badge bg-danger">All Services</span>';
            }

            const serviceList = services.split(',');
            const badges = serviceList.map(service => {
                const colors = {
                    'appeals': 'warning',
                    'reports': 'info',
                    'tickets': 'primary',
                    'applications': 'success'
                };
                return `<span class="badge bg-${colors[service] || 'secondary'} me-1">${service.charAt(0).toUpperCase() + service.slice(1)}</span>`;
            });

            return badges.join('');
        }

        function addBlock() {
            const discordId = document.getElementById('blockDiscordId').value.trim();
            const services = document.getElementById('blockServices').value;
            const reason = document.getElementById('blockReason').value.trim();
            const notes = document.getElementById('blockNotes').value.trim();

            if (!discordId || !services || !reason) {
                showToast('Error', 'Please fill in all required fields.', 'danger');
                return;
            }

            // Validate Discord ID format (should be numeric and 17-19 digits)
            if (!/^\d{17,19}$/.test(discordId)) {
                showToast('Error', 'Invalid Discord ID format. Discord IDs should be 17-19 digits.', 'danger');
                return;
            }

            const submitBtn = document.querySelector('#addBlockForm button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding...';
            submitBtn.disabled = true;

            fetch('/adminapi/player-services/blocks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    discord_id: discordId,
                    blocked_services: services,
                    reason: reason,
                    notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;

                if (data.success) {
                    showToast('Success', 'Block added successfully.', 'success');
                    document.getElementById('addBlockForm').reset();
                    loadBlocks();
                } else {
                    showToast('Error', data.message || 'Failed to add block.', 'danger');
                }
            })
            .catch(error => {
                console.error('Error adding block:', error);
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                showToast('Error', 'Failed to add block. Please try again.', 'danger');
            });
        }

        function removeBlock(discordId) {
            if (!confirm('Are you sure you want to remove this block? The user will be able to access the services again.')) {
                return;
            }

            fetch('/adminapi/player-services/blocks', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    discord_id: discordId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Success', 'Block removed successfully.', 'success');
                    loadBlocks();
                } else {
                    showToast('Error', data.message || 'Failed to remove block.', 'danger');
                }
            })
            .catch(error => {
                console.error('Error removing block:', error);
                showToast('Error', 'Failed to remove block. Please try again.', 'danger');
            });
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showNotesModal(notes, discordId) {
            // Create modal HTML
            const modalHTML = `
                <div class="modal fade" id="notesModal" tabindex="-1" aria-labelledby="notesModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="notesModalLabel">
                                    <i class="fas fa-sticky-note me-2"></i>Additional Notes
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <strong>Discord ID:</strong> <code>${escapeHtml(discordId)}</code>
                                </div>
                                <div class="notes-content">
                                    <strong>Notes:</strong>
                                    <div class="mt-2 p-3 bg-light rounded">
                                        ${escapeHtml(notes).replace(/\n/g, '<br>')}
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('notesModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('notesModal'));
            modal.show();

            // Clean up modal when hidden
            document.getElementById('notesModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        // Load all settings from the server
        function loadSettings() {
            // Load maintenance mode status with cache-busting
            fetch('/adminapi/maintenance/toggle?_=' + new Date().getTime(), {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(response => {
                if (response.success) {
                    const maintenanceMode = document.getElementById('maintenanceMode');
                    const maintenanceMessage = document.getElementById('maintenanceMessage');
                    const maintenanceMessageContainer = document.getElementById('maintenanceMessageContainer');

                    if (maintenanceMode && maintenanceMessage && maintenanceMessageContainer) {
                        // Log the status for debugging
                        console.log('Maintenance mode status:', response.enabled ? 'ENABLED' : 'DISABLED');

                        // Explicitly set the checkbox state
                        maintenanceMode.checked = Boolean(response.enabled);
                        maintenanceMessage.value = response.message || '';
                        maintenanceMessageContainer.style.display = response.enabled ? 'block' : 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Failed to load maintenance mode status:', error);
            });

            // Load security settings
            $.ajax({
                url: '/adminapi/security/get',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const settings = response.settings;

                        // Update session timeout
                        const sessionTimeout = document.getElementById('sessionTimeout');
                        if (sessionTimeout && settings.sessionTimeout) {
                            sessionTimeout.value = settings.sessionTimeout;
                        }

                        // Update login attempts
                        const loginAttempts = document.getElementById('loginAttempts');
                        if (loginAttempts && settings.loginAttempts) {
                            loginAttempts.value = settings.loginAttempts;
                        }

                        // Update IP whitelist
                        const enableIpWhitelist = document.getElementById('enableIpWhitelist');
                        const ipWhitelist = document.getElementById('ipWhitelist');
                        const currentIp = document.getElementById('currentIp');

                        if (enableIpWhitelist && ipWhitelist) {
                            enableIpWhitelist.checked = settings.enableIpWhitelist;
                            ipWhitelist.value = settings.ipWhitelist || '';
                        }

                        if (currentIp) {
                            currentIp.textContent = settings.currentIp || 'Unknown';
                        }
                    }
                },
                error: function(xhr) {
                    console.error('Failed to load security settings:', xhr);
                }
            });
        }

        // Toggle maintenance mode
        function toggleMaintenanceMode() {
            const maintenanceMode = document.getElementById('maintenanceMode');
            const maintenanceMessage = document.getElementById('maintenanceMessage');
            const notifyUsers = document.getElementById('notifyUsers');
            const saveMaintenanceBtn = document.getElementById('saveMaintenanceBtn');

            if (!maintenanceMode || !maintenanceMessage || !saveMaintenanceBtn) {
                return;
            }

            const enabled = maintenanceMode.checked;
            const message = maintenanceMessage.value.trim();

            if (enabled && !message) {
                showToast('Error', 'Please enter a maintenance message.', 'danger');
                return;
            }

            // If disabling maintenance mode, use a default message
            const finalMessage = enabled ? message : 'The system is currently undergoing maintenance. Please try again later.';

            const originalBtnText = saveMaintenanceBtn.innerHTML;
            saveMaintenanceBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Saving...';
            saveMaintenanceBtn.disabled = true;

            // Log the request data for debugging
            console.log('Maintenance mode toggle request:', {
                enabled: enabled,
                message: finalMessage,
                notify_users: notifyUsers ? notifyUsers.checked : false
            });

            // Use fetch instead of $.ajax for better error handling
            fetch('/adminapi/maintenance/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    enabled: enabled,
                    message: finalMessage,
                    notify_users: notifyUsers ? notifyUsers.checked : false
                })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || `HTTP error! Status: ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                saveMaintenanceBtn.innerHTML = originalBtnText;
                saveMaintenanceBtn.disabled = false;

                if (data.success) {
                    showToast('Success', data.message || 'Maintenance mode settings updated successfully.', 'success');

                    // Force reload after 2 seconds if maintenance mode was disabled
                    if (!enabled) {
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    }
                } else {
                    showToast('Error', data.message || 'Failed to update maintenance mode settings.', 'danger');
                }
            })
            .catch(error => {
                saveMaintenanceBtn.innerHTML = originalBtnText;
                saveMaintenanceBtn.disabled = false;

                console.error('Error toggling maintenance mode:', error);
                showToast('Error', error.message || 'An error occurred while updating maintenance mode settings.', 'danger');
            });
        }

        // Force reset maintenance mode
        function resetMaintenanceMode() {
            const resetMaintenanceBtn = document.getElementById('resetMaintenanceBtn');
            if (!resetMaintenanceBtn) {
                return;
            }

            const originalBtnText = resetMaintenanceBtn.innerHTML;
            resetMaintenanceBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Resetting...';
            resetMaintenanceBtn.disabled = true;

            // Call the reset endpoint
            fetch('/adminapi/maintenance/reset', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || `HTTP error! Status: ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                resetMaintenanceBtn.innerHTML = originalBtnText;
                resetMaintenanceBtn.disabled = false;

                if (data.success) {
                    showToast('Success', data.message || 'Maintenance mode has been forcibly reset.', 'success');

                    // Force reload after 2 seconds
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showToast('Error', data.message || 'Failed to reset maintenance mode.', 'danger');
                }
            })
            .catch(error => {
                resetMaintenanceBtn.innerHTML = originalBtnText;
                resetMaintenanceBtn.disabled = false;

                console.error('Error resetting maintenance mode:', error);
                showToast('Error', error.message || 'An error occurred while resetting maintenance mode.', 'danger');
            });
        }

        // Save security settings
        function saveSecuritySettings() {
            const sessionTimeout = document.getElementById('sessionTimeout');
            const loginAttempts = document.getElementById('loginAttempts');
            const enableIpWhitelist = document.getElementById('enableIpWhitelist');
            const ipWhitelist = document.getElementById('ipWhitelist');
            const saveSecurityBtn = document.getElementById('saveSecuritySettings');

            if (!sessionTimeout || !loginAttempts || !enableIpWhitelist || !ipWhitelist || !saveSecurityBtn) {
                return;
            }

            const originalBtnText = saveSecurityBtn.innerHTML;
            saveSecurityBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Saving...';
            saveSecurityBtn.disabled = true;

            $.ajax({
                url: '/adminapi/security/update',
                method: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify({
                    sessionTimeout: sessionTimeout.value,
                    loginAttempts: loginAttempts.value,
                    enableIpWhitelist: enableIpWhitelist.checked,
                    ipWhitelist: ipWhitelist.value
                }),
                success: function(response) {
                    saveSecurityBtn.innerHTML = originalBtnText;
                    saveSecurityBtn.disabled = false;

                    if (response.success) {
                        showToast('Success', response.message || 'Security settings updated successfully.', 'success');
                    } else {
                        showToast('Error', response.message || 'Failed to update security settings.', 'danger');
                    }
                },
                error: function(xhr) {
                    saveSecurityBtn.innerHTML = originalBtnText;
                    saveSecurityBtn.disabled = false;

                    let errorMsg = 'Failed to update security settings.';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMsg = response.message;
                        }
                    } catch (e) {
                        // Ignore parsing error
                    }

                    showToast('Error', errorMsg, 'danger');
                }
            });
        }


        function showToast(title, message, type = 'primary') {
            const toastHTML = `
                <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050">
                    <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="toast-header bg-${type} text-white">
                            <strong class="me-auto">${title}</strong>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            ${message}
                        </div>
                    </div>
                </div>
            `;


            const toastContainer = document.createElement('div');
            toastContainer.innerHTML = toastHTML;
            document.body.appendChild(toastContainer);


            setTimeout(() => {
                document.body.removeChild(toastContainer);
            }, 3000);
        }
    </script>
</body>
</html>
