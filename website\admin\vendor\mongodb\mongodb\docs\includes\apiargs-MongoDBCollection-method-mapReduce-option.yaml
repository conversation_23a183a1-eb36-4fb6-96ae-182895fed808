source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: bypassDocumentValidation
post: |
  This only applies when results are output to a collection.
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: collation
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.13
---
arg_name: option
name: finalize
type: :php:`MongoDB\\BSON\\Javascript <class.mongodb-bson-javascript>`
description: |
  Follows the reduce method and modifies the output.

  .. note::

     Passing a Javascript instance with a scope is deprecated. Put all scope
     variables in the ``scope`` option of the MapReduce operation.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: jsMode
type: boolean
description: |
  Specifies whether to convert intermediate data into BSON format between the
  execution of the map and reduce functions.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: limit
type: integer
description: |
  Specifies a maximum number of documents for the input into the map function.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: maxTimeMS
---
arg_name: option
name: query
type: array|object
description: |
  Specifies the selection criteria using query operators for determining the
  documents input to the map function.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readConcern
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readPreference
post: |
  This option will be ignored when results are output to a collection.
---
arg_name: option
name: scope
type: array|object
description: |
  Specifies global variables that are accessible in the map, reduce, and finalize
  functions.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
---
source:
  file: apiargs-MongoDBCollection-method-find-option.yaml
  ref: sort
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: typeMap
---
arg_name: option
name: verbose
type: boolean
description: |
  Specifies whether to include the timing information in the result information.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: writeConcern
...
