/* Report Details Styling */

/* Report Details Panel */
#reportDetails {
    position: fixed;
    top: 0;
    right: -500px;
    width: 500px;
    max-width: 100%;
    height: 100vh;
    background-color: #fff;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    overflow-y: auto;
    transition: right 0.3s ease;
    padding: 0;
}

/* Dark mode for report details panel */
body.dark-mode #reportDetails {
    background-color: var(--secondary-color);
}

#reportDetails.active {
    right: 0;
}

/* Close button hover effect */
.btn-close:hover {
    opacity: 1;
}

/* New Report Details Styling */

/* Status banner */
.status-banner {
    border-radius: 6px;
    font-weight: 500;
}

.bg-warning-subtle {
    background-color: rgba(255, 193, 7, 0.15);
}

.bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.15);
}

.bg-danger-subtle {
    background-color: rgba(220, 53, 69, 0.15);
}

/* Info boxes */
.info-box {
    background-color: #fff;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.info-box:hover {
    box-shadow: 0 3px 8px rgba(0,0,0,0.1);
}

/* Player name */
.player-name {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: -0.5px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

/* Timeline list */
.timeline-list li {
    padding: 6px 0;
    border-bottom: 1px dashed #e9ecef;
}

.timeline-list li:last-child {
    border-bottom: none;
}

/* Evidence box */
.evidence-box {
    max-height: 300px;
    overflow-y: auto;
    background-color: #f8f9fa;
}

.evidence-content {
    white-space: pre-wrap;
    word-break: break-word;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.9rem;
}

/* Status badges */
.badge {
    font-weight: 500;
    padding: 0.5em 0.8em;
    letter-spacing: normal;
}

/* Resolution box */
.report-resolution {
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* Action buttons */
.report-actions .btn {
    min-width: 150px;
    transition: all 0.2s;
}

.report-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Resolution box */
.bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.1);
}

.bg-danger-subtle {
    background-color: rgba(220, 53, 69, 0.1);
}

.report-resolution {
    border-left: 4px solid;
}

.report-resolution h5 {
    color: #495057;
}

.report-resolution p {
    color: #6c757d;
}

/* For Accepted reports */
.report-resolution.bg-success-subtle {
    border-left-color: #198754;
}

/* For Denied reports */
.report-resolution.bg-danger-subtle {
    border-left-color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #reportDetails {
        width: 100%;
        right: -100%;
    }

    .report-actions {
        flex-direction: column;
        gap: 10px !important;
    }

    .report-actions .btn {
        width: 100%;
    }
}
