arg_name: param
name: $filter
type: array|object
description: |
  The filter criteria that specifies the documents{{action}}.
interface: phpmethod
operation: ~
optional: false
replacement:
  action: ""
---
arg_name: param
name: $replacement
type: array|object
description: |
  The replacement document.
interface: phpmethod
operation: ~
optional: false
---
arg_name: param
name: $update
type: array|object
description: |
  Specifies the field and value combinations to update and any relevant update
  operators. ``$update`` uses MongoDB's :method:`update operators
  </reference/operator/update>`. Starting with MongoDB 4.2, an `aggregation
  pipeline <https://mongodb.com/docs/master/reference/command/update/#update-with-an-aggregation-pipeline>`_
  can be passed as this parameter.
interface: phpmethod
operation: ~
optional: false
...
