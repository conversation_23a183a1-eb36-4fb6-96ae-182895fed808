=====================================
MongoDB\\Database::renameCollection()
=====================================

.. versionadded:: 1.10

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Database::renameCollection()

   Rename a collection within the current database.

   .. code-block:: php

      function renameCollection(string $fromCollectionName, string $toCollectionName, ?string $toDatabaseName = null, array $options = []): array|object

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBDatabase-method-renameCollection-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBDatabase-method-renameCollection-option.rst

Return Values
-------------

An array or object with the result document of the :manual:`renameCollection
</reference/command/renameCollection>` command. The return type will depend on the
``typeMap`` option.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Example
-------

The following example renames the ``restaurants`` collection in the ``test``
database to ``places``:

.. code-block:: php

   <?php

   $db = (new MongoDB\Client)->test;

   $result = $db->renameCollection('restaurants', 'places');

   var_dump($result);

The output would then resemble::

   object(MongoDB\Model\BSONDocument)#8 (1) {
     ["storage":"ArrayObject":private]=>
     array(1) {
       ["ok"]=>
       float(1)
     }
   }

See Also
--------

- :phpmethod:`MongoDB\\Collection::rename()`
- :manual:`renameCollection </reference/command/renameCollection>` command reference in the MongoDB
  manual
