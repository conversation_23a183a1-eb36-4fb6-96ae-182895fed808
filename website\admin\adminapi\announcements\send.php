<?php
/**
 * Send Announcement API Endpoint
 * Allows admins to send global or targeted announcements to staff members
 */


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/security.php';
require_once __DIR__ . '/../../includes/notifications.php';


header('Content-Type: application/json');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


error_log("Send Announcement API Request - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Send Announcement API Request - Session ID: " . session_id());
error_log("Send Announcement API Request - User ID: " . ($_SESSION['discord_user_id'] ?? 'Not set'));
error_log("Send Announcement API Request - User Role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));


if (!is_authenticated()) {
    error_log("Send Announcement API - Authentication failed");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_ADMIN)) {
    error_log("Send Announcement API - Authorization failed - User role: " . ($_SESSION['discord_user_role'] ?? 'Unknown'));
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access - Admin role required']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {

        $json = file_get_contents('php://input');
        $data = json_decode($json, true);


        if (empty($data['title']) || empty($data['message'])) {
            error_log("Send Announcement API - Missing required fields");
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            exit;
        }


        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            error_log("Send Announcement API - Database connection failed");
            throw new Exception("Failed to connect to database");
        }


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('announcements', $collections)) {
            error_log("Send Announcement API - Creating announcements collection");
            $db->createCollection('announcements');
            $db->announcements->createIndex(['timestamp' => -1]);
            $db->announcements->createIndex(['recipients' => 1]);
            $db->announcements->createIndex(['is_global' => 1]);
        }


        $message = str_replace("\r\n", "\n", $data['message']);


        $announcement = [
            'title' => $data['title'],
            'message' => $message,
            'sender_id' => $_SESSION['discord_user_id'],
            'sender_name' => $_SESSION['discord_username'],
            'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
            'is_global' => isset($data['is_global']) && $data['is_global'] === true,
            'recipients' => [],
            'read_by' => []
        ];


        if (!$announcement['is_global'] && !empty($data['recipients'])) {
            $announcement['recipients'] = $data['recipients'];
        }


        $result = $db->announcements->insertOne($announcement);

        if (!$result->getInsertedId()) {
            error_log("Send Announcement API - Failed to insert announcement");
            throw new Exception("Failed to insert announcement");
        }


        $target = $announcement['is_global'] ? 'All staff members' : count($announcement['recipients']) . ' staff members';
        $announcementType = $announcement['is_global'] ? 'global' : 'targeted';

        $db->staff_activity_log->insertOne([
            'user_id' => $_SESSION['discord_user_id'],
            'username' => $_SESSION['discord_username'],
            'action' => "Sent " . $announcementType . " announcement: " . $data['title'],
            'type' => 'announcement',
            'target' => $target,
            'details' => "Message: " . substr($data['message'], 0, 100) . (strlen($data['message']) > 100 ? '...' : ''),
            'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
            'description' => "Sent " . $announcementType . " announcement to " . $target
        ]);


        if ($announcement['is_global']) {

            error_log("Creating global notification for announcement: " . $data['title']);
            create_notification(
                'all', // Send to all users
                'announcement',
                $data['title'],
                $message,
                null // No link for now
            );
        } else if (!empty($announcement['recipients'])) {

            error_log("Creating targeted notifications for " . count($announcement['recipients']) . " recipients");
            foreach ($announcement['recipients'] as $recipientId) {
                create_notification(
                    $recipientId,
                    'announcement',
                    $data['title'],
                    $message,
                    null // No link for now
                );
            }
        }


        echo json_encode([
            'success' => true,
            'message' => "Successfully sent " . ($announcement['is_global'] ? 'global' : 'targeted') . " announcement",
            'announcement_id' => (string)$result->getInsertedId()
        ]);

    } catch (Exception $e) {

        error_log("Send Announcement API Error: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());


        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error: ' . $e->getMessage()
        ]);
    }
} else {

    error_log("Send Announcement API - Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
