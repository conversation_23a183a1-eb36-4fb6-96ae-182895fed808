/* Modern PlayerInfo Design - Dark Mode Only */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.4);
    --border-radius: 16px;
    --border-radius-sm: 12px;
    --border-radius-lg: 20px;

    /* Dark mode colors as default */
    --text-primary: #e9ecef;
    --text-secondary: #adb5bd;
    --text-muted: #6c757d;
    --bg-light: #2c3e50;
    --bg-card: #34495e;
    --border-color: #495057;
}

/* Modern elements now use dark colors by default */

/* Modern Player Card */
.modern-player-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.modern-player-card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
}

/* Player Header */
.player-header-modern {
    background: var(--primary-gradient);
    padding: 2rem;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.player-avatar-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.player-avatar-modern {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.player-avatar-modern img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.online-indicator {
    position: absolute;
    bottom: 4px;
    right: 4px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.online-indicator.online {
    background: #2ecc71;
    animation: pulse-online 2s infinite;
}

.online-indicator.offline {
    background: #e74c3c;
}

@keyframes pulse-online {
    0% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(46, 204, 113, 0); }
    100% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0); }
}

.player-info-modern {
    flex: 1;
}

.player-name-modern {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.player-badges-modern {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
}

.rank-badge-modern {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.faction-badge-modern {
    background: rgba(52, 152, 219, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(52, 152, 219, 0.3);
    color: #3498db;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
}

.no-faction-badge-modern {
    background: rgba(149, 165, 166, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(149, 165, 166, 0.3);
    color: #95a5a6;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
}

.player-status-modern {
    display: flex;
    gap: 1.5rem;
    font-size: 0.875rem;
    opacity: 0.9;
    flex-wrap: wrap;
}

.status-indicator {
    display: flex;
    align-items: center;
    font-weight: 500;
}

.status-indicator.online {
    color: #2ecc71;
}

.status-indicator.offline {
    color: #e74c3c;
}

.last-seen-modern {
    display: flex;
    align-items: center;
    opacity: 0.8;
}

/* Player Actions */
.player-actions-modern {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.action-btn-modern {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.875rem;
}

.action-btn-modern:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    color: white;
}

.action-btn-modern.rank-btn:hover {
    background: var(--warning-gradient);
    border-color: transparent;
}

.action-btn-modern.punish-btn:hover {
    background: var(--danger-gradient);
    border-color: transparent;
}

.action-btn-modern.note-btn:hover {
    background: var(--success-gradient);
    border-color: transparent;
}

/* Quick Stats */
.quick-stats-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
    background: var(--bg-light);
}

.stat-item-modern {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.stat-item-modern:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-icon-modern {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.stat-icon-modern.kills {
    background: var(--danger-gradient);
}

.stat-icon-modern.deaths {
    background: var(--dark-gradient);
}

.stat-icon-modern.kdr {
    background: var(--primary-gradient);
}

.stat-icon-modern.balance {
    background: var(--warning-gradient);
}

.stat-content-modern {
    flex: 1;
}

.stat-value-modern {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}

.stat-label-modern {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Text colors are now dark mode by default */

/* Modern Tabs */
.modern-tabs-container {
    background: var(--bg-card);
    border-top: 1px solid var(--border-color);
}

.modern-tabs-nav {
    display: flex;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.modern-tabs-nav::-webkit-scrollbar {
    display: none;
}

.modern-tab-btn {
    background: none;
    border: none;
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    color: var(--text-secondary);
    text-decoration: none;
    min-width: 200px;
    flex-shrink: 0;
}

.modern-tab-btn:hover {
    background: var(--bg-light);
    color: var(--text-primary);
}

.modern-tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: var(--bg-light);
}

.tab-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-light);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.modern-tab-btn.active .tab-icon {
    background: #667eea;
    color: white;
}

.tab-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.tab-title {
    font-weight: 600;
    font-size: 0.95rem;
}

.tab-subtitle {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Modern Tab Content */
.modern-tab-content {
    background: var(--bg-card);
    min-height: 400px;
}

.modern-tab-panel {
    padding: 1.5rem;
}

/* Tab pane specific styling */
.tab-pane {
    min-height: 300px;
    padding: 1rem;
}

/* Empty state styling */
.empty-state-modern {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
    min-height: 300px;
}

.empty-state-modern i {
    font-size: 4rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-state-modern h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.empty-state-modern p {
    color: var(--text-secondary);
    margin-bottom: 0;
    max-width: 400px;
}

/* Punishment History Specific Styling */
.punishment-history-container {
    padding: 1rem;
    min-height: 300px;
}

.punishment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 0 0.5rem;
}

.punishment-header h3 {
    color: var(--text-primary);
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.punishment-header .btn {
    background: var(--primary-gradient);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    transition: all 0.3s ease;
}

.punishment-header .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Punishment cards/list styling */
.punishment-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.punishment-item {
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    transition: all 0.3s ease;
}

.punishment-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Notes section styling */
.notes-container {
    padding: 1rem;
    min-height: 300px;
}

.notes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 0 0.5rem;
}

.notes-header h3 {
    color: var(--text-primary);
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-grid-modern {
    display: grid;
    gap: 2rem;
}

.info-card-modern {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.info-header-modern {
    background: var(--primary-gradient);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.info-icon-modern {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.info-header-modern h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.info-content-modern {
    padding: 2rem;
}

.info-row-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.info-row-modern:last-child {
    margin-bottom: 0;
}

.info-item-modern {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item-modern.full-width {
    grid-column: 1 / -1;
}

.info-label-modern {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    display: flex;
    align-items: center;
}

.info-value-modern {
    font-size: 1rem;
    color: var(--text-primary);
    font-weight: 600;
    padding: 0.75rem 1rem;
    background: var(--bg-light);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
}

.info-value-modern.monospace {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

/* Stats Cards Enhancement */
.stats-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.stats-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* Compact Stat Cards */
.stat-card-compact {
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
}

.stat-card-compact:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon-compact {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.stat-icon-compact.kills {
    background: var(--danger-gradient);
}

.stat-icon-compact.deaths {
    background: var(--dark-gradient);
}

.stat-icon-compact.kdr {
    background: var(--primary-gradient);
}

.stat-icon-compact.balance {
    background: var(--warning-gradient);
}

.stat-icon-compact.strength {
    background: var(--success-gradient);
}

.stat-icon-compact.killstreak {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.stat-content-compact {
    flex: 1;
    min-width: 0;
}

.stat-value-compact {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}

.stat-label-compact {
    display: block;
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-card-enhanced {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.stat-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-header-enhanced {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-icon-enhanced {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    color: white;
}

.stat-title-enhanced {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.stat-value-enhanced {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-description-enhanced {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Progress Bars */
.progress-modern {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 1rem;
}

.progress-bar-modern {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* Modal Fixes */
.modal {
    z-index: 1055;
}

.modal-dialog {
    margin: 1rem auto;
    max-height: calc(100vh - 2rem);
    display: flex;
    align-items: center;
    min-height: calc(100vh - 2rem);
}

.modal-content {
    max-height: calc(100vh - 2rem);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.modal-header {
    background: var(--primary-gradient);
    color: white;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    flex-shrink: 0;
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background: var(--bg-card);
    color: var(--text-primary);
}

.modal-footer {
    background: var(--bg-light);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    flex-shrink: 0;
}

.modal-title {
    color: white;
    font-weight: 600;
}

/* Form elements in modals */
.modal .form-label {
    color: var(--text-primary);
    font-weight: 500;
}

.modal .form-control,
.modal .form-select {
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.modal .form-control:focus,
.modal .form-select:focus {
    background: var(--bg-light);
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    color: var(--text-primary);
}

.modal .form-text {
    color: var(--text-secondary);
}

.modal .alert {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #ffc107;
}

.modal .card {
    background: var(--bg-light);
    border: 1px solid var(--border-color);
}

.modal .card-body {
    color: var(--text-primary);
}

/* Responsive modal fixes */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
        max-height: calc(100vh - 1rem);
        min-height: calc(100vh - 1rem);
    }

    .modal-content {
        max-height: calc(100vh - 1rem);
    }

    .modal-header,
    .modal-footer {
        padding: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .player-header-modern {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .player-avatar-section {
        flex-direction: column;
        text-align: center;
    }

    .player-actions-modern {
        justify-content: center;
        width: 100%;
    }

    .quick-stats-modern {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .modern-tab-btn {
        min-width: 150px;
        padding: 1rem 1.5rem;
    }

    .player-name-modern {
        font-size: 1.5rem;
    }

    .modern-tab-panel {
        padding: 1rem;
    }

    .info-row-modern {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stats-grid-modern {
        grid-template-columns: 1fr;
    }
}
