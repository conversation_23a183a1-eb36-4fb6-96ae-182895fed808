arg_name: option
name: readConcern
type: :php:`MongoDB\\Driver\\ReadConcern <class.mongodb-driver-readconcern>`
description: |
   :manual:`Read concern </reference/read-concern>` to use for the operation.
   Defaults to the client's read concern.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: readPreference
type: :php:`MongoDB\\Driver\\ReadPreference <class.mongodb-driver-readpreference>`
description: |
   :manual:`Read preference </reference/read-preference>` to use for the
   operation. Defaults to the client's read preference.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: typeMap
---
arg_name: option
name: writeConcern
type: :php:`MongoDB\\Driver\\WriteConcern <class.mongodb-driver-writeconcern>`
description: |
   :manual:`Write concern </reference/write-concern>` to use for the operation.
   Defaults to the client's write concern.
interface: phpmethod
operation: ~
optional: true
...
