<?php
/**
 * Database Access Class
 *
 * Provides centralized database access with security and performance optimizations
 */

require_once __DIR__ . '/../admin/vendor/autoload.php';
require_once __DIR__ . '/mongo_security.php';
require_once __DIR__ . '/db_optimization.php';

use MongoDB\Client;
use MongoDB\BSON\UTCDateTime;
use MongoDB\BSON\ObjectId;

/**
 * Custom config file parser that handles problematic characters like ~
 */
if (!function_exists('parse_config_file_safe')) {
function parse_config_file_safe($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }

    $config = [];
    $lines = file($file_path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

    if ($lines === false) {
        return false;
    }

    foreach ($lines as $line_num => $line) {
        $line = trim($line);

        // Skip comments and empty lines
        if (empty($line) || $line[0] === '#' || $line[0] === ';') {
            continue;
        }

        // Look for key=value pairs
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);

            // Remove quotes if present
            if (strlen($value) >= 2 &&
                (($value[0] === '"' && $value[strlen($value)-1] === '"') ||
                 ($value[0] === "'" && $value[strlen($value)-1] === "'"))) {
                $value = substr($value, 1, -1);
            }

            // Handle ~ character by expanding it to home directory if needed
            if (strpos($value, '~') === 0) {
                $value = str_replace('~', '/home', $value);
            }

            $config[$key] = $value;
        }
    }

    return $config;
}
}

class DatabaseAccess {
    private $client;
    public $db; // Changed to public so it can be checked in submit_appeal.php
    private $reports_collection;

    /**
     * Initialize MongoDB connection with connection pooling and error handling
     */
    public function __construct() {
        try {
            static $cached_client = null;
            static $cached_db = null;

            if ($cached_client !== null && $cached_db !== null) {
                $this->client = $cached_client;
                $this->db = $cached_db;
                $this->reports_collection = $this->db->reports;
                return;
            }


            // Load environment configuration with support for special characters
            require_once __DIR__ . '/config_parser.php';

            try {
                $env = load_env_config([
                    '/etc/massacremc/config/api.env',
                    '/etc/massacremc/config/admin.env'
                ]);
            } catch (Exception $e) {
                throw new Exception("Database configuration not found. Please check environment settings.");
            }

            // If missing required values, throw an exception
            if (empty($env['MONGO_URI'])) {
                throw new Exception("Database configuration not found. Please check environment settings.");
            }


            $mongo_uri = $env['MONGO_URI'] ?? 'mongodb://localhost:27017';
            $db_name = $env['MONGO_DB'] ?? 'mmc';


            // Set secure connection options
            $options = [
                'connectTimeoutMS' => 10000,
                'serverSelectionTimeoutMS' => 10000,
                'w' => 1,
                'readPreference' => 'primaryPreferred',
                'retryWrites' => true,
                'maxPoolSize' => 50,
                'minPoolSize' => 5,
                'socketTimeoutMS' => 60000,
                'heartbeatFrequencyMS' => 10000,
                // Use SSL if available
                'ssl' => !empty($env['MONGO_SSL']) && $env['MONGO_SSL'] === 'true'
            ];

            try {
                // Create MongoDB client
                $this->client = new Client($mongo_uri, $options);

                // Select database
                $this->db = $this->client->{$db_name};

                // Log successful connection
                secure_log("MongoDB connection established", "info");
            } catch (Exception $e) {
                // Log error securely
                secure_log("MongoDB connection error", "error", [
                    'message' => $e->getMessage()
                ]);

                // Re-throw to be caught by the outer try-catch
                throw $e;
            }


            $dbName = $this->db->getDatabaseName();
            secure_log("Using database: $dbName", "info");

            if ($dbName !== 'mmc') {
                secure_log("WARNING: Not using the 'mmc' database. Current database: $dbName", "warning");

                try {
                    $this->db = $this->client->selectDatabase('mmc');
                    secure_log("Switched to 'mmc' database", "info");
                } catch (Exception $e) {
                    secure_log("Failed to switch to 'mmc' database: " . $e->getMessage(), "error");

                }
            }


            try {
                $collections = [];
                foreach ($this->db->listCollections() as $collectionInfo) {
                    $collections[] = $collectionInfo->getName();
                }

                secure_log("Available collections: " . implode(', ', $collections), "debug");

                if (!in_array('reports', $collections)) {
                    secure_log("reports collection does not exist. Creating it now.", "info");
                    $this->db->createCollection('reports');
                    $this->db->reports->createIndex(['_id' => 1], ['unique' => true]);
                    $this->db->reports->createIndex(['status' => 1]);
                    $this->db->reports->createIndex(['created_at' => -1]);
                    $this->db->reports->createIndex(['offender_name' => 1]);
                    secure_log("reports collection created successfully with indexes", "info");
                }
            } catch (Exception $e) {
                secure_log("Error checking/creating reports collection: " . $e->getMessage(), "error");

            }

            $this->reports_collection = $this->db->reports;


            $cached_client = $this->client;
            $cached_db = $this->db;


            $this->ensure_indexes();

        } catch (Exception $e) {
            secure_log("MongoDB connection error: " . $e->getMessage(), "error");

            // Set properties to null to indicate failed connection
            $this->client = null;
            $this->db = null;
            $this->reports_collection = null;
        }
    }

    /**
     * Ensure all necessary indexes exist for optimal performance
     */
    private function ensure_indexes() {
        try {

            create_database_indexes($this->db);
        } catch (Exception $e) {
            secure_log("Error creating indexes: " . $e->getMessage(), "error");
        }
    }


    /**
     * Submit a report directly to MongoDB with improved security and error handling
     *
     * @param string $offender_name Name of the player being reported
     * @param string $rule_broken Rule that was broken
     * @param string $evidence Evidence of the rule violation
     * @param string $reporters_email Email of the person reporting
     * @param string $reporter_name Discord username of the reporter
     * @param string $discord_id Discord ID of the reporter
     * @return array Result of the operation
     */
    public function submit_report($offender_name, $rule_broken, $evidence, $reporters_email, $reporter_name = '', $discord_id = '') {
        // Check if user is blocked from reports
        if (!empty($discord_id)) {
            require_once __DIR__ . '/player-service-blocks.php';
            $blockInfo = isUserBlockedFromService($discord_id, 'reports');
            if ($blockInfo) {
                return [
                    'success' => false,
                    'blocked' => true,
                    'message' => getBlockMessage($blockInfo, 'reports')
                ];
            }
        }

        if (!isset($this->reports_collection) || $this->reports_collection === null) {
            secure_log("Reports collection not initialized in submit_report, attempting to initialize", "warning");
            try {

                if (!isset($this->db) || $this->db === null) {
                    secure_log("Database connection is not valid in submit_report", "error");
                    return [
                        'error' => 'Database connection error',
                        'success' => false
                    ];
                }


                try {
                    $collections = [];
                    foreach ($this->db->listCollections() as $collectionInfo) {
                        $collections[] = $collectionInfo->getName();
                    }

                    if (!in_array('reports', $collections)) {
                        secure_log("Creating reports collection in submit_report", "info");
                        $this->db->createCollection('reports');
                        $this->db->reports->createIndex(['_id' => 1], ['unique' => true]);
                        $this->db->reports->createIndex(['status' => 1]);
                        $this->db->reports->createIndex(['created_at' => -1]);
                        $this->db->reports->createIndex(['offender_name' => 1]);
                    }
                } catch (Exception $e) {
                    secure_log("Error checking/creating reports collection in submit_report: " . $e->getMessage(), "error");
                    return [
                        'error' => 'Database error: Unable to create reports collection',
                        'success' => false
                    ];
                }


                $this->reports_collection = $this->db->reports;
                secure_log("Reports collection initialized in submit_report", "info");
            } catch (Exception $e) {
                secure_log("Failed to initialize reports collection in submit_report: " . $e->getMessage(), "error");
                return [
                    'error' => 'Database error: Unable to initialize reports collection',
                    'success' => false
                ];
            }
        }


        $report_id = bin2hex(random_bytes(4));


        $offender_name = validate($offender_name, 'playername', [
            'required' => true
        ]);

        $rule_broken = validate($rule_broken, 'enum', [
            'required' => true,
            'allowed_values' => [
                'Hacking/Cheating', 'Kill Farming', 'Punishment Evading', 'Harassment/Bullying',
                'Inappropriate Content', 'Inappropriate Language', 'Hate Speech', 'Scamming',
                'Exploiting Bugs', 'Advertising', 'Other'
            ]
        ]);

        $evidence = validate($evidence, 'text', [
            'required' => true,
            'max_length' => 1000,
            'min_length' => 15
        ]);

        $reporters_email = validate($reporters_email, 'email', [
            'required' => true
        ]);


        if (!$offender_name || !$rule_broken || !$evidence || !$reporters_email) {
            return [
                'error' => 'Missing or invalid required fields',
                'success' => false
            ];
        }


        $now = new DateTime();
        $now->setTimezone(new DateTimeZone('America/New_York')); // Convert to Eastern Time


        try {
            $mongoDate = new UTCDateTime($now->getTimestamp() * 1000); // Convert to milliseconds
        } catch (Exception $e) {
            secure_log("Error creating MongoDB UTCDateTime: " . $e->getMessage(), "error");


            try {
                $mongoDate = new UTCDateTime(new DateTime());
                secure_log("Created MongoDB UTCDateTime using alternative approach", "info");
            } catch (Exception $e2) {
                secure_log("Error creating MongoDB UTCDateTime (alternative approach): " . $e2->getMessage(), "error");


                $mongoDate = new UTCDateTime(round(microtime(true) * 1000));
                secure_log("Created MongoDB UTCDateTime using microtime", "info");
            }
        }


        $formatted_et_timestamp = $now->format('F d, Y \a\t h:i A') . ' ET';


        secure_log("MongoDB date created: " . json_encode($mongoDate), "debug");


        $priority = 4; // Default to low priority


        $priority_map = [
            'Hacking/Cheating' => 1, // High
            'Punishment Evading' => 1, // High
            'Harassment/Bullying' => 1, // High
            'Hate Speech' => 1, // High
            'Inappropriate Content' => 1, // High
            'Inappropriate Language' => 2, // Urgent
            'Exploiting Bugs' => 2, // Urgent
            'Kill Farming' => 3, // Medium
            'Scamming' => 4, // Low
            'Advertising' => 4, // Low
            'Other' => 4 // Low
        ];


        if (isset($priority_map[$rule_broken])) {
            $priority = $priority_map[$rule_broken];
        }


        $report_doc = [
            '_id' => $report_id,
            'offender_name' => $offender_name,
            'rule_broken' => $rule_broken,
            'evidence' => $evidence,
            'reporters_email' => $reporters_email,
            'reporter_name' => $reporter_name, // Discord username
            'discord_id' => $discord_id, // Discord ID for portal linking
            'status' => 'Pending',
            'priority' => $priority, // Add priority field
            'created_at' => $mongoDate,
            'updated_at' => $mongoDate,
            'created_at_formatted' => $formatted_et_timestamp
        ];


        $debug_doc = $report_doc;
        unset($debug_doc['reporters_email']);
        unset($debug_doc['evidence']);
        secure_log("Report document structure: " . json_encode($debug_doc), "debug");


        if (!$this->reports_collection) {
            secure_log("Reports collection is not initialized", "error");


            try {
                $this->reports_collection = $this->db->reports;
                secure_log("Initialized reports collection", "info");
            } catch (Exception $e) {
                secure_log("Failed to initialize reports collection: " . $e->getMessage(), "error");
                return [
                    'error' => 'Database configuration error',
                    'success' => false
                ];
            }
        }


        if (!isset($this->reports_collection) || $this->reports_collection === null) {
            secure_log("Reports collection still not initialized before insert attempt", "error");


            try {

                $mongo_uri = 'mongodb://localhost:27017';
                $db_name = 'mmc';


                $options = [
                    'connectTimeoutMS' => 10000,
                    'serverSelectionTimeoutMS' => 10000,
                    'retryWrites' => true,
                    'socketTimeoutMS' => 60000,
                    'ssl' => false
                ];

                $client = new Client($mongo_uri, $options);
                $db = $client->selectDatabase($db_name);


                $collections = [];
                foreach ($db->listCollections() as $collectionInfo) {
                    $collections[] = $collectionInfo->getName();
                }

                if (!in_array('reports', $collections)) {
                    secure_log("Creating reports collection with direct connection", "info");
                    $db->createCollection('reports');
                    $db->reports->createIndex(['_id' => 1], ['unique' => true]);
                    $db->reports->createIndex(['status' => 1]);
                    $db->reports->createIndex(['created_at' => -1]);
                    $db->reports->createIndex(['offender_name' => 1]);
                }


                $this->reports_collection = $db->reports;
                secure_log("Successfully initialized reports collection with direct connection", "info");
            } catch (Exception $e) {
                secure_log("Final attempt to initialize reports collection failed: " . $e->getMessage(), "error");
                return [
                    'error' => 'Database configuration error: Reports collection not available',
                    'success' => false
                ];
            }
        }


        try {

            $debug_doc = $report_doc;
            unset($debug_doc['reporters_email']);
            secure_log("Submitting report: " . json_encode($debug_doc), "info");


            try {
                secure_log("Attempting to insert document into reports collection", "info");
                $result = $this->reports_collection->insertOne($report_doc);
                secure_log("Insert operation completed", "info");
            } catch (Exception $insert_error) {
                secure_log("Error during insertOne operation: " . $insert_error->getMessage(), "error");
                secure_log("Error stack trace: " . $insert_error->getTraceAsString(), "error");


                try {
                    secure_log("Trying simpler insert approach", "info");

                    $priority = 4; // Default to low priority


                    $priority_map = [
                        'Hacking/Cheating' => 1, // High
                        'Punishment Evading' => 1, // High
                        'Harassment/Bullying' => 1, // High
                        'Hate Speech' => 1, // High
                        'Inappropriate Content' => 1, // High
                        'Inappropriate Language' => 2, // Urgent
                        'Exploiting Bugs' => 2, // Urgent
                        'Kill Farming' => 3, // Medium
                        'Scamming' => 4, // Low
                        'Advertising' => 4, // Low
                        'Other' => 4 // Low
                    ];


                    if (isset($priority_map[$rule_broken])) {
                        $priority = $priority_map[$rule_broken];
                    }

                    $result = $this->reports_collection->insertOne([
                        '_id' => $report_id,
                        'offender_name' => $offender_name,
                        'rule_broken' => $rule_broken,
                        'evidence' => $evidence,
                        'reporters_email' => $reporters_email,
                        'reporter_name' => $reporter_name,
                        'discord_id' => $discord_id,
                        'status' => 'Pending',
                        'priority' => $priority, // Add priority field
                        'created_at' => new UTCDateTime()
                    ]);
                    secure_log("Simpler insert operation completed", "info");
                } catch (Exception $simple_insert_error) {
                    secure_log("Simpler insert approach also failed: " . $simple_insert_error->getMessage(), "error");
                    throw $simple_insert_error; // Re-throw to be caught by the outer try-catch
                }
            }

            if (!$result->isAcknowledged()) {
                throw new Exception("Write operation not acknowledged");
            }

            secure_log("Report inserted successfully with ID: " . $report_id, "info");

            // Send Discord webhook notification to staff
            try {
                $this->send_report_discord_webhook($report_id, [
                    'offender_name' => $offender_name,
                    'rule_broken' => $rule_broken,
                    'reporter_name' => $reporter_name,
                    'priority' => $priority,
                    'created_at_formatted' => $formatted_et_timestamp
                ]);
                secure_log("Discord webhook sent for report: " . $report_id, "info");
            } catch (Exception $webhook_error) {
                // Don't fail the entire operation if webhook fails
                secure_log("Error sending Discord webhook: " . $webhook_error->getMessage(), "error");
            }

            // Send confirmation email
            try {
                $this->send_report_confirmation_email($report_id, [
                    'offender_name' => $offender_name,
                    'rule_broken' => $rule_broken,
                    'reporters_email' => $reporters_email
                ]);
                secure_log("Confirmation email sent for report: " . $report_id, "info");
            } catch (Exception $email_error) {
                // Don't fail the entire operation if email fails
                secure_log("Error sending confirmation email: " . $email_error->getMessage(), "error");
            }

            return [
                'success' => true,
                'message' => 'Report submitted successfully',
                'report_id' => $report_id
            ];
        } catch (Exception $e) {
            secure_log("MongoDB Insert Error: " . $e->getMessage(), "error");
            secure_log("Error stack trace: " . $e->getTraceAsString(), "error");


            $error_message = $e->getMessage();


            if (strpos($error_message, 'duplicate key error') !== false) {
                secure_log("Duplicate report ID detected. Generating a new ID and retrying.", "warning");


                $report_id = bin2hex(random_bytes(4));
                $report_doc['_id'] = $report_id;

                try {

                    if (!isset($report_doc['priority'])) {

                        $priority = 4; // Default to low priority


                        $priority_map = [
                            'Hacking/Cheating' => 1, // High
                            'Punishment Evading' => 1, // High
                            'Harassment/Bullying' => 1, // High
                            'Hate Speech' => 1, // High
                            'Inappropriate Content' => 1, // High
                            'Inappropriate Language' => 2, // Urgent
                            'Exploiting Bugs' => 2, // Urgent
                            'Kill Farming' => 3, // Medium
                            'Scamming' => 4, // Low
                            'Advertising' => 4, // Low
                            'Other' => 4 // Low
                        ];


                        if (isset($priority_map[$rule_broken])) {
                            $priority = $priority_map[$rule_broken];
                        }

                        $report_doc['priority'] = $priority;
                    }

                    $result = $this->reports_collection->insertOne($report_doc);

                    if ($result->isAcknowledged()) {
                        secure_log("Report inserted successfully with new ID: " . $report_id, "info");

                        // Send Discord webhook notification to staff
                        try {
                            $this->send_report_discord_webhook($report_id, [
                                'offender_name' => $offender_name,
                                'rule_broken' => $rule_broken,
                                'reporter_name' => $reporter_name,
                                'priority' => $priority,
                                'created_at_formatted' => $formatted_et_timestamp
                            ]);
                            secure_log("Discord webhook sent for report (retry): " . $report_id, "info");
                        } catch (Exception $webhook_error) {
                            // Don't fail the entire operation if webhook fails
                            secure_log("Error sending Discord webhook (retry): " . $webhook_error->getMessage(), "error");
                        }

                        // Send confirmation email
                        $this->send_report_confirmation_email($report_id, [
                            'offender_name' => $offender_name,
                            'rule_broken' => $rule_broken,
                            'reporters_email' => $reporters_email
                        ]);

                        return [
                            'success' => true,
                            'message' => 'Report submitted successfully',
                            'report_id' => $report_id
                        ];
                    }
                } catch (Exception $retry_error) {
                    secure_log("Retry failed: " . $retry_error->getMessage(), "error");
                    secure_log("Retry error stack trace: " . $retry_error->getTraceAsString(), "error");
                }
            }


            if (strpos($error_message, 'Failed to connect') !== false ||
                strpos($error_message, 'No servers') !== false ||
                strpos($error_message, 'connection') !== false) {
                return [
                    'error' => 'Database connection error: Unable to connect to the database server',
                    'success' => false
                ];
            }


            if (strpos($error_message, 'Authentication failed') !== false ||
                strpos($error_message, 'auth') !== false) {
                return [
                    'error' => 'Database authentication error: Unable to authenticate with the database',
                    'success' => false
                ];
            }


            if (strpos($error_message, 'not authorized') !== false ||
                strpos($error_message, 'permission') !== false) {
                return [
                    'error' => 'Database permission error: Insufficient permissions to submit report',
                    'success' => false
                ];
            }

            return [
                'error' => 'Database error: Unable to submit report. Please try again later.',
                'success' => false
            ];
        }
    }

    /**
     * Send confirmation email (mimics the Python function)
     */
    private function send_report_confirmation_email($report_id, $data) {

        try {
            $env = parse_config_file_safe('/etc/massacremc/config/api.env');

            if (empty($env['MAILGUN_API_KEY']) || empty($env['MAILGUN_DOMAIN']) || empty($env['SENDER_EMAIL'])) {
                error_log("Email configuration incomplete, skipping confirmation email");
                return false;
            }


            $receiver_email = $data['reporters_email'];
            $email_subject = "🚨 MassacreMC - Report Confirmation #" . $report_id;


            $now = new DateTime();
            $now->setTimezone(new DateTimeZone('America/New_York'));
            $timestamp = $now->format('F d, Y \a\t h:i A') . ' ET';
            $currentYear = $now->format('Y');

            $html_body = "
            <!DOCTYPE html>
            <html lang='en'>
                <head>
                    <meta charset='UTF-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    <title>Report Confirmation</title>
                </head>
                <body style='margin: 0; padding: 0; font-family: \"Segoe UI\", Arial, sans-serif; background-color: #121212; color: #e0e0e0;'>
                    <div style='max-width: 600px; margin: 20px auto; background: #1e1e1e; border: 1px solid #333; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.3);'>
                        <!-- Header with Logo -->
                        <div style='background: linear-gradient(135deg, #d32f2f, #b71c1c); color: white; padding: 30px; text-align: center;'>
                            <img src='https://portal.massacremc.net/img/logo.png' alt='MassacreMC Logo' style='max-width: 150px; margin-bottom: 15px;'>
                            <h1 style='margin: 0; font-size: 28px; letter-spacing: 0.5px; text-transform: uppercase;'>Report Confirmation</h1>
                            <p style='margin: 10px 0 0; font-size: 16px; opacity: 0.9;'>Reference: #{$report_id}</p>
                        </div>

                        <!-- Main Content -->
                        <div style='padding: 35px; background-color: #1e1e1e; border-bottom: 1px solid #333;'>
                            <h2 style='color: #ff5252; font-size: 22px; margin-top: 0; margin-bottom: 25px; border-bottom: 2px solid #333; padding-bottom: 10px;'>Report Details</h2>

                            <div style='background: #252525; border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #ff5252;'>
                                <table style='width: 100%; border-collapse: collapse;'>
                                    <tr>
                                        <td style='padding: 10px 0; border-bottom: 1px solid #333;'>
                                            <strong style='color: #ff5252; display: inline-block; width: 140px;'>Report ID:</strong>
                                            <span style='color: #e0e0e0; font-family: monospace; font-size: 15px;'>{$report_id}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style='padding: 10px 0; border-bottom: 1px solid #333;'>
                                            <strong style='color: #ff5252; display: inline-block; width: 140px;'>Reported Player:</strong>
                                            <span style='color: #e0e0e0;'>{$data['offender_name']}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style='padding: 10px 0; border-bottom: 1px solid #333;'>
                                            <strong style='color: #ff5252; display: inline-block; width: 140px;'>Rule Violation:</strong>
                                            <span style='color: #e0e0e0;'>{$data['rule_broken']}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style='padding: 10px 0; border-bottom: 1px solid #333;'>
                                            <strong style='color: #ff5252; display: inline-block; width: 140px;'>Status:</strong>
                                            <span style='display: inline-block; background: rgba(255, 87, 34, 0.2); color: #ff5722; font-weight: bold; padding: 5px 10px; border-radius: 4px; font-size: 14px;'>UNDER REVIEW</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style='padding: 10px 0;'>
                                            <strong style='color: #ff5252; display: inline-block; width: 140px;'>Submitted:</strong>
                                            <span style='color: #e0e0e0;'>{$timestamp}</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div style='background: #252525; border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #69f0ae;'>
                                <h3 style='color: #69f0ae; margin-top: 0; font-size: 18px;'>What happens next?</h3>
                                <p style='color: #e0e0e0; line-height: 1.6; font-size: 15px; margin-bottom: 0;'>
                                    Thank you for submitting your report. Our moderation team will investigate the reported behavior and take appropriate action according to our server rules. You will receive a notification via email once your report has been processed.
                                </p>
                            </div>

                            <div style='background: #252525; border-radius: 8px; padding: 20px; border-left: 4px solid #ffab40;'>
                                <h3 style='color: #ffab40; margin-top: 0; font-size: 18px;'>Important Information</h3>
                                <ul style='color: #e0e0e0; line-height: 1.6; padding-left: 20px;'>
                                    <li>Reports are typically processed within 24-48 hours</li>
                                    <li>Additional evidence may be requested if needed</li>
                                    <li>False reports may result in penalties for the reporter</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Footer -->
                        <div style='background: linear-gradient(135deg, #7b1fa2, #4a148c); padding: 40px; text-align: center; border-top: 3px solid #7b1fa2;'>
                            <h3 style='color: #ffffff; margin-top: 0; font-size: 24px; margin-bottom: 20px; font-weight: 600; letter-spacing: 0.5px;'>Connect With Us</h3>

                            <div style='margin: 30px 0; text-align: left; max-width: 400px; margin-left: auto; margin-right: auto;'>
                                <ul style='list-style-type: none; padding: 0; margin: 0;'>
                                    <li style='margin-bottom: 12px; line-height: 1.6;'>
                                        <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Server IP:</span>
                                        <span style='color: #43A047; font-weight: 600; font-family: monospace;'>play.massacremc.net</span>
                                    </li>
                                    <li style='margin-bottom: 12px; line-height: 1.6;'>
                                        <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Discord:</span>
                                        <a href='https://discord.gg/mBjd5cXSxR' style='color: #5865F2; font-weight: 600; text-decoration: none; font-family: monospace;'>discord.gg/mBjd5cXSxR</a>
                                    </li>
                                    <li style='margin-bottom: 12px; line-height: 1.6;'>
                                        <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Website:</span>
                                        <a href='https://portal.massacremc.net' style='color: #7b1fa2; font-weight: 600; text-decoration: none; font-family: monospace;'>portal.massacremc.net</a>
                                    </li>
                                </ul>
                            </div>

                            <div style='margin-top: 35px; padding-top: 25px; border-top: 1px solid rgba(255,255,255,0.2);'>
                                <p style='margin: 0 0 15px; color: #ffffff; font-size: 16px;'>Need assistance? Email us at <a href='mailto:<EMAIL>' style='color: #ffffff; text-decoration: none; font-weight: 700; border-bottom: 2px solid rgba(255,255,255,0.4); padding-bottom: 2px;'><EMAIL></a></p>

                                <div style='background-color: rgba(0,0,0,0.2); border-radius: 8px; padding: 15px; margin-top: 20px; display: inline-block;'>
                                    <p style='margin: 0 0 5px; font-size: 13px; color: rgba(255,255,255,0.7);'>This is an automated message. Please do not reply to this email.</p>
                                    <p style='margin: 5px 0 0; font-size: 13px; color: rgba(255,255,255,0.7);'>© {$currentYear} MassacreMC. All rights reserved.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </body>
            </html>
            ";


            $ch = curl_init("https://api.mailgun.net/v3/{$env['MAILGUN_DOMAIN']}/messages");
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_USERPWD, "api:{$env['MAILGUN_API_KEY']}");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);

            // Use certificate file for SSL verification
            $cert_file = __DIR__ . '/certs/massacremc.net.pem';
            if (file_exists($cert_file)) {
                error_log("Using certificate file for email: " . $cert_file);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                curl_setopt($ch, CURLOPT_CAINFO, $cert_file);
            } else {
                // Try alternative locations
                $alt_cert_files = [
                    '/home/<USER>/website/certs/massacremc.net.pem',
                    '/etc/ssl/certs/ca-certificates.crt'
                ];

                $cert_found = false;
                foreach ($alt_cert_files as $alt_file) {
                    if (file_exists($alt_file)) {
                        error_log("Using alternative certificate file: " . $alt_file);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                        curl_setopt($ch, CURLOPT_CAINFO, $alt_file);
                        $cert_found = true;
                        break;
                    }
                }

                if (!$cert_found) {
                    error_log("No certificate file found, disabling SSL verification for email");
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                }
            }

            curl_setopt($ch, CURLOPT_POSTFIELDS, [
                'from' => "MassacreMC Moderation Team <{$env['SENDER_EMAIL']}>",
                'to' => $receiver_email,
                'subject' => $email_subject,
                'html' => $html_body
            ]);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            $curl_errno = curl_errno($ch);
            curl_close($ch);

            if ($curl_error) {
                error_log("cURL error sending email: {$curl_error} (Error code: {$curl_errno})");

                // If it's an SSL certificate error, try again with SSL verification disabled
                if ($curl_errno == 60 || $curl_errno == 77) { // SSL certificate problem
                    error_log("Retrying email with SSL verification disabled");

                    $ch = curl_init("https://api.mailgun.net/v3/{$env['MAILGUN_DOMAIN']}/messages");
                    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                    curl_setopt($ch, CURLOPT_USERPWD, "api:{$env['MAILGUN_API_KEY']}");
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, [
                        'from' => "MassacreMC Moderation Team <{$env['SENDER_EMAIL']}>",
                        'to' => $receiver_email,
                        'subject' => $email_subject,
                        'html' => $html_body
                    ]);

                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    $curl_error = curl_error($ch);
                    curl_close($ch);

                    if ($curl_error) {
                        error_log("Retry failed with cURL error: {$curl_error}");
                        return false;
                    }
                } else {
                    return false;
                }
            }

            if ($http_code === 200) {
                error_log("Email sent successfully to {$receiver_email} via Mailgun");
                return true;
            } else {
                error_log("Mailgun API error: HTTP {$http_code} - Response: {$response}");
                return false;
            }
        } catch (Exception $e) {
            error_log("Error sending email: " . $e->getMessage());
            return false;
        }
    }

    public function get_leaderboard($filter_type = 'kills', $sort = 'desc') {
        try {

            $filter_type = sanitize_mongo_input($filter_type, 'string', [
                'max_length' => 20,
                'allowed_values' => ['kills', 'balance', 'kdr', 'deaths', 'killstreaks', 'bounties']
            ]);

            $sort = sanitize_mongo_input($sort, 'string', [
                'max_length' => 10,
                'allowed_values' => ['asc', 'desc']
            ]);


            if ($filter_type === null) {
                return ['error' => 'Invalid type parameter', 'status' => 400];
            }


            if ($sort === null) {
                $sort = 'desc'; // Default to desc if invalid
            }

            $sort_order = ($sort === 'desc') ? -1 : 1; // MongoDB sort order
            $leaderboard_data = [];


            if ($filter_type === 'kills') {
                $projection = ['username' => 1, 'faction.stats.kills' => 1];
                $cursor = $this->db->player_data->find([], [
                    'projection' => $projection,
                    'sort' => ['faction.stats.kills' => $sort_order]
                ]);

                foreach ($cursor as $item) {
                    if (isset($item['username'])) {
                        $username = $item['username'];
                        $kills = $item['faction']['stats']['kills'] ?? 0;
                        $leaderboard_data[] = [
                            'username' => $username,
                            'kills' => (int)$kills
                        ];
                    }
                }
            }


            elseif ($filter_type === 'killstreaks') {
                $projection = ['username' => 1, 'faction.stats.killstreak' => 1];
                $cursor = $this->db->player_data->find([], [
                    'projection' => $projection,
                    'sort' => ['faction.stats.killstreak' => $sort_order]
                ]);

                foreach ($cursor as $item) {
                    if (isset($item['username'])) {
                        $username = $item['username'];
                        $killstreak = $item['faction']['stats']['killstreak'] ?? 0;
                        $leaderboard_data[] = [
                            'username' => $username,
                            'killstreak' => (int)$killstreak
                        ];
                    }
                }
            }


            elseif ($filter_type === 'balance') {
                $projection = ['username' => 1, 'faction.stats.doubloons' => 1];
                $cursor = $this->db->player_data->find([], [
                    'projection' => $projection,
                    'sort' => ['faction.stats.doubloons' => $sort_order]
                ]);

                foreach ($cursor as $item) {
                    if (isset($item['username'])) {
                        $username = $item['username'];
                        $balance = $item['faction']['stats']['doubloons'] ?? 0;
                        $leaderboard_data[] = [
                            'username' => $username,
                            'balance' => (int)$balance
                        ];
                    }
                }
            }


            elseif ($filter_type === 'bounties') {
                $projection = ['username' => 1, 'xuid' => 1, 'bounty' => 1, 'faction.bounties' => 1, 'bounties' => 1];
                $cursor = $this->db->player_data->find([], [
                    'projection' => $projection
                ]);

                foreach ($cursor as $item) {
                    if (isset($item['username'])) {
                        $username = $item['username'];
                        $player_xuid = isset($item['xuid']) ? $item['xuid'] : '';
                        $bounty_amount = 0;

                        // Check for bounty directly in player document (highest priority)
                        if (isset($item['bounty']) && is_numeric($item['bounty']) && (int)$item['bounty'] > 0) {
                            $bounty_amount = (int)$item['bounty'];
                        }

                        // Check for bounties in player's faction.bounties
                        if ($bounty_amount === 0 && isset($item['faction']['bounties'])) {
                            if (is_array($item['faction']['bounties'])) {
                                // Check if the player's own UUID is in the bounties
                                if (isset($item['faction']['bounties'][$player_uuid]) &&
                                    is_numeric($item['faction']['bounties'][$player_uuid])) {
                                    $bounty_amount = (int)$item['faction']['bounties'][$player_uuid];
                                }
                            } elseif (is_object($item['faction']['bounties']) &&
                                    method_exists($item['faction']['bounties'], 'getArrayCopy')) {
                                $bounties_array = $item['faction']['bounties']->getArrayCopy();

                                if (isset($bounties_array[$player_uuid]) && is_numeric($bounties_array[$player_uuid])) {
                                    $bounty_amount = (int)$bounties_array[$player_uuid];
                                }
                            }
                        }

                        // Check for bounties in player's bounties field
                        if ($bounty_amount === 0 && isset($item['bounties'])) {
                            if (is_array($item['bounties'])) {
                                if (isset($item['bounties'][$player_uuid]) && is_numeric($item['bounties'][$player_uuid])) {
                                    $bounty_amount = (int)$item['bounties'][$player_uuid];
                                }
                            } elseif (is_object($item['bounties']) && method_exists($item['bounties'], 'getArrayCopy')) {
                                $bounties_array = $item['bounties']->getArrayCopy();

                                if (isset($bounties_array[$player_uuid]) && is_numeric($bounties_array[$player_uuid])) {
                                    $bounty_amount = (int)$bounties_array[$player_uuid];
                                }
                            }
                        }

                        $leaderboard_data[] = [
                            'username' => $username,
                            'bounties' => $bounty_amount
                        ];
                    }
                }

                // Check the global bounties collection and other players for remaining bounties
                foreach ($leaderboard_data as &$player_data) {
                    $username = $player_data['username'];
                    
                    // Get player UUID if we don't already have it
                    $playerQuery = create_mongo_regex_query('username', $username, false, 'exact');
                    $player = $this->db->player_data->findOne($playerQuery, ['projection' => ['uuid' => 1]]);
                    $player_uuid = isset($player['uuid']) ? $player['uuid'] : '';
                    
                    if (!empty($player_uuid) && $player_data['bounties'] === 0) {
                        // Check global bounties collection
                        try {
                            $bounties_doc = $this->db->bounties->findOne(
                                ['players.' . $player_uuid => ['$exists' => true]],
                                ['projection' => ['players.' . $player_uuid => 1]]
                            );

                            if ($bounties_doc && isset($bounties_doc['players']) && isset($bounties_doc['players'][$player_uuid])) {
                                $player_data['bounties'] = (int)$bounties_doc['players'][$player_uuid];
                            }
                        } catch (Exception $e) {
                            error_log("Error querying bounties collection for leaderboard: " . $e->getMessage());
                        }

                        // Check for bounties in other player documents if still zero
                        if ($player_data['bounties'] === 0) {
                            try {
                                $other_players = $this->db->player_data->find(
                                    ['faction.bounties.' . $player_uuid => ['$exists' => true]],
                                    ['projection' => ['faction.bounties.' . $player_uuid => 1]]
                                );

                                foreach ($other_players as $other_player) {
                                    if (isset($other_player['faction']['bounties'][$player_uuid])) {
                                        $player_data['bounties'] = (int)$other_player['faction']['bounties'][$player_uuid];
                                        break;
                                    }
                                }
                            } catch (Exception $e) {
                                error_log("Error querying other players for bounties in leaderboard: " . $e->getMessage());
                            }
                        }
                    }
                }
                
                usort($leaderboard_data, function ($a, $b) use ($sort_order) {
                    if ($sort_order === -1) {
                        return $b['bounties'] - $a['bounties'];
                    } else {
                        return $a['bounties'] - $b['bounties'];
                    }
                });
            }

            elseif ($filter_type === 'deaths') {
                $projection = ['username' => 1, 'faction.stats.deaths' => 1];
                $cursor = $this->db->player_data->find([], [
                    'projection' => $projection,
                    'sort' => ['faction.stats.deaths' => $sort_order]
                ]);

                foreach ($cursor as $item) {
                    if (isset($item['username'])) {
                        $username = $item['username'];
                        $deaths = $item['faction']['stats']['deaths'] ?? 0;
                        $leaderboard_data[] = [
                            'username' => $username,
                            'deaths' => (int)$deaths
                        ];
                    }
                }
            }


            elseif ($filter_type === 'kdr') {
                $projection = ['username' => 1, 'faction.stats.kills' => 1, 'faction.stats.deaths' => 1];
                $cursor = $this->db->player_data->find([], ['projection' => $projection]);

                if ($cursor) {
                    foreach ($cursor as $player) {
                        $username = $player['username'] ?? null;
                        $stats = $player['faction']['stats'] ?? [];
                        $kills = $stats['kills'] ?? 0;
                        $deaths = $stats['deaths'] ?? 0;
                        $kdr = ($deaths === 0) ? $kills : $kills / $deaths;

                        if ($username) {
                            $leaderboard_data[] = [
                                'username' => $username,
                                'kills' => (int)$kills,
                                'deaths' => (int)$deaths,
                                'kdr' => (float)$kdr
                            ];
                        }
                    }
                }


                usort($leaderboard_data, function ($a, $b) use ($sort_order) {
                    return $sort_order * ($b['kdr'] <=> $a['kdr']);
                });
            }

            return $leaderboard_data;
        } catch (Exception $e) {
            error_log("Error fetching leaderboard: " . $e->getMessage());
            return ['error' => 'An error occurred: ' . $e->getMessage(), 'status' => 500];
        }
    }

    public function get_faction_leaderboard($filter_type = 'strength', $sort = 'desc') {
        try {

            $filter_type = sanitize_mongo_input($filter_type, 'string', [
                'max_length' => 20,
                'allowed_values' => ['balance', 'kills', 'strength']
            ]);

            $sort = sanitize_mongo_input($sort, 'string', [
                'max_length' => 10,
                'allowed_values' => ['asc', 'desc']
            ]);


            if ($filter_type === null) {
                return ['error' => 'Invalid filter type'];
            }


            if ($sort === null) {
                $sort = 'desc'; // Default to desc if invalid
            }


            $collection = $this->db->faction_data;
            $sortDirection = ($sort === 'desc') ? -1 : 1;


            $fieldMap = [
                'balance' => 'bankdoubloons',
                'strength' => 'strength',
                'kills' => 'kills'
            ];

            $field = $fieldMap[$filter_type] ?? $filter_type;


            $cursor = $collection->find(
                [], // Empty filter to get all documents
                [
                    'projection' => [
                        'name' => 1,
                        $field => 1
                    ],
                    'sort' => [$field => $sortDirection],
                    'limit' => 100 // Limit to top 100 factions
                ]
            );

            $leaderboard_data = [];
            foreach ($cursor as $document) {
                $faction = [
                    'name' => $document['name'] ?? 'Unknown',
                ];


                switch ($filter_type) {
                    case 'balance':
                        $faction['balance'] = isset($document['bankdoubloons']) ? (int)$document['bankdoubloons'] : 0;
                        break;
                    case 'kills':
                        $faction['kills'] = isset($document['kills']) ? (int)$document['kills'] : 0;
                        break;
                    case 'strength':
                        $faction['strength'] = isset($document['strength']) ? (float)$document['strength'] : 0;
                        break;
                }

                $leaderboard_data[] = $faction;
            }

            return $leaderboard_data;
        } catch (Exception $e) {
            error_log("Error fetching faction leaderboard: " . $e->getMessage());
            return ['error' => 'Database error'];
        }
    }

    /**
     * Get report counts with optimized query
     *
     * @return array Report counts with total, pending, and timestamp
     */
    public function get_report_counts() {
        try {

            $pipeline = [
                [
                    '$facet' => [
                        'total' => [
                            ['$count' => 'count']
                        ],
                        'pending' => [
                            ['$match' => ['status' => 'Pending']],
                            ['$count' => 'count']
                        ]
                    ]
                ]
            ];

            $result = $this->reports_collection->aggregate($pipeline)->toArray();


            $total = isset($result[0]['total'][0]['count']) ? $result[0]['total'][0]['count'] : 0;
            $pending = isset($result[0]['pending'][0]['count']) ? $result[0]['pending'][0]['count'] : 0;


            $now = new DateTime();
            $now->setTimezone(new DateTimeZone('America/New_York'));

            return [
                'total' => $total,
                'pending' => $pending,
                'timestamp' => new UTCDateTime($now->getTimestamp() * 1000),
                'formatted_time' => $now->format('F d, Y \a\t h:i A') . ' ET'
            ];
        } catch (Exception $e) {
            secure_log("Error fetching report counts: " . $e->getMessage(), "error");
            return [
                'total' => 0,
                'pending' => 0,
                'error' => 'Failed to retrieve counts'
            ];
        }
    }

    /**
     * Get player UUID from username for Go API integration
     */
    public function get_player_uuid($username) {
        try {
            $username = sanitize_mongo_input($username, 'string', [
                'max_length' => 100,
                'allow_empty' => false
            ]);

            if ($username === null) {
                return ['error' => 'Invalid username', 'status' => 400];
            }

            // Search for player by username
            $playerQuery = create_mongo_regex_query('username', $username, false, 'exact');
            $player = $this->db->player_data->findOne($playerQuery, [
                'projection' => ['uuid' => 1, 'username' => 1]
            ]);

            if (!$player) {
                return ['error' => 'Player not found', 'status' => 404];
            }

            // Convert binary UUID to string format
            $uuid_binary = $player['uuid'] ?? null;
            if ($uuid_binary && $uuid_binary instanceof MongoDB\BSON\Binary) {
                // Convert binary UUID to hex string format
                $uuid_hex = bin2hex($uuid_binary->getData());
                // Format as standard UUID (8-4-4-4-12)
                $uuid_formatted = sprintf(
                    '%s-%s-%s-%s-%s',
                    substr($uuid_hex, 0, 8),
                    substr($uuid_hex, 8, 4),
                    substr($uuid_hex, 12, 4),
                    substr($uuid_hex, 16, 4),
                    substr($uuid_hex, 20, 12)
                );

                return [
                    'success' => true,
                    'username' => $player['username'],
                    'uuid' => $uuid_formatted
                ];
            }

            return ['error' => 'UUID not found for player', 'status' => 404];

        } catch (Exception $e) {
            error_log("Error getting player UUID: " . $e->getMessage());
            return ['error' => 'Database error', 'status' => 500];
        }
    }

    public function search_player_or_faction($query) {
        if (!$query) {
            return ['error' => 'Search query is required'];
        }

        try {

            $query = sanitize_mongo_input($query, 'string', [
                'max_length' => 100,
                'allow_empty' => false
            ]);

            if ($query === null) {
                return ['error' => 'Invalid search query'];
            }

            error_log("Searching for: " . $query);


            $playerExactQuery = create_mongo_regex_query('username', $query, false, 'exact');
            error_log("Player exact query: " . json_encode($playerExactQuery));
            $player = $this->db->player_data->findOne($playerExactQuery);

            if (!$player) {
                $playerPartialQuery = create_mongo_regex_query('username', $query, false, 'contains');
                error_log("Player partial query: " . json_encode($playerPartialQuery));
                $player = $this->db->player_data->findOne($playerPartialQuery);
            }


            $faction = null;
            $factionExactQuery = create_mongo_regex_query('name', $query, false, 'exact');
            error_log("Faction exact query: " . json_encode($factionExactQuery));
            $faction = $this->db->faction_data->findOne($factionExactQuery);

            if (!$faction) {
                $factionPartialQuery = create_mongo_regex_query('name', $query, false, 'contains');
                error_log("Faction partial query: " . json_encode($factionPartialQuery));
                $faction = $this->db->faction_data->findOne($factionPartialQuery);
            }


            error_log("Search results - Player: " . ($player ? 'Found: ' . ($player['username'] ?? 'unnamed') : 'Not found') .
                      ", Faction: " . ($faction ? 'Found: ' . ($faction['name'] ?? 'unnamed') : 'Not found'));


            $response = [
                'player' => !empty($player),
                'faction' => !empty($faction),
                'query' => $query
            ];

            if ($player && !empty($player['username'])) {
                $response['player_name'] = $player['username'];

                // Add UUID for Go API integration
                $uuid_binary = $player['uuid'] ?? null;
                if ($uuid_binary && $uuid_binary instanceof MongoDB\BSON\Binary) {
                    $uuid_hex = bin2hex($uuid_binary->getData());
                    $response['player_uuid'] = sprintf(
                        '%s-%s-%s-%s-%s',
                        substr($uuid_hex, 0, 8),
                        substr($uuid_hex, 8, 4),
                        substr($uuid_hex, 12, 4),
                        substr($uuid_hex, 16, 4),
                        substr($uuid_hex, 20, 12)
                    );
                }
            }

            if ($faction && !empty($faction['name'])) {
                $response['faction_name'] = $faction['name'];
            }

            error_log("Response: " . json_encode($response));
            return $response;
        } catch (Exception $e) {
            error_log("Error during search: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return ['error' => 'Database error: ' . $e->getMessage()];
        }
    }

    /**
     * Get player punishment history from the unified punishments collection
     */
    public function get_player_punishments($username) {
        try {

            $username = sanitize_mongo_input($username, 'string', [
                'max_length' => 100,
                'allow_empty' => false
            ]);

            if ($username === null) {
                return [];
            }

            $punishments = [];


            $query = sanitize_mongo_query(['player_name' => $username]);
            $options = [
                'sort' => ['issued_at' => -1] // Sort by issued date, newest first
            ];

            $cursor = $this->db->punishments->find($query, $options);

            foreach ($cursor as $doc) {

                $has_expired = false;
                if (isset($doc['active']) && $doc['active'] === true && $this->has_punishment_expired($doc)) {
                    $has_expired = true;


                    try {
                        $this->db->punishments->updateOne(
                            ['punishment_id' => $doc['punishment_id']],
                            [
                                '$set' => [
                                    'active' => false,
                                    'expired' => true,
                                    'expired_at' => new UTCDateTime(time() * 1000)
                                ],
                                '$push' => [
                                    'history' => [
                                        'action' => 'expired',
                                        'timestamp' => new UTCDateTime(time() * 1000),
                                        'reason' => 'Punishment duration has elapsed'
                                    ]
                                ]
                            ]
                        );


                        $doc['active'] = false;
                        $doc['expired'] = true;
                        $doc['expired_at'] = new UTCDateTime(time() * 1000);

                        error_log("Marked punishment " . $doc['punishment_id'] . " as expired");
                    } catch (Exception $e) {
                        error_log("Error updating expired punishment status: " . $e->getMessage());

                    }
                }


                $punishmentType = strtoupper($doc['punishment_type'] ?? 'UNKNOWN');

                $punishment = [
                    'type' => $punishmentType,
                    'reason' => $doc['reason'] ?? 'No reason provided',
                    'date' => $doc['issued_at'] instanceof UTCDateTime ?
                             date('Y-m-d H:i:s', $doc['issued_at']->toDateTime()->getTimestamp()) :
                             (is_string($doc['issued_at']) ? $doc['issued_at'] : ''),
                    'punisher' => $doc['staff_name'] ?? 'Unknown Staff',
                    'active' => $has_expired ? false : ($doc['active'] ?? false),
                    'expired' => $has_expired || (isset($doc['expired']) && $doc['expired'] === true),
                    'punishment_id' => $doc['punishment_id'] ?? ''
                ];

                if (isset($doc['expires_at']) && $doc['expires_at']) {
                    $punishment['expires'] = $doc['expires_at'] instanceof UTCDateTime ?
                                           date('Y-m-d H:i:s', $doc['expires_at']->toDateTime()->getTimestamp()) :
                                           (is_string($doc['expires_at']) ? $doc['expires_at'] : '');
                }


                if (isset($doc['duration'])) {
                    $punishment['duration'] = $doc['duration'];
                }

                if (isset($doc['offense_type']) && isset($doc['offense_count'])) {
                    $punishment['offense_type'] = $doc['offense_type'];
                    $punishment['offense_count'] = $doc['offense_count'];
                }

                $punishments[] = $punishment;
            }

            return $punishments;
        } catch (Exception $e) {
            error_log("Error fetching player punishments: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Check if a punishment has naturally expired based on its expiration date
     * This specifically checks for time-based expiration, not manual removal
     *
     * @param array $punishment Punishment document
     * @return bool Whether the punishment has naturally expired due to time
     */
    private function has_punishment_expired($punishment) {

        if (!isset($punishment['expires_at']) || $punishment['expires_at'] === null) {
            return false;
        }


        $now = time();


        $expires_at = null;
        if ($punishment['expires_at'] instanceof UTCDateTime) {
            $expires_at = $punishment['expires_at']->toDateTime()->getTimestamp();
        } elseif (is_string($punishment['expires_at'])) {
            $expires_at = strtotime($punishment['expires_at']);
        }


        if ($expires_at === null || $expires_at === false) {
            return false;
        }


        return $now > $expires_at;
    }

    /**
     * Check if a punishment is inactive (either expired or manually removed)
     *
     * @param array $punishment Punishment document
     * @return bool Whether the punishment is inactive
     */
    private function is_punishment_inactive($punishment) {

        if (isset($punishment['active']) && $punishment['active'] === false) {
            return true;
        }


        if (isset($punishment['removed_at']) && $punishment['removed_at'] !== null) {
            return true;
        }


        return $this->has_punishment_expired($punishment);
    }

    /**
     * Send appeal confirmation email
     */
    private function send_appeal_confirmation_email($appeal_id, $data) {
        try {
            error_log("send_appeal_confirmation_email called with appeal_id: $appeal_id");

            if (!file_exists('/etc/massacremc/config/api.env')) {
                error_log("Email config file not found: /etc/massacremc/config/api.env");
                return false;
            }

            $env = parse_config_file_safe('/etc/massacremc/config/api.env');

            if (empty($env)) {
                error_log("Failed to parse email config file or file is empty");
                return false;
            }

            if (empty($env['MAILGUN_API_KEY']) || empty($env['MAILGUN_DOMAIN']) || empty($env['SENDER_EMAIL'])) {
                error_log("Missing email configuration - API Key: " . (empty($env['MAILGUN_API_KEY']) ? "NO" : "YES") .
                         ", Domain: " . (empty($env['MAILGUN_DOMAIN']) ? "NO" : "YES") .
                         ", Sender: " . (empty($env['SENDER_EMAIL']) ? "NO" : "YES"));
                return false;
            }

            error_log("Email configuration loaded successfully");


            $receiver_email = $data['email'];
            $email_subject = "🔍 MassacreMC - Appeal Confirmation #" . $appeal_id;


            $now = new DateTime();
            $now->setTimezone(new DateTimeZone('America/New_York'));
            $timestamp = $now->format('F d, Y \a\t h:i A') . ' ET';
            $currentYear = $now->format('Y');

            $html_body = "
            <!DOCTYPE html>
            <html lang='en'>
                <head>
                    <meta charset='UTF-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    <title>Appeal Confirmation</title>
                </head>
                <body style='margin: 0; padding: 0; font-family: \"Segoe UI\", Arial, sans-serif; background-color: #121212; color: #e0e0e0;'>
                    <div style='max-width: 600px; margin: 20px auto; background: #1e1e1e; border: 1px solid #333; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.3);'>
                        <!-- Header with Logo -->
                        <div style='background: linear-gradient(135deg, #7b1fa2, #4a148c); color: white; padding: 30px; text-align: center;'>
                            <img src='https://portal.massacremc.net/img/logo.png' alt='MassacreMC Logo' style='max-width: 150px; margin-bottom: 15px;'>
                            <h1 style='margin: 0; font-size: 28px; letter-spacing: 0.5px; text-transform: uppercase;'>Appeal Confirmation</h1>
                            <p style='margin: 10px 0 0; font-size: 16px; opacity: 0.9;'>Reference: #{$appeal_id}</p>
                        </div>

                        <!-- Main Content -->
                        <div style='padding: 35px; background-color: #1e1e1e; border-bottom: 1px solid #333;'>
                            <h2 style='color: #bb86fc; font-size: 22px; margin-top: 0; margin-bottom: 25px; border-bottom: 2px solid #333; padding-bottom: 10px;'>Appeal Details</h2>

                            <div style='background: #252525; border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #bb86fc;'>
                                <table style='width: 100%; border-collapse: collapse;'>
                                    <tr>
                                        <td style='padding: 10px 0; border-bottom: 1px solid #333;'>
                                            <strong style='color: #bb86fc; display: inline-block; width: 140px;'>Appeal ID:</strong>
                                            <span style='color: #e0e0e0; font-family: monospace; font-size: 15px;'>{$appeal_id}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style='padding: 10px 0; border-bottom: 1px solid #333;'>
                                            <strong style='color: #bb86fc; display: inline-block; width: 140px;'>Player Name:</strong>
                                            <span style='color: #e0e0e0;'>{$data['player_name']}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style='padding: 10px 0; border-bottom: 1px solid #333;'>
                                            <strong style='color: #bb86fc; display: inline-block; width: 140px;'>Punishment ID:</strong>
                                            <span style='color: #e0e0e0; font-family: monospace; font-size: 15px;'>{$data['punishment_id']}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style='padding: 10px 0; border-bottom: 1px solid #333;'>
                                            <strong style='color: #bb86fc; display: inline-block; width: 140px;'>Status:</strong>
                                            <span style='display: inline-block; background: rgba(255, 87, 34, 0.2); color: #ff5722; font-weight: bold; padding: 5px 10px; border-radius: 4px; font-size: 14px;'>UNDER REVIEW</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style='padding: 10px 0;'>
                                            <strong style='color: #bb86fc; display: inline-block; width: 140px;'>Submitted:</strong>
                                            <span style='color: #e0e0e0;'>{$timestamp}</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div style='background: #252525; border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #03dac6;'>
                                <h3 style='color: #03dac6; margin-top: 0; font-size: 18px;'>What happens next?</h3>
                                <p style='color: #e0e0e0; line-height: 1.6; font-size: 15px; margin-bottom: 0;'>
                                    Thank you for submitting your appeal. Our moderation team will carefully review your case and make a decision based on our server rules and policies. You will receive a notification via email once your appeal has been processed.
                                </p>
                            </div>

                            <div style='background: #252525; border-radius: 8px; padding: 20px; border-left: 4px solid #cf6679;'>
                                <h3 style='color: #cf6679; margin-top: 0; font-size: 18px;'>Important Information</h3>
                                <ul style='color: #e0e0e0; line-height: 1.6; padding-left: 20px;'>
                                    <li>Please do not submit multiple appeals for the same punishment</li>
                                    <li>Appeals are typically processed within 24-48 hours</li>
                                    <li>All decisions made by our moderation team are final</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Footer -->
                        <div style='background: linear-gradient(135deg, #7b1fa2, #4a148c); padding: 40px; text-align: center; border-top: 3px solid #7b1fa2;'>
                            <h3 style='color: #ffffff; margin-top: 0; font-size: 24px; margin-bottom: 20px; font-weight: 600; letter-spacing: 0.5px;'>Connect With Us</h3>

                            <div style='margin: 30px 0; text-align: left; max-width: 400px; margin-left: auto; margin-right: auto;'>
                                <ul style='list-style-type: none; padding: 0; margin: 0;'>
                                    <li style='margin-bottom: 12px; line-height: 1.6;'>
                                        <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Server IP:</span>
                                        <span style='color: #43A047; font-weight: 600; font-family: monospace;'>play.massacremc.net</span>
                                    </li>
                                    <li style='margin-bottom: 12px; line-height: 1.6;'>
                                        <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Discord:</span>
                                        <a href='https://discord.gg/mBjd5cXSxR' style='color: #5865F2; font-weight: 600; text-decoration: none; font-family: monospace;'>discord.gg/mBjd5cXSxR</a>
                                    </li>
                                    <li style='margin-bottom: 12px; line-height: 1.6;'>
                                        <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Website:</span>
                                        <a href='https://portal.massacremc.net' style='color: #7b1fa2; font-weight: 600; text-decoration: none; font-family: monospace;'>portal.massacremc.net</a>
                                    </li>
                                </ul>
                            </div>

                            <div style='margin-top: 35px; padding-top: 25px; border-top: 1px solid rgba(255,255,255,0.2);'>
                                <p style='margin: 0 0 15px; color: #ffffff; font-size: 16px;'>Need assistance? Email us at <a href='mailto:<EMAIL>' style='color: #ffffff; text-decoration: none; font-weight: 700; border-bottom: 2px solid rgba(255,255,255,0.4); padding-bottom: 2px;'><EMAIL></a></p>

                                <div style='background-color: rgba(0,0,0,0.2); border-radius: 8px; padding: 15px; margin-top: 20px; display: inline-block;'>
                                    <p style='margin: 0 0 5px; font-size: 13px; color: rgba(255,255,255,0.7);'>This is an automated message. Please do not reply to this email.</p>
                                    <p style='margin: 5px 0 0; font-size: 13px; color: rgba(255,255,255,0.7);'>© {$currentYear} MassacreMC. All rights reserved.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </body>
            </html>
            ";


            $ch = curl_init("https://api.mailgun.net/v3/{$env['MAILGUN_DOMAIN']}/messages");
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_USERPWD, "api:{$env['MAILGUN_API_KEY']}");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);

            // Use certificate file for SSL verification
            $cert_file = __DIR__ . '/certs/massacremc.net.pem';
            if (file_exists($cert_file)) {
                error_log("Using certificate file for appeal confirmation email: " . $cert_file);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                curl_setopt($ch, CURLOPT_CAINFO, $cert_file);
            } else {
                // Try alternative locations
                $alt_cert_files = [
                    '/home/<USER>/website/certs/massacremc.net.pem',
                    '/etc/ssl/certs/ca-certificates.crt'
                ];

                $cert_found = false;
                foreach ($alt_cert_files as $alt_file) {
                    if (file_exists($alt_file)) {
                        error_log("Using alternative certificate file for appeal confirmation email: " . $alt_file);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                        curl_setopt($ch, CURLOPT_CAINFO, $alt_file);
                        $cert_found = true;
                        break;
                    }
                }

                if (!$cert_found) {
                    error_log("No certificate file found, disabling SSL verification for appeal confirmation email");
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                }
            }

            $post_fields = [
                'from' => "MassacreMC Moderation Team <{$env['SENDER_EMAIL']}>",
                'to' => $receiver_email,
                'subject' => $email_subject,
                'html' => $html_body
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);

            error_log("Appeal confirmation email request details: from={$env['SENDER_EMAIL']}, to={$receiver_email}, subject={$email_subject}");

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            $curl_errno = curl_errno($ch);
            curl_close($ch);

            if ($curl_error) {
                error_log("cURL error sending appeal confirmation email: {$curl_error} (Error code: {$curl_errno})");

                // If it's an SSL certificate error, try again with SSL verification disabled
                if ($curl_errno == 60 || $curl_errno == 77) { // SSL certificate problem
                    error_log("Retrying appeal confirmation email with SSL verification disabled");

                    $ch = curl_init("https://api.mailgun.net/v3/{$env['MAILGUN_DOMAIN']}/messages");
                    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                    curl_setopt($ch, CURLOPT_USERPWD, "api:{$env['MAILGUN_API_KEY']}");
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);

                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    $curl_error = curl_error($ch);
                    curl_close($ch);

                    if ($curl_error) {
                        error_log("Retry failed with cURL error: {$curl_error}");
                        return false;
                    }
                } else {
                    return false;
                }
            }

            if ($http_code >= 200 && $http_code < 300) {
                error_log("Appeal confirmation email sent successfully to {$receiver_email} for appeal #{$appeal_id}");
                error_log("Mailgun API response: " . $response);
                return true;
            } else {
                error_log("Failed to send appeal confirmation email: HTTP Code {$http_code}, Response: {$response}");
                return false;
            }
        } catch (Exception $e) {
            error_log("Exception in send_appeal_confirmation_email: " . $e->getMessage());
            error_log("Exception stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Submit an appeal for a punishment
     *
     * @param string $username Player username
     * @param string $punishment_id Punishment ID
     * @param string $email Email address
     * @param string $reason Appeal reason
     * @param string $discord_id Discord ID for portal linking
     * @return array Result with success status and message
     */
    public function submit_appeal($username, $punishment_id, $email, $reason, $discord_id = '') {
        try {
            // Check if user is blocked from appeals
            if (!empty($discord_id)) {
                require_once __DIR__ . '/player-service-blocks.php';
                $blockInfo = isUserBlockedFromService($discord_id, 'appeals');
                if ($blockInfo) {
                    return [
                        'success' => false,
                        'blocked' => true,
                        'message' => getBlockMessage($blockInfo, 'appeals')
                    ];
                }
            }

            $username = sanitize_mongo_input($username, 'string', [
                'max_length' => 100,
                'allow_empty' => false
            ]);

            $punishment_id = sanitize_mongo_input($punishment_id, 'string', [
                'max_length' => 100,
                'allow_empty' => false
            ]);

            $email = sanitize_mongo_input($email, 'string', [
                'max_length' => 255,
                'allow_empty' => false
            ]);

            $reason = sanitize_mongo_input($reason, 'string', [
                'max_length' => 2000,
                'allow_empty' => false
            ]);

            if ($username === null || $punishment_id === null || $email === null || $reason === null) {
                return ['success' => false, 'message' => 'Invalid input parameters'];
            }


            $check_result = $this->check_punishment_simplified($username, $punishment_id);

            if (isset($check_result['error'])) {
                return ['success' => false, 'message' => $check_result['error']];
            }

            if (isset($check_result['hasAppeal']) && $check_result['hasAppeal']) {
                $status = $check_result['status'] ?? 'Pending';
                $appeal_id = $check_result['appeal_id'] ?? 'Unknown';
                $created_at = $check_result['created_at'] ?? '';
                $note = $check_result['note'] ?? '';


                $message = "An appeal for this punishment already exists (ID: $appeal_id, Status: $status)";

                if (!empty($created_at)) {
                    $message .= ", submitted on $created_at";
                }

                if (!empty($note)) {
                    $message .= ". Note: $note";
                }

                return [
                    'success' => false,
                    'message' => $message,
                    'duplicate' => true,
                    'appeal_id' => $appeal_id,
                    'status' => $status
                ];
            }


            try {

                $has_appeals_collection = false;
                try {
                    $collections = [];
                    foreach ($this->db->listCollections() as $collectionInfo) {
                        $collections[] = $collectionInfo->getName();
                    }
                    $has_appeals_collection = in_array('appeals', $collections);
                } catch (Exception $e) {
                    error_log("Error checking for appeals collection: " . $e->getMessage());
                }

                if (!$has_appeals_collection) {
                    error_log("Appeals collection does not exist - creating it");
                    $this->db->createCollection('appeals');

                    $this->db->appeals->createIndex(['player_name' => 1]);
                    $this->db->appeals->createIndex(['punishment_id' => 1]);
                    $this->db->appeals->createIndex(['status' => 1]);
                    $this->db->appeals->createIndex(['created_at' => -1]);


                    $existing_appeals = [];
                } else {
                    $case_insensitive_query = [
                        'punishment_id' => $punishment_id
                    ];

                    error_log("Checking for existing appeals with punishment ID: $punishment_id");
                    $existing_appeals = $this->db->appeals->find(sanitize_mongo_query($case_insensitive_query))->toArray();
                    error_log("Found " . count($existing_appeals) . " existing appeals with this punishment ID");
                }
            } catch (Exception $e) {
                error_log("Error checking for existing appeals: " . $e->getMessage());
                $existing_appeals = [];
            }

            if (!empty($existing_appeals)) {
                foreach ($existing_appeals as $appeal) {
                    $appeal_username = $appeal['player_name'] ?? '';


                    if (strcasecmp($appeal_username, $username) === 0) {
                        $status = $appeal['status'] ?? 'Pending';
                        $appeal_id = $appeal['id'] ?? $appeal['_id'] ?? 'Unknown';

                        error_log("Duplicate appeal caught in secondary check - Player: $username, Found: $appeal_username, Punishment: $punishment_id");

                        return [
                            'success' => false,
                            'message' => "An appeal for this punishment already exists under the username '$appeal_username' (ID: $appeal_id, Status: $status)",
                            'duplicate' => true,
                            'appeal_id' => $appeal_id,
                            'status' => $status
                        ];
                    }
                }
            }


            $punishment = $check_result['punishment'];


            $appeal_id = '' . bin2hex(random_bytes(4));


            $now = new DateTime('now', new DateTimeZone('America/New_York'));
            $created_at = new UTCDateTime($now->getTimestamp() * 1000);


            $appeal = [
                'id' => $appeal_id,
                'player_name' => $username,
                'punishment_id' => $punishment_id,
                'punishment_type' => $punishment['punishment_type'],
                'offense_type' => $punishment['offense_type'] ?? '',
                'offense_count' => $punishment['offense_count'] ?? 1,
                'email' => $email,
                'discord_id' => $discord_id, // Discord ID for portal linking
                'reason' => $reason,
                'status' => 'Pending',
                'created_at' => $created_at,
                'created_at_formatted' => $now->format('c'), // ISO 8601 format
                'history' => [
                    [
                        'action' => 'created',
                        'timestamp' => $created_at,
                        'details' => 'Appeal submitted by player'
                    ]
                ]
            ];


            try {
                $has_appeals_collection = false;
                try {
                    $collections = [];
                    foreach ($this->db->listCollections() as $collectionInfo) {
                        $collections[] = $collectionInfo->getName();
                    }
                    $has_appeals_collection = in_array('appeals', $collections);
                } catch (Exception $e) {
                    error_log("Error checking for appeals collection: " . $e->getMessage());
                    $has_appeals_collection = false;
                }

                if (!$has_appeals_collection) {
                    error_log("Appeals collection does not exist - creating it");
                    $this->db->createCollection('appeals');

                    $this->db->appeals->createIndex(['player_name' => 1]);
                    $this->db->appeals->createIndex(['punishment_id' => 1]);
                    $this->db->appeals->createIndex(['status' => 1]);
                    $this->db->appeals->createIndex(['created_at' => -1]);
                }
            } catch (Exception $e) {
                error_log("Error creating appeals collection: " . $e->getMessage());

            }


            try {
                $this->db->appeals->insertOne($appeal);


                error_log("Appeal submitted - ID: $appeal_id, Player: $username, Punishment: $punishment_id");

                // Send Discord webhook notification to staff
                try {
                    $this->send_appeal_discord_webhook($appeal_id, [
                        'player_name' => $username,
                        'punishment_id' => $punishment_id,
                        'punishment_type' => $punishment['punishment_type'],
                        'offense_type' => $punishment['offense_type'] ?? '',
                        'created_at_formatted' => $now->format('F d, Y \a\t h:i A') . ' ET'
                    ]);
                    error_log("Discord webhook sent for appeal: " . $appeal_id);
                } catch (Exception $webhook_error) {
                    // Don't fail the entire operation if webhook fails
                    error_log("Error sending Discord webhook for appeal: " . $webhook_error->getMessage());
                }

                // Send confirmation email
                try {
                    $email_data = [
                        'player_name' => $username,
                        'punishment_id' => $punishment_id,
                        'email' => $email
                    ];

                    error_log("Attempting to send appeal confirmation email to: $email for appeal: $appeal_id");
                    $email_sent = $this->send_appeal_confirmation_email($appeal_id, $email_data);

                    if (!$email_sent) {
                        error_log("Failed to send appeal confirmation email to $email for appeal $appeal_id, but appeal was submitted successfully");
                    } else {
                        error_log("Appeal confirmation email sent successfully to $email for appeal $appeal_id");
                    }
                } catch (Exception $email_error) {
                    error_log("Exception sending appeal confirmation email: " . $email_error->getMessage());
                    error_log("Email error stack trace: " . $email_error->getTraceAsString());
                }

                return [
                    'success' => true,
                    'message' => 'Appeal submitted successfully',
                    'appeal_id' => $appeal_id
                ];
            } catch (Exception $e) {
                error_log("Error inserting appeal: " . $e->getMessage());
                return [
                    'success' => false,
                    'message' => 'Failed to submit appeal. Please try again later.',
                    'error_details' => $e->getMessage()
                ];
            }
        } catch (Exception $e) {
            error_log("Error submitting appeal: " . $e->getMessage());
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }

    /**
     * Simplified version of check_punishment_for_appeal that follows a cleaner approach
     *
     * @param string $username Player username
     * @param string $punishment_id Punishment ID
     * @return array Result with punishment details or error
     */
    public function check_punishment_simplified($username, $punishment_id) {
        try {

            error_log("check_punishment_simplified called with username: $username, punishment_id: $punishment_id");


            if (!$this->db) {
                error_log("Database connection is not available in check_punishment_simplified");
                return ['error' => 'Database connection error. Please try again later.'];
            }


            try {
                $collections = [];
                foreach ($this->db->listCollections() as $collectionInfo) {
                    $collections[] = $collectionInfo->getName();
                }
                error_log("Available collections: " . implode(', ', $collections));


                if (!in_array('punishments', $collections)) {
                    error_log("Punishments collection does not exist in the database");
                    return ['error' => 'The punishments system is not properly configured. Please contact an administrator.'];
                }
            } catch (Exception $e) {
                error_log("Database connection error in check_punishment_simplified: " . $e->getMessage());
                return ['error' => 'Database connection error. Please try again later.'];
            }


            $username = sanitize_mongo_input($username, 'string', [
                'max_length' => 100,
                'allow_empty' => false
            ]);

            $punishment_id = sanitize_mongo_input($punishment_id, 'string', [
                'max_length' => 100,
                'allow_empty' => false
            ]);

            if (!$username || !$punishment_id) {
                error_log("Invalid input in check_punishment_simplified: username or punishment_id is empty after sanitization");
                return ['error' => 'Invalid input. Please check your username and punishment ID.'];
            }


            error_log("Sanitized values - username: $username, punishment_id: $punishment_id");



            $punishment_query = sanitize_mongo_query([
                'player_name' => $username,
                'punishment_id' => $punishment_id
            ]);

            error_log("Exact match query: " . json_encode($punishment_query));


            try {
                $punishment = $this->db->punishments->findOne($punishment_query);


                error_log("Exact match query result: " . ($punishment ? "Found" : "Not found"));


                if (!$punishment) {
                    $case_insensitive_query = [
                        'player_name' => ['$regex' => '^' . preg_quote($username) . '$', '$options' => 'i'],
                        'punishment_id' => ['$regex' => '^' . preg_quote($punishment_id) . '$', '$options' => 'i']
                    ];

                    $sanitized_query = sanitize_mongo_query($case_insensitive_query);
                    error_log("Case-insensitive query: " . json_encode($sanitized_query));

                    $punishment = $this->db->punishments->findOne($sanitized_query);
                    error_log("Case-insensitive query result: " . ($punishment ? "Found" : "Not found"));
                }


                if (!$punishment) {
                    $punishment_id_query = sanitize_mongo_query([
                        'punishment_id' => $punishment_id
                    ]);

                    error_log("Punishment ID only query: " . json_encode($punishment_id_query));
                    $punishment = $this->db->punishments->findOne($punishment_id_query);
                    error_log("Punishment ID only query result: " . ($punishment ? "Found" : "Not found"));


                    if ($punishment) {
                        $stored_username = $punishment['player_name'] ?? '';
                        error_log("Found punishment with ID $punishment_id for player: $stored_username");


                        if (strcasecmp($stored_username, $username) !== 0) {
                            error_log("Username mismatch: provided=$username, stored=$stored_username");
                            return ['error' => "The punishment ID exists but is associated with a different username. Please check your username."];
                        }
                    }
                }


                if (!$punishment) {
                    error_log("No punishment found with the provided criteria");
                    return ['error' => 'Invalid punishment ID or username combination.'];
                }


                $is_active = $punishment['active'] ?? false;
                $is_expired = $punishment['expired'] ?? false;

                error_log("Punishment status - active: " . ($is_active ? "true" : "false") . ", expired: " . ($is_expired ? "true" : "false"));


                if (!$is_active && !$is_expired) {
                    error_log("Punishment has been removed and cannot be appealed");
                    return ['error' => 'This punishment has been removed and cannot be appealed.'];
                }


                $formatted_punishment = [
                    'punishment_id' => $punishment['punishment_id'] ?? $punishment_id,
                    'punishment_type' => $punishment['punishment_type'] ?? 'Unknown',
                    'reason' => $punishment['reason'] ?? 'No reason provided',
                    'duration' => $punishment['duration'] ?? 'permanent',
                    'offense_type' => $punishment['offense_type'] ?? 'Unknown',
                    'offense_count' => $punishment['offense_count'] ?? 1,
                    'active' => $is_active,
                    'expired' => $is_expired
                ];

                error_log("Formatted punishment data: " . json_encode($formatted_punishment));


                if (isset($punishment['issued_at']) && $punishment['issued_at'] instanceof UTCDateTime) {
                    $issued_date = $punishment['issued_at']->toDateTime();
                    $issued_date->setTimezone(new DateTimeZone('America/New_York'));
                    $formatted_punishment['issued_at'] = $issued_date->format('F d, Y \a\t h:i A') . ' ET';
                } else {
                    $formatted_punishment['issued_at'] = 'Unknown';
                }


                if (isset($punishment['expires_at']) && $punishment['expires_at'] instanceof UTCDateTime) {
                    $expires_date = $punishment['expires_at']->toDateTime();
                    $expires_date->setTimezone(new DateTimeZone('America/New_York'));
                    $formatted_punishment['expires_at'] = $expires_date->format('F d, Y \a\t h:i A') . ' ET';
                } else {
                    $formatted_punishment['expires_at'] = null;
                }

            } catch (Exception $e) {
                error_log("Error while checking for punishment: " . $e->getMessage());
                error_log("Error stack trace: " . $e->getTraceAsString());
                return ['error' => 'Database error while checking for punishment. Please try again later.'];
            }


            try {

                $collections = [];
                foreach ($this->db->listCollections() as $collectionInfo) {
                    $collections[] = $collectionInfo->getName();
                }

                error_log("Available collections for appeals check: " . implode(', ', $collections));

                if (!in_array('appeals', $collections)) {
                    error_log("Appeals collection does not exist, creating it now");
                    $this->db->createCollection('appeals');
                    $this->db->appeals->createIndex(['player_name' => 1]);
                    $this->db->appeals->createIndex(['punishment_id' => 1]);
                    $this->db->appeals->createIndex(['status' => 1]);
                    $this->db->appeals->createIndex(['created_at' => -1]);
                    error_log("Appeals collection created with indexes");
                }


                $appeal_query = sanitize_mongo_query([
                    'player_name' => $username,
                    'punishment_id' => $punishment_id
                ]);

                error_log("Checking for existing appeal with query: " . json_encode($appeal_query));

                $existing_appeal = $this->db->appeals->findOne($appeal_query);
                error_log("Existing appeal check result: " . ($existing_appeal ? "Found" : "Not found"));


                if (!$existing_appeal) {
                    $case_insensitive_query = [
                        'player_name' => ['$regex' => '^' . preg_quote($username) . '$', '$options' => 'i'],
                        'punishment_id' => ['$regex' => '^' . preg_quote($punishment_id) . '$', '$options' => 'i']
                    ];

                    $sanitized_query = sanitize_mongo_query($case_insensitive_query);
                    error_log("Case-insensitive appeal query: " . json_encode($sanitized_query));

                    $existing_appeal = $this->db->appeals->findOne($sanitized_query);
                    error_log("Case-insensitive appeal check result: " . ($existing_appeal ? "Found" : "Not found"));
                }


                if ($existing_appeal) {
                    $status = $existing_appeal['status'] ?? 'Pending';
                    $appeal_id = $existing_appeal['appeal_id'] ?? $existing_appeal['id'] ?? 'Unknown';

                    if (isset($existing_appeal['created_at']) && $existing_appeal['created_at'] instanceof UTCDateTime) {
                        $date = $existing_appeal['created_at']->toDateTime();
                        $date->setTimezone(new DateTimeZone('America/New_York'));
                        $created_at = $date->format('F d, Y \a\t h:i A') . ' ET';
                    } else {
                        $created_at = '';
                    }
                    $note = $existing_appeal['staff_note'] ?? '';

                    error_log("Found existing appeal - ID: $appeal_id, Status: $status");

                    return [
                        'hasAppeal' => true,
                        'status' => $status,
                        'appeal_id' => $appeal_id,
                        'created_at' => $created_at,
                        'note' => $note
                    ];
                }

            } catch (Exception $e) {
                error_log("Error while checking for existing appeals: " . $e->getMessage());
                error_log("Error stack trace: " . $e->getTraceAsString());
                return ['error' => 'Database error while checking for existing appeals. Please try again later.'];
            }


            error_log("No existing appeal found, returning punishment details for appeal form");
            return [
                'hasAppeal' => false,
                'punishment' => $formatted_punishment
            ];

        } catch (Exception $e) {
            error_log("Unhandled exception in check_punishment_simplified: " . $e->getMessage());
            error_log("Error stack trace: " . $e->getTraceAsString());
            return ['error' => 'An error occurred while checking your punishment. Please try again later.'];
        }
    }

    /**
     * Send Discord webhook notification for new report submission
     *
     * @param string $report_id Report ID
     * @param array $report_data Report data including offender_name, rule_broken, reporter_name, priority, created_at_formatted
     * @return bool Whether webhook was sent successfully
     */
    private function send_report_discord_webhook($report_id, $report_data) {
        // Load environment configuration
        global $ENV;
        if (empty($ENV)) {
            require_once __DIR__ . '/config_parser.php';
            try {
                $ENV = load_env_config([
                    '/etc/massacremc/config/admin.env',
                    __DIR__ . '/../.env'
                ]);
            } catch (Exception $e) {
                error_log("Failed to load environment config for report webhook: " . $e->getMessage());
                $ENV = [];
            }
        }

        // Discord webhook URL for reports
        $webhookUrl = $ENV['DISCORD_REPORTS_WEBHOOK_URL'] ?? getenv('DISCORD_REPORTS_WEBHOOK_URL') ?? null;

        if (empty($webhookUrl)) {
            error_log("Discord reports webhook URL not configured");
            return false;
        }

        // Priority colors and labels
        $priority_config = [
            1 => ['color' => 0xFF0000, 'label' => '🔴 HIGH'],      // Red
            2 => ['color' => 0xFF8C00, 'label' => '🟠 URGENT'],    // Orange
            3 => ['color' => 0xFFFF00, 'label' => '🟡 MEDIUM'],    // Yellow
            4 => ['color' => 0x00FF00, 'label' => '🟢 LOW']        // Green
        ];

        $priority = $report_data['priority'] ?? 4;
        $priority_info = $priority_config[$priority] ?? $priority_config[4];

        // Create Discord embed
        $embed = [
            'title' => '📋 New Report Submitted',
            'color' => $priority_info['color'],
            'timestamp' => date('c'), // ISO 8601 format
            'fields' => [
                [
                    'name' => '🎯 Reported Player',
                    'value' => $report_data['offender_name'],
                    'inline' => true
                ],
                [
                    'name' => '⚖️ Rule Violation',
                    'value' => $report_data['rule_broken'],
                    'inline' => true
                ],
                [
                    'name' => '📊 Priority',
                    'value' => $priority_info['label'],
                    'inline' => true
                ],
                [
                    'name' => '👤 Reporter',
                    'value' => $report_data['reporter_name'] ?: 'Anonymous',
                    'inline' => true
                ],
                [
                    'name' => '🆔 Report ID',
                    'value' => "`{$report_id}`",
                    'inline' => true
                ],
                [
                    'name' => '⏰ Submitted',
                    'value' => $report_data['created_at_formatted'],
                    'inline' => true
                ]
            ],
            'footer' => [
                'text' => 'MassacreMC Reports System',
                'icon_url' => 'https://cdn.discordapp.com/attachments/your-icon-url.png'
            ]
        ];

        // Create webhook payload
        $payload = [
            'content' => '🚨 **New Report Alert** - Staff attention required!',
            'embeds' => [$embed]
        ];

        // Send webhook
        $ch = curl_init($webhookUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For development

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if (!empty($curlError)) {
            error_log("Report webhook cURL error: $curlError");
            return false;
        }

        if ($httpCode !== 204 && $httpCode !== 200) {
            error_log("Failed to send report webhook. HTTP Code: $httpCode, Response: $response");
            return false;
        }

        return true;
    }

    /**
     * Send Discord webhook notification for new appeal submission
     *
     * @param string $appeal_id Appeal ID
     * @param array $appeal_data Appeal data including player_name, punishment_id, punishment_type, offense_type, created_at_formatted
     * @return bool Whether webhook was sent successfully
     */
    private function send_appeal_discord_webhook($appeal_id, $appeal_data) {
        // Load environment configuration
        global $ENV;
        if (empty($ENV)) {
            require_once __DIR__ . '/config_parser.php';
            try {
                $ENV = load_env_config([
                    '/etc/massacremc/config/admin.env',
                    __DIR__ . '/../.env'
                ]);
            } catch (Exception $e) {
                error_log("Failed to load environment config for appeal webhook: " . $e->getMessage());
                $ENV = [];
            }
        }

        // Discord webhook URL for appeals
        $webhookUrl = $ENV['DISCORD_APPEALS_WEBHOOK_URL'] ?? getenv('DISCORD_APPEALS_WEBHOOK_URL') ?? null;

        if (empty($webhookUrl)) {
            error_log("Discord appeals webhook URL not configured");
            return false;
        }

        // Punishment type colors
        $punishment_colors = [
            'ban' => 0xFF0000,      // Red
            'mute' => 0xFF8C00,     // Orange
            'kick' => 0xFFFF00,     // Yellow
            'warn' => 0x00FF00,     // Green
            'tempban' => 0xFF4500,  // Orange Red
            'tempmute' => 0xFFA500  // Orange
        ];

        $punishment_type = strtolower($appeal_data['punishment_type'] ?? 'unknown');
        $embed_color = $punishment_colors[$punishment_type] ?? 0x5865F2; // Discord Blurple as default

        // Create Discord embed
        $embed = [
            'title' => '⚖️ New Appeal Submitted',
            'color' => $embed_color,
            'timestamp' => date('c'), // ISO 8601 format
            'fields' => [
                [
                    'name' => '👤 Player',
                    'value' => $appeal_data['player_name'],
                    'inline' => true
                ],
                [
                    'name' => '🔨 Punishment Type',
                    'value' => ucfirst($appeal_data['punishment_type']),
                    'inline' => true
                ],
                [
                    'name' => '📋 Offense',
                    'value' => $appeal_data['offense_type'] ?: 'Not specified',
                    'inline' => true
                ],
                [
                    'name' => '🆔 Appeal ID',
                    'value' => "`{$appeal_id}`",
                    'inline' => true
                ],
                [
                    'name' => '🔗 Punishment ID',
                    'value' => "`{$appeal_data['punishment_id']}`",
                    'inline' => true
                ],
                [
                    'name' => '⏰ Submitted',
                    'value' => $appeal_data['created_at_formatted'],
                    'inline' => true
                ]
            ],
            'footer' => [
                'text' => 'MassacreMC Appeals System',
                'icon_url' => 'https://cdn.discordapp.com/attachments/your-icon-url.png'
            ]
        ];

        // Create webhook payload
        $payload = [
            'content' => '📨 **New Appeal Submitted** - Staff review required!',
            'embeds' => [$embed]
        ];

        // Send webhook
        $ch = curl_init($webhookUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For development

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if (!empty($curlError)) {
            error_log("Appeal webhook cURL error: $curlError");
            return false;
        }

        if ($httpCode !== 204 && $httpCode !== 200) {
            error_log("Failed to send appeal webhook. HTTP Code: $httpCode, Response: $response");
            return false;
        }

        return true;
    }
}