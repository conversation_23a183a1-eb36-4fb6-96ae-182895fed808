RewriteEngine On
RewriteBase /dashboard/

RewriteRule ^login(\.php)?$ login.php [L]

# DirectoryIndex reports.php  # Removed as not allowed in .htaccess
# Using RewriteRule instead to achieve the same result
RewriteCond %{REQUEST_URI} ^/dashboard/?$
RewriteRule ^$ reports.php [L]

# Handle clean URLs
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^([^/]+)/?$ $1.php [L]
RewriteRule ^auth/check-roles$ auth/check-roles.php [L]
RewriteRule ^auth/check_status$ auth/check_status.php [L]
RewriteRule ^auth/check$ auth/check.php [L]

RewriteRule ^adminapi/appeals$ adminapi/appeals.php [L,QSA]
RewriteRule ^adminapi/appeals/([^/]+)/approve$ adminapi/appeals/approve.php?id=$1 [L,QSA]
RewriteRule ^adminapi/appeals/([^/]+)/deny$ adminapi/appeals/deny.php?id=$1 [L,QSA]

RewriteRule ^adminapi/reports$ adminapi/reports.php [L,QSA]
RewriteRule ^adminapi/reports/([^/]+)/(accept|reject)$ adminapi/reports.php [L,QSA]
RewriteRule ^adminapi/search$ adminapi/search.php [L,QSA]
RewriteRule ^adminapi/punishments$ adminapi/punishments.php [L,QSA]
RewriteRule ^adminapi/punishments/([^/]+)/add$ adminapi/punishments.php [L,QSA]
RewriteRule ^adminapi/punishments/([^/]+)/([^/]+)/remove$ adminapi/punishments.php [L,QSA]
RewriteRule ^adminapi/dashboard/stats$ adminapi/dashboard/stats.php [L,QSA]
RewriteRule ^adminapi/dashboard/staff/online$ adminapi/dashboard/staff/online.php [L,QSA]

    # Authentication routes
RewriteRule ^auth/check_status$ auth/check_status.php [L,QSA]
RewriteRule ^login$ login.php [L]
RewriteRule ^logout$ logout.php [L]
RewriteRule ^callback$ callback.php [L,QSA]

    # Dashboard pages
RewriteRule ^dashboard/?$ dashboard/index.php [L]
RewriteRule ^dashboard/([^/\.]+)$ dashboard/$1.php [L]


# Prevent direct PHP access
RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s([^.]+)\.php [NC]
RewriteRule ^ %1 [R=301,L]