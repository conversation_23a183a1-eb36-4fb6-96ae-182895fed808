<?php
// Force dark mode immediately to prevent any flash of light mode
echo '<script>
    (function() {
        // Always force dark mode regardless of preferences
        document.documentElement.classList.add("dark-mode");
        document.body.classList.add("dark-mode");
        localStorage.setItem("darkMode", "enabled");
        document.cookie = "darkMode=enabled; path=/; max-age=31536000; SameSite=Lax";
    })();
</script>';
?>
<div id="sidebar" class="sidebar">

    <?php

    $userRole = $_SESSION['discord_user_role'] ?? '';
    $isOwner = $userRole === 'OWNER';
    $isDeveloper = $userRole === 'DEVELOPER' || $isOwner;
    $isSupervisor = $userRole === 'SUPERVISOR' || $isDeveloper;
    $isAdmin = $userRole === 'ADMIN' || $isSupervisor;
    $isModerator = $userRole === 'MODERATOR' || $isAdmin;
    $isTrainee = $userRole === 'TRAINEE' || $isModerator;
    ?>

    <!-- Staff Access - TRAINEE_ROLE or higher -->
    <?php if ($isTrainee): ?>
    <div class="sidebar-section">
        <div class="sidebar-section-title">Main</div>
        <a href="/dashboard/dashboard" class="<?= $_SERVER['REQUEST_URI'] === '/dashboard/dashboard' || $_SERVER['REQUEST_URI'] === '/dashboard/dashboard' ? 'active' : '' ?>">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </a>
        <a href="/dashboard/reports" class="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard/reports') !== false ? 'active' : '' ?>">
            <i class="fas fa-flag"></i> Reports
        </a>
        <?php if (has_role(ROLE_ADMIN)): ?>
        <a href="/dashboard/appeals" class="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard/appeals') !== false ? 'active' : '' ?>">
            <i class="fas fa-gavel"></i> Appeals
        </a>
        <a href="/dashboard/tickets" class="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard/tickets') !== false ? 'active' : '' ?>">
            <i class="fas fa-ticket-alt"></i> Tickets
        </a>
        <?php endif; ?>
    </div>

    <div class="sidebar-section">
        <div class="sidebar-section-title">Management</div>
        <a href="/dashboard/playerinfo" class="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard/playerinfo') !== false ? 'active' : '' ?>">
            <i class="fas fa-user"></i> Search
        </a>
        <a href="/dashboard/punishments" class="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard/punishments') !== false ? 'active' : '' ?>">
            <i class="fas fa-ban"></i> Punishments
        </a>
        <?php if ($isAdmin): ?>
        <a href="/dashboard/staff-applications" class="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard/staff-applications') !== false ? 'active' : '' ?>">
            <i class="fas fa-user-tie"></i> Applications
        </a>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- DEVELOPER_ROLE or higher -->
    <?php if ($isDeveloper): ?>
    <div class="sidebar-section">
        <div class="sidebar-section-title">Developer</div>
        <a href="/dashboard/audit-logs" class="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard/audit-logs') !== false ? 'active' : '' ?>">
            <i class="fas fa-history"></i> Audit Logs
        </a>
        <a href="/dashboard/settings" class="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard/settings') !== false ? 'active' : '' ?>">
            <i class="fas fa-cog"></i> Settings
        </a>
    </div>
    <?php endif; ?>

    <!-- ADMIN_ROLE or higher -->
    <?php if ($isAdmin): ?>
    <div class="sidebar-section">
        <div class="sidebar-section-title">Administration</div>
        <a href="/dashboard/staff" class="<?= $_SERVER['REQUEST_URI'] === '/dashboard/staff' || strpos($_SERVER['REQUEST_URI'], '/dashboard/staff?') !== false || strpos($_SERVER['REQUEST_URI'], '/dashboard/staff/') !== false ? 'active' : '' ?>">
            <i class="fas fa-user-shield"></i> Staff Management
        </a>
    </div>
    <?php endif; ?>

    <div class="sidebar-section">
        <div class="sidebar-section-title">Support</div>
        <a href="/dashboard/help" class="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard/help') !== false ? 'active' : '' ?>">
            <i class="fas fa-question-circle"></i> Help Center
        </a>
    </div>

    <!-- User Profile Section -->
    <div class="sidebar-section mt-auto">
        <div class="sidebar-section-title">My Account</div>
        <a href="/dashboard/profile" class="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard/profile') !== false ? 'active' : '' ?>">
            <i class="fas fa-user-circle"></i> My Profile
        </a>

        <a href="#" onclick="logout(); return false;" class="logout-btn">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>
</div>