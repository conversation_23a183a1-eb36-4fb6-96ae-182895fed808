<?php
/**
 * Announcements API Router
 * Routes requests to the appropriate endpoint based on the URL path
 */


require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/auth.php';


header('Content-Type: application/json');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


error_log("Announcements API Router - Request URI: " . $_SERVER['REQUEST_URI']);
error_log("Announcements API Router - Session ID: " . session_id());
error_log("Announcements API Router - User ID: " . ($_SESSION['discord_user_id'] ?? 'Not set'));


if (!is_authenticated()) {
    error_log("Announcements API Router - Authentication failed");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


$request_uri = $_SERVER['REQUEST_URI'];
$path_parts = explode('/', $request_uri);


$endpoint = null;
for ($i = 0; $i < count($path_parts); $i++) {
    if ($path_parts[$i] === 'announcements.php' && isset($path_parts[$i + 1])) {
        $endpoint = $path_parts[$i + 1];
        break;
    }
}


switch ($endpoint) {
    case 'list':
        require_once __DIR__ . '/announcements/list.php';
        break;
    case 'send':
        require_once __DIR__ . '/announcements/send.php';
        break;
    case 'mark-read':
        require_once __DIR__ . '/announcements/mark-read.php';
        break;
    default:
        error_log("Announcements API Router - Invalid endpoint: " . $endpoint);
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Endpoint not found']);
        break;
}
?>
