<?php
/**
 * API Client for Go API Integration
 * 
 * Provides functions to interact with the Go API endpoints
 */

class ApiClient {
    private $base_url;
    private $jwt_token;
    private $timeout;

    public function __construct() {
        // Load environment configuration
        require_once __DIR__ . '/config_parser.php';
        
        try {
            $env = load_env_config([
                '/etc/massacremc/config/api.env',
                '/etc/massacremc/config/admin.env'
            ]);
        } catch (Exception $e) {
            error_log("API Client: Failed to load environment config: " . $e->getMessage());
            $env = [];
        }

        // Set API configuration
        $this->base_url = $env['API_BASE_URL'] ?? 'http://localhost:8080';
        $this->jwt_token = $env['JWT_SECRET'] ?? 'boobs'; // Default from config.ts
        $this->timeout = intval($env['API_TIMEOUT'] ?? '30');
    }

    /**
     * Make a GET request to the API
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = rtrim($this->base_url, '/') . '/' . ltrim($endpoint, '/');
        
        $context_options = [
            'http' => [
                'method' => $method,
                'header' => [
                    'Authorization: Bearer ' . $this->jwt_token,
                    'Content-Type: application/json',
                    'User-Agent: MassacreMC-Website/1.0'
                ],
                'timeout' => $this->timeout,
                'ignore_errors' => true
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ];

        if ($data && $method === 'POST') {
            $context_options['http']['content'] = json_encode($data);
        }

        $context = stream_context_create($context_options);
        
        error_log("API Client: Making {$method} request to {$url}");
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            $error = error_get_last();
            error_log("API Client: Request failed - " . ($error['message'] ?? 'Unknown error'));
            throw new Exception('API request failed: ' . ($error['message'] ?? 'Unknown error'));
        }

        // Check HTTP response code
        if (isset($http_response_header)) {
            $status_line = $http_response_header[0];
            preg_match('/HTTP\/\d\.\d\s+(\d+)/', $status_line, $matches);
            $status_code = isset($matches[1]) ? intval($matches[1]) : 200;
            
            if ($status_code >= 400) {
                error_log("API Client: HTTP {$status_code} error for {$url}");
                throw new Exception("API request failed with HTTP {$status_code}");
            }
        }

        $decoded = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("API Client: JSON decode error - " . json_last_error_msg());
            throw new Exception('Invalid JSON response from API');
        }

        return $decoded;
    }

    /**
     * Get faction data from Go API
     */
    public function getFactionData($faction_name) {
        try {
            if (empty($faction_name)) {
                return ['error' => 'Faction name is required', 'status' => 400];
            }

            $endpoint = 'api/factions/' . urlencode($faction_name);
            $response = $this->makeRequest($endpoint);

            if (isset($response['data'])) {
                error_log("API Client: Successfully retrieved faction data for {$faction_name}");
                return $response['data'];
            } else {
                error_log("API Client: No data field in response for faction {$faction_name}");
                return ['error' => 'Invalid response format from API', 'status' => 500];
            }

        } catch (Exception $e) {
            error_log("API Client: Error getting faction data for {$faction_name}: " . $e->getMessage());
            return ['error' => 'Failed to retrieve faction data: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Get player data from Go API
     */
    public function getPlayerData($uuid) {
        try {
            if (empty($uuid)) {
                return ['error' => 'UUID is required', 'status' => 400];
            }

            $endpoint = 'api/players/' . urlencode($uuid);
            $response = $this->makeRequest($endpoint);

            if (isset($response['data'])) {
                error_log("API Client: Successfully retrieved player data for UUID {$uuid}");
                return $response['data'];
            } else {
                error_log("API Client: No data field in response for UUID {$uuid}");
                return ['error' => 'Invalid response format from API', 'status' => 500];
            }

        } catch (Exception $e) {
            error_log("API Client: Error getting player data for UUID {$uuid}: " . $e->getMessage());
            return ['error' => 'Failed to retrieve player data: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Test API connection
     */
    public function testConnection() {
        try {
            // Try a simple endpoint to test connectivity
            $response = $this->makeRequest('api/health');
            return ['success' => true, 'response' => $response];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
