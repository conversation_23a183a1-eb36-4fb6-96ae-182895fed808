============================================
MongoDB\\GridFS\\Bucket::getChunkSizeBytes()
============================================

.. versionchanged:: 1.2

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::getChunkSizeBytes()

   Returns the chunk size of this bucket in bytes.

   .. code-block:: php

      function getChunkSizeBytes(): integer

Return Values
-------------

The chunk size of this bucket in bytes.

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   var_dump($bucket->getChunkSizeBytes());

The output would then resemble::

   int(261120)
