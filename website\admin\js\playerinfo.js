/**
 * Format a date in Eastern Time
 * @param {string|Date} dateInput - Date to format
 * @returns {string} Formatted date string with ET timezone
 */
function formatDateET(dateInput) {
    if (!dateInput) return 'Unknown';

    try {
        const date = new Date(dateInput);


        if (isNaN(date.getTime())) {
            return 'Unknown';
        }


        const options = {
            timeZone: 'America/New_York',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };

        return date.toLocaleString('en-US', options) + ' ET';
    } catch (e) {
        return 'Unknown';
    }
}

/**
 * Format offense type for display
 * @param {string} offenseType - The offense type to format
 * @returns {string} Formatted offense type
 */
function formatOffenseType(offenseType) {
    if (!offenseType) return '';


    let formatted = offenseType.replace(/_/g, ' ');


    formatted = formatted.split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

    return formatted;
}

document.addEventListener('DOMContentLoaded', function() {

    const searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');


    const punishmentModal = document.getElementById('punishmentModal');
    if (punishmentModal) {
        punishmentModal.addEventListener('hidden.bs.modal', function() {

            const form = document.getElementById('punishmentForm');
            if (form) {
                form.reset();
            }


            const submitBtn = document.getElementById('submitPunishment');
            if (submitBtn) {
                submitBtn.disabled = false;
            }


            const punishmentTypeSelect = document.getElementById('punishmentTypeSelect');
            if (punishmentTypeSelect) {
                punishmentTypeSelect.value = '';
            }


            const progressionInfo = document.getElementById('offenseProgressionInfo');
            if (progressionInfo) {
                progressionInfo.innerHTML = '';
            }
        });
    }


    const userId = window.AUTH_DATA ? window.AUTH_DATA.userId : '';
    const username = window.AUTH_DATA ? window.AUTH_DATA.username : '';
    const userRole = window.AUTH_DATA ? window.AUTH_DATA.role : '';
    const userAvatar = window.AUTH_DATA ? window.AUTH_DATA.avatar : '';


    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';


    $.ajaxSetup({
        xhrFields: {
            withCredentials: true  // This ensures cookies are sent with requests
        },
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            'X-Discord-ID': userId,
            'X-Discord-Username': username,
            'X-Discord-Role': userRole,
            'X-Requested-With': 'XMLHttpRequest'  // Identify as AJAX request
        },
        beforeSend: function(xhr) {

            xhr.setRequestHeader('X-Session-ID', window.AUTH_DATA.sessionId || '');
        }
    });


    function checkSession() {
        $.ajax({
            url: '/auth/check_status',
            method: 'GET',
            success: function(response) {
                if (!response.authenticated) {
                    handleAuthError();
                }
            },
            error: function() {
                handleAuthError();
            }
        });
    }


    function handleAuthError(xhr) {


        const errorReason = xhr?.responseJSON?.reason || 'unknown reason';
        const sessionId = window.AUTH_DATA?.sessionId || 'unknown';

        Swal.fire({
            title: 'Authentication Required',
            html: `Your session has expired. Please log in again.<br><br>
                  <small class="text-muted">Technical details: ${errorReason}<br>
                  Session ID: ${sessionId}</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Log In',
            cancelButtonText: 'Try Again',
            allowOutsideClick: false
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '/login.php?expired=1';
            } else {

                window.location.reload();
            }
        });
    }


    // Store interval ID for cleanup
    window.playerInfoSessionInterval = setInterval(checkSession, 5 * 60 * 1000);

    // Clean up interval when page unloads
    window.addEventListener('beforeunload', function() {
        if (window.playerInfoSessionInterval) {
            clearInterval(window.playerInfoSessionInterval);
        }
    });




    document.getElementById('searchButton').addEventListener('click', performSearch);


    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();


            const suggestions = document.getElementById('searchSuggestions');
            suggestions.style.display = 'none';


            performSearch();
        }
    });


    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');


    if (searchSuggestions) {
        searchSuggestions.style.display = 'none';
    }




    function updateSearchHistoryDisplay() {
        const historyTags = document.getElementById('historyTags');
        historyTags.innerHTML = '';


        const recentSearches = searchHistory.slice(0, 5);

        if (recentSearches.length === 0) {
            const emptyMessage = document.createElement('span');
            emptyMessage.className = 'text-muted';
            emptyMessage.textContent = 'No recent searches';
            historyTags.appendChild(emptyMessage);
            return;
        }

        recentSearches.forEach(item => {
            const tag = document.createElement('div');
            tag.className = `history-tag ${item.type}`;

            const icon = document.createElement('i');
            icon.className = item.type === 'player' ? 'fas fa-user' : 'fas fa-users';

            const text = document.createTextNode(item.query);

            tag.appendChild(icon);
            tag.appendChild(text);

            tag.addEventListener('click', function() {
                document.getElementById('searchType').value = item.type;
                document.getElementById('searchInput').value = item.query;
                performSearch();
            });

            historyTags.appendChild(tag);
        });
    }


    updateSearchHistoryDisplay();

    document.getElementById('submitNote').addEventListener('click', function() {
        submitNote();
    });


    document.getElementById('punishmentDurationUnit').addEventListener('change', function() {
        const durationInput = document.getElementById('punishmentDuration');
        if (this.value === 'permanent') {
            durationInput.disabled = true;
            durationInput.value = '';
        } else {
            durationInput.disabled = false;
            if (!durationInput.value) {
                durationInput.value = '1';
            }
        }
    });


    function formatDate(dateInput) {
        if (!dateInput) return 'Unknown';

        let timestamp;


        if (typeof dateInput === 'object' && dateInput !== null) {

            if (dateInput.$date) {

                if (typeof dateInput.$date === 'string') {
                    timestamp = new Date(dateInput.$date);
                } else if (typeof dateInput.$date === 'number') {
                    timestamp = new Date(dateInput.$date);
                } else if (typeof dateInput.$date === 'object' && dateInput.$date.$numberLong) {

                    timestamp = new Date(parseInt(dateInput.$date.$numberLong));
                }
            } else {

                timestamp = new Date(dateInput);
            }
        } else {

            timestamp = new Date(dateInput);
        }


        if (isNaN(timestamp.getTime())) {
            return 'Unknown';
        }

        return timestamp.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
            timeZone: 'America/New_York'
        }) + ' ET';
    }

    function performSearch() {
        const query = document.getElementById('searchInput').value.trim();
        const searchType = document.getElementById('searchType').value;

        if (!query) {
            Swal.fire({
                icon: 'warning',
                title: 'Empty Search',
                text: 'Please enter a search term'
            });
            return;
        }


        const searchItem = { query, type: searchType, timestamp: new Date().toISOString() };


        const existingIndex = searchHistory.findIndex(item =>
            item.query.toLowerCase() === query.toLowerCase() && item.type === searchType
        );

        if (existingIndex !== -1) {
            searchHistory.splice(existingIndex, 1);
        }


        searchHistory.unshift(searchItem);


        if (searchHistory.length > 20) {
            searchHistory.pop();
        }


        localStorage.setItem('searchHistory', JSON.stringify(searchHistory));


        updateSearchHistoryDisplay();


        document.getElementById('loadingSpinner').classList.add('active');


        $.ajax({
            url: '/adminapi/search.php',
            method: 'GET',
            data: { query: query, type: searchType },
            dataType: 'json',
            success: function(response) {

                document.getElementById('loadingSpinner').classList.remove('active');

                if (response.error) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Search Error',
                        text: response.error
                    });
                    $('#playerData').html(`
                        <div class="empty-state">
                            <i class="fas fa-exclamation-circle text-danger"></i>
                            <h4>Search Error</h4>
                            <p>${response.error}</p>
                        </div>
                    `);
                    return;
                }


                if (searchType === 'player') {
                    renderPlayerData(response);
                } else {
                    renderFactionData(response);
                }
            },
            error: function(xhr) {

                document.getElementById('loadingSpinner').classList.remove('active');


                if (xhr.status === 401) {
                    handleAuthError(xhr);
                    return;
                }

                const errorMsg = xhr.responseJSON?.error || 'An error occurred while searching';
                Swal.fire({
                    icon: 'error',
                    title: 'Search Failed',
                    text: errorMsg
                });
                $('#playerData').html(`
                    <div class="empty-state">
                        <i class="fas fa-exclamation-circle text-danger"></i>
                        <h4>Search Failed</h4>
                        <p>${errorMsg}</p>
                        <button class="btn btn-primary mt-3" onclick="performSearch()">
                            <i class="fas fa-sync-alt me-2"></i> Try Again
                        </button>
                    </div>
                `);
            }
        });
    }

    function renderPlayerData(data) {
        // Store current player data globally for use in other functions
        window.currentPlayerData = {
            xuid: data.xuid || '',
            playerName: data.playernames && data.playernames.length > 0 ? data.playernames[0] : 'Unknown',
            data: data
        };

        const xuid = data.xuid || '';
        const playerName = data.playernames && data.playernames.length > 0 ? data.playernames[0] : 'Unknown';
        const online = data.online === true ? 'Online' : 'Offline';
        const firstJoined = formatDate(data.firstlogin || '');
        const lastSeen = formatDate(data.lastlogin || '');


        const rankId = data.groupsettings && data.groupsettings.rankid ? data.groupsettings.rankid : 'player';


        let faction = 'None';
        let factionRole = 0;
        let factionJoined = 'N/A';
        let doubloons = 0;
        let strength = 0;
        let kills = 0;
        let deaths = 0;
        let killstreak = 0;
        let bounties = {};


        if (data.games && data.games.factions) {
            const factions = data.games.factions;
            faction = factions.name || 'None';
            factionRole = factions.role || 0;
            factionJoined = formatDate(factions.firstjoined || '');


            if (factions.stats) {
                doubloons = factions.stats.doubloons || 0;
                strength = factions.stats.strength || 0;
                kills = factions.stats.kills || 0;
                deaths = factions.stats.deaths || 0;
                killstreak = factions.stats.killstreak || 0;
            }


            if (factions.bounties) {
                bounties = factions.bounties;
            }
        }


        const kdr = deaths > 0 ? (kills / deaths).toFixed(2) : kills;


        const formattedDoubloons = doubloons.toLocaleString();
        const formattedStrength = strength.toLocaleString();


        let totalBounty = 0;
        if (bounties) {
            Object.values(bounties).forEach(value => {
                if (typeof value === 'number') {
                    totalBounty += value;
                }
            });
        }
        const formattedBounty = totalBounty.toLocaleString();


        const notes = data.notes || 'No notes available for this player.';


        const infoContent = `
            <div class="dark-player-container">
                <!-- Player Header -->
                <div class="dark-player-header">
                    <div class="player-avatar-section">
                        <div class="player-avatar">
                            <img src="https://api.mineatar.io/head/${playerName}?scale=8" alt="${playerName}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="avatar-fallback" style="display: none; width: 64px; height: 64px; background: #6c757d; color: white; border-radius: 8px; align-items: center; justify-content: center; font-size: 24px; font-weight: bold;">
                                ${playerName.charAt(0).toUpperCase()}
                            </div>
                            <div class="status-dot ${data.online ? 'online' : 'offline'}"></div>
                        </div>
                        <div class="player-details">
                            <h1 class="player-name">${playerName}</h1>
                            <div class="player-badges">
                                <span class="rank-badge" style="${getRankBadgeStyle(rankId)}">
                                    ${rankId !== 'player' ? rankId.toUpperCase() : 'PLAYER'}
                                </span>
                                ${faction !== 'None' ?
                                    `<span class="faction-badge">${faction}</span>` :
                                    `<span class="no-faction-badge">No Faction</span>`
                                }
                                <span class="status-badge ${data.online ? 'online' : 'offline'}">
                                    ${data.online ? 'Online' : 'Offline'}
                                </span>
                            </div>
                            <div class="player-meta">
                                <span><i class="fas fa-calendar-plus"></i> Joined: ${firstJoined}</span>
                                <span><i class="fas fa-clock"></i> Last seen: ${lastSeen}</span>
                            </div>
                        </div>
                    </div>
                    <div class="player-actions">
                        ${(window.AUTH_DATA && (window.AUTH_DATA.role === 'DEVELOPER' || window.AUTH_DATA.role === 'OWNER')) ?
                            `<button class="action-btn rank-btn" onclick="openRankModal('${xuid}', '${playerName}', '${rankId}')">
                                <i class="fas fa-crown"></i> Change Rank
                            </button>` : ''
                        }
                        <button class="action-btn punish-btn" onclick="openPunishmentModal('${xuid}', '${playerName}')">
                            <i class="fas fa-gavel"></i> Punish
                        </button>
                        <button class="action-btn note-btn" onclick="openNoteModal('${xuid}', '${playerName}')">
                            <i class="fas fa-sticky-note"></i> Add Note
                        </button>
                    </div>
                </div>



                <!-- Navigation Tabs -->
                <div class="dark-tabs-nav">
                    <button class="tab-btn active" data-tab="info">
                        <i class="fas fa-user"></i> Player Info
                    </button>
                    <button class="tab-btn" data-tab="punishments">
                        <i class="fas fa-gavel"></i> Punishments
                    </button>
                    <button class="tab-btn" data-tab="notes">
                        <i class="fas fa-sticky-note"></i> Notes
                    </button>
                    ${canViewConnectedAccounts() ? `
                    <button class="tab-btn" data-tab="accounts">
                        <i class="fas fa-link"></i> Accounts
                    </button>
                    ` : ''}
                </div>

                <!-- Tab Content -->
                <div class="dark-tab-content">
                    <!-- Player Info Tab -->
                    <div class="tab-panel active" id="info-panel">
                        <div class="info-grid">
                            <!-- Basic Info Card -->
                            <div class="info-card">
                                <div class="card-header">
                                    <i class="fas fa-user-circle"></i>
                                    <h3>Basic Information</h3>
                                </div>
                                <div class="card-content">
                                    <div class="info-item">
                                        <span class="label">XUID</span>
                                        <span class="value monospace">${xuid}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">First Joined</span>
                                        <span class="value">${firstJoined}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Last Seen</span>
                                        <span class="value">${lastSeen}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Status</span>
                                        <span class="value ${data.online ? 'online' : 'offline'}">${data.online ? 'Online' : 'Offline'}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Statistics Card -->
                            <div class="info-card">
                                <div class="card-header">
                                    <i class="fas fa-chart-bar"></i>
                                    <h3>Statistics</h3>
                                </div>
                                <div class="card-content">
                                    <div class="stats-grid">
                                        <div class="stat-item">
                                            <div class="stat-icon kills"><i class="fas fa-crosshairs"></i></div>
                                            <div class="stat-info">
                                                <span class="stat-value">${kills.toLocaleString()}</span>
                                                <span class="stat-label">Kills</span>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-icon deaths"><i class="fas fa-skull"></i></div>
                                            <div class="stat-info">
                                                <span class="stat-value">${deaths.toLocaleString()}</span>
                                                <span class="stat-label">Deaths</span>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-icon kdr"><i class="fas fa-chart-line"></i></div>
                                            <div class="stat-info">
                                                <span class="stat-value">${kdr}</span>
                                                <span class="stat-label">K/D Ratio</span>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-icon balance"><i class="fas fa-coins"></i></div>
                                            <div class="stat-info">
                                                <span class="stat-value">${formattedDoubloons}</span>
                                                <span class="stat-label">Doubloons</span>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-icon strength"><i class="fas fa-dumbbell"></i></div>
                                            <div class="stat-info">
                                                <span class="stat-value">${formattedStrength}</span>
                                                <span class="stat-label">Strength</span>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-icon killstreak"><i class="fas fa-fire"></i></div>
                                            <div class="stat-info">
                                                <span class="stat-value">${killstreak.toLocaleString()}</span>
                                                <span class="stat-label">Killstreak</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${faction !== 'None' ? `
                            <!-- Faction Card -->
                            <div class="info-card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-flag"></i>
                                        <h3 style="display: inline; margin-left: 8px;">Faction</h3>
                                    </div>
                                    ${window.AUTH_DATA && window.AUTH_DATA.role === 'DEVELOPER' ? `
                                        <button class="btn btn-danger btn-sm" onclick="deleteFaction('${faction}')" title="Delete Faction">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    ` : ''}
                                </div>
                                <div class="card-content">
                                    <div class="info-item">
                                        <span class="label">Faction Name</span>
                                        <span class="value">
                                            <a href="javascript:void(0)" onclick="searchFaction('${faction}')" class="text-decoration-none">
                                                ${faction}
                                            </a>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Role</span>
                                        <span class="value">${getFactionRoleName(factionRole)}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Joined</span>
                                        <span class="value">${factionJoined}</span>
                                    </div>
                                    ${totalBounty > 0 ? `
                                    <div class="info-item">
                                        <span class="label">Total Bounty</span>
                                        <span class="value">${formattedBounty}</span>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>



                    <!-- Punishments Tab -->
                    <div class="tab-panel" id="punishments-panel">
                        <div class="tab-header">
                            <h2><i class="fas fa-gavel"></i> Punishment History</h2>
                            <button class="add-btn" onclick="openPunishmentModal('${xuid}', '${playerName}')">
                                <i class="fas fa-plus"></i> Add Punishment
                            </button>
                        </div>
                        <div id="punishmentsContent" class="content-area">
                            <div class="loading-state">
                                <div class="spinner"></div>
                                <p>Loading punishment history...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Notes Tab -->
                    <div class="tab-panel" id="notes-panel">
                        <div class="tab-header">
                            <h2><i class="fas fa-sticky-note"></i> Staff Notes</h2>
                        </div>
                        <div id="notesContent" class="content-area">
                            <div class="loading-state">
                                <div class="spinner"></div>
                                <p>Loading notes...</p>
                            </div>
                        </div>
                    </div>

                    ${canViewConnectedAccounts() ? `
                    <!-- Accounts Tab -->
                    <div class="tab-panel" id="accounts-panel">
                        <div class="tab-header">
                            <h2><i class="fas fa-link"></i> Connected Accounts</h2>
                        </div>
                        <div id="accountsContent" class="content-area">
                            <div class="loading-state">
                                <div class="spinner"></div>
                                <p>Loading connected accounts...</p>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;

        $('#playerData').html(infoContent);

        // Initialize dark mode tabs
        initializeDarkTabs();

        // Load tab content after DOM is ready
        setTimeout(() => {
            loadPunishments(); // Now uses actual player name from global currentPlayerData
            loadNotes(); // Now uses XUID from global currentPlayerData
            // Only load connected accounts for developer+ staff
            if (canViewConnectedAccounts()) {
                loadConnectedAccounts(); // Now uses actual player name from global currentPlayerData
            }
        }, 100);
    }

    function initializeDarkTabs() {
        // Add dark mode styles if not already added
        if (!document.getElementById('dark-player-styles')) {
            const darkStyles = `
                <style id="dark-player-styles">
                /* Dark Mode Player Info Styles */
                .dark-player-container {
                    background: #1a1a1a;
                    border-radius: 12px;
                    overflow: hidden;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                }

                .dark-player-header {
                    background: linear-gradient(135deg, #2d2d2d 0%, #1f1f1f 100%);
                    padding: 24px;
                    border-bottom: 1px solid #333;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .player-avatar-section {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                }

                .player-avatar {
                    position: relative;
                    width: 64px;
                    height: 64px;
                }

                .player-avatar img {
                    width: 100%;
                    height: 100%;
                    border-radius: 8px;
                    border: 2px solid #444;
                }

                .status-dot {
                    position: absolute;
                    bottom: -2px;
                    right: -2px;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    border: 2px solid #1a1a1a;
                }

                .status-dot.online {
                    background: #10b981;
                }

                .status-dot.offline {
                    background: #6b7280;
                }

                .player-details h1.player-name {
                    color: #fff;
                    font-size: 28px;
                    font-weight: 700;
                    margin: 0 0 8px 0;
                }

                .player-badges {
                    display: flex;
                    gap: 8px;
                    margin-bottom: 8px;
                }

                .rank-badge, .faction-badge, .no-faction-badge, .status-badge {
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: 600;
                    text-transform: uppercase;
                }

                .rank-badge {
                    background: #3b82f6;
                    color: white;
                }

                .faction-badge {
                    background: #8b5cf6;
                    color: white;
                }

                .no-faction-badge {
                    background: #6b7280;
                    color: white;
                }

                .status-badge.online {
                    background: #10b981;
                    color: white;
                }

                .status-badge.offline {
                    background: #6b7280;
                    color: white;
                }

                .player-meta {
                    display: flex;
                    gap: 16px;
                    color: #9ca3af;
                    font-size: 14px;
                }

                .player-meta i {
                    margin-right: 4px;
                }

                .player-actions {
                    display: flex;
                    gap: 12px;
                }

                .action-btn {
                    background: #374151;
                    color: #fff;
                    border: 1px solid #4b5563;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 14px;
                    cursor: pointer;
                    transition: all 0.2s;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                }

                .action-btn:hover {
                    background: #4b5563;
                    border-color: #6b7280;
                }

                .action-btn.rank-btn:hover {
                    background: #3b82f6;
                    border-color: #3b82f6;
                }

                .action-btn.punish-btn:hover {
                    background: #ef4444;
                    border-color: #ef4444;
                }

                .action-btn.note-btn:hover {
                    background: #f59e0b;
                    border-color: #f59e0b;
                }

                /* Tabs */
                .dark-tabs-nav {
                    background: #262626;
                    display: flex;
                    border-bottom: 1px solid #333;
                }

                .tab-btn {
                    background: none;
                    border: none;
                    color: #9ca3af;
                    padding: 16px 24px;
                    cursor: pointer;
                    transition: all 0.2s;
                    border-bottom: 3px solid transparent;
                    font-weight: 500;
                }

                .tab-btn:hover {
                    color: #fff;
                    background: #333;
                }

                .tab-btn.active {
                    color: #3b82f6;
                    border-bottom-color: #3b82f6;
                    background: #1a1a1a;
                }

                .tab-btn i {
                    margin-right: 8px;
                }

                /* Tab Content */
                .dark-tab-content {
                    background: #1a1a1a;
                }

                .tab-panel {
                    display: none;
                    padding: 24px;
                }

                .tab-panel.active {
                    display: block;
                }

                .tab-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 24px;
                }

                .tab-header h2 {
                    color: #fff;
                    font-size: 24px;
                    font-weight: 600;
                    margin: 0;
                }

                .tab-header i {
                    margin-right: 8px;
                }

                .add-btn {
                    background: #3b82f6;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    transition: background 0.2s;
                }

                .add-btn:hover {
                    background: #2563eb;
                }

                /* Info Grid */
                .info-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                    gap: 24px;
                }

                .info-card {
                    background: #262626;
                    border: 1px solid #333;
                    border-radius: 8px;
                    overflow: hidden;
                }

                .card-header {
                    background: #2d2d2d;
                    padding: 16px;
                    border-bottom: 1px solid #333;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                }

                .card-header i {
                    color: #3b82f6;
                    font-size: 18px;
                }

                .card-header h3 {
                    color: #fff;
                    font-size: 18px;
                    font-weight: 600;
                    margin: 0;
                }

                .card-content {
                    padding: 20px;
                }

                .info-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 12px 0;
                    border-bottom: 1px solid #333;
                }

                .info-item:last-child {
                    border-bottom: none;
                }

                .info-item .label {
                    color: #9ca3af;
                    font-weight: 500;
                }

                .info-item .value {
                    color: #fff;
                    font-weight: 600;
                }

                .info-item .value.online {
                    color: #10b981;
                }

                .info-item .value.offline {
                    color: #6b7280;
                }

                .info-item .value.monospace {
                    font-family: 'Courier New', monospace;
                    font-size: 14px;
                }

                /* Stats Grid */
                .stats-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 16px;
                }

                .stat-item {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    padding: 16px;
                    background: #1f1f1f;
                    border-radius: 6px;
                    border: 1px solid #333;
                }

                .stat-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 6px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                }

                .stat-icon.kills { background: #ef4444; color: white; }
                .stat-icon.deaths { background: #6b7280; color: white; }
                .stat-icon.kdr { background: #10b981; color: white; }
                .stat-icon.balance { background: #f59e0b; color: white; }
                .stat-icon.strength { background: #8b5cf6; color: white; }
                .stat-icon.killstreak { background: #f97316; color: white; }

                .stat-info {
                    flex: 1;
                }

                .stat-value {
                    display: block;
                    color: #fff;
                    font-size: 18px;
                    font-weight: 700;
                }

                .stat-label {
                    display: block;
                    color: #9ca3af;
                    font-size: 12px;
                    text-transform: uppercase;
                    font-weight: 500;
                }

                /* Content Area */
                .content-area {
                    background: #262626;
                    border: 1px solid #333;
                    border-radius: 8px;
                    min-height: 300px;
                }

                /* Loading State */
                .loading-state {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 60px 20px;
                    color: #9ca3af;
                }

                .spinner {
                    width: 40px;
                    height: 40px;
                    border: 3px solid #333;
                    border-top: 3px solid #3b82f6;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin-bottom: 16px;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                /* Empty State */
                .empty-state {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 60px 20px;
                    color: #9ca3af;
                    text-align: center;
                }

                .empty-state i {
                    font-size: 48px;
                    margin-bottom: 16px;
                    opacity: 0.5;
                }

                .empty-state h4 {
                    color: #fff;
                    margin-bottom: 8px;
                }

                /* Faction delete button styling */
                .btn-danger.btn-sm {
                    padding: 0.25rem 0.5rem;
                    font-size: 0.75rem;
                    border-radius: 0.25rem;
                    transition: all 0.2s ease;
                }

                .btn-danger.btn-sm:hover {
                    background-color: #c82333;
                    border-color: #bd2130;
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
                }

                .card-header .btn-danger.btn-sm {
                    margin-left: auto;
                }

                /* Faction name link styling */
                .info-item .value a {
                    color: inherit;
                    transition: color 0.2s ease;
                }

                .info-item .value a:hover {
                    color: #0d6efd !important;
                    text-decoration: underline !important;
                }

                /* Faction card header styling */
                .card-header.d-flex {
                    display: flex !important;
                    justify-content: space-between !important;
                    align-items: center !important;
                }
                </style>
            `;
            document.head.insertAdjacentHTML('beforeend', darkStyles);
        }

        // Initialize tab click handlers
        $(document).off('click', '.tab-btn').on('click', '.tab-btn', function() {
            const tabName = $(this).data('tab');
            console.log('Tab clicked:', tabName);

            // Update tab buttons
            $('.tab-btn').removeClass('active');
            $(this).addClass('active');

            // Update tab panels
            $('.tab-panel').removeClass('active');
            $(`#${tabName}-panel`).addClass('active');

            console.log('Tab panel activated:', `#${tabName}-panel`);

            // Load content for the selected tab
            const playerName = getCurrentPlayerName();
            const xuid = getCurrentPlayerXuid();
            console.log('Current player name:', playerName);
            console.log('Current player XUID:', xuid);

            if (playerName || xuid) {
                switch(tabName) {
                    case 'punishments':
                        console.log('Loading punishments...');
                        loadPunishments(); // Now uses actual player name from global currentPlayerData
                        break;
                    case 'notes':
                        console.log('Loading notes...');
                        loadNotes(); // Now uses XUID from global currentPlayerData
                        break;
                    case 'accounts':
                        // Only load connected accounts for developer+ staff
                        if (canViewConnectedAccounts()) {
                            console.log('Loading connected accounts...');
                            loadConnectedAccounts(); // Now uses actual player name from global currentPlayerData
                        }
                        break;
                }
            }
        });
    }

    function getCurrentPlayerName() {
        return document.getElementById('searchInput')?.value?.trim() || '';
    }

    function getCurrentPlayerXuid() {
        return window.currentPlayerData?.xuid || '';
    }

    function getCurrentPlayerData() {
        return window.currentPlayerData || null;
    }

    // Helper function to check if user has developer+ permissions for connected accounts
    function canViewConnectedAccounts() {
        console.log('Checking connected accounts permission. User role:', window.AUTH_DATA?.role);
        const hasPermission = window.AUTH_DATA && (
            window.AUTH_DATA.role === 'DEVELOPER' ||
            window.AUTH_DATA.role === 'OWNER'
        );
        console.log('Connected accounts permission result:', hasPermission);
        return hasPermission;
    }


    function formatPlaytime(minutes) {
        if (!minutes) return 'Unknown';

        const days = Math.floor(minutes / 1440);
        const hours = Math.floor((minutes % 1440) / 60);
        const mins = minutes % 60;

        let result = '';
        if (days > 0) result += `${days}d `;
        if (hours > 0 || days > 0) result += `${hours}h `;
        result += `${mins}m`;

        return result;
    }


    function formatTags(tags) {
        if (!tags || tags === 'None') return 'None';

        if (Array.isArray(tags)) {
            return tags.map(tag => `<span class="badge bg-info me-1">${tag}</span>`).join(' ');
        } else if (typeof tags === 'string') {
            return tags.split(',').map(tag => `<span class="badge bg-info me-1">${tag.trim()}</span>`).join(' ');
        }

        return tags;
    }


    function getFactionRoleName(roleId) {

        const roleNames = {
            0: 'Owner',
            1: 'Officer',
            2: 'Member'
        };

        return roleNames[roleId] || 'Member';
    }


    function formatFactionRole(roleId) {
        return getFactionRoleName(roleId);
    }


    function renderBounties(bounties) {
        if (!bounties || Object.keys(bounties).length === 0) {
            return '<p class="text-muted">No bounties found for this player.</p>';
        }

        let bountiesHtml = '<div class="table-responsive"><table class="table table-sm table-striped">';
        bountiesHtml += '<thead><tr><th>Player ID</th><th>Amount</th></tr></thead><tbody>';

        for (const [playerId, amount] of Object.entries(bounties)) {
            bountiesHtml += `
                <tr>
                    <td>${playerId}</td>
                    <td>$${Number(amount).toLocaleString()}</td>
                </tr>
            `;
        }

        bountiesHtml += '</tbody></table></div>';
        return bountiesHtml;
    }

    function renderFactionData(data) {



        const factionName = data.name || 'Unknown Faction';
        const bankDoubloons = data.bankdoubloons || 0;
        const strength = data.strength || 0;
        const memberCount = data.memberCount || 0;
        const allies = data.allies || [];
        const requests = data.requests || [];


        const owner = data.owner || 'None';
        const officers = data.officers || [];
        const regularMembers = data.members || [];


        let ownerDisplay = owner ? `<div class="member-item owner"><i class="fas fa-crown me-2"></i>${owner}</div>` : '<p class="text-muted">No owner</p>';

        let officersDisplay = '<p class="text-muted">No officers</p>';
        if (officers && officers.length > 0) {
            officersDisplay = officers.map(officer =>
                `<div class="member-item officer"><i class="fas fa-user-shield me-2"></i>${officer}</div>`
            ).join('');
        }

        let membersDisplay = '<p class="text-muted">No regular members</p>';
        if (regularMembers && regularMembers.length > 0) {
            membersDisplay = regularMembers.map(member =>
                `<div class="member-item member"><i class="fas fa-user me-2"></i>${member}</div>`
            ).join('');
        }


        let alliesDisplay = '<p class="text-muted">No allies</p>';
        if (allies && allies.length > 0) {
            alliesDisplay = allies.map(ally =>
                `<div class="ally-item"><i class="fas fa-handshake me-2"></i>${ally}</div>`
            ).join('');
        }


        let requestsDisplay = '<p class="text-muted">No pending join requests</p>';
        if (requests && requests.length > 0) {
            requestsDisplay = requests.map(request =>
                `<div class="request-item"><i class="fas fa-user-plus me-2"></i>${request}</div>`
            ).join('');
        }


        const formattedDoubloons = bankDoubloons.toLocaleString();
        const formattedStrength = strength.toLocaleString();


        const infoContent = `
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-flag me-2"></i> ${factionName}
                    </h4>
                    <div class="d-flex align-items-center gap-2">
                        <span class="badge bg-primary">${memberCount} Members</span>
                        ${window.AUTH_DATA && window.AUTH_DATA.role === 'DEVELOPER' ? `
                            <button class="btn btn-danger btn-sm" onclick="deleteFaction('${factionName}')" title="Delete Faction">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        ` : ''}
                    </div>
                </div>

                <div class="card-body">
                    <!-- Faction Stats Overview -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="stat-card">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="stat-title">Bank Balance</div>
                                        <div class="stat-value">$${formattedDoubloons}</div>
                                    </div>
                                    <div class="stat-icon">
                                        <i class="fas fa-coins"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="stat-card">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="stat-title">Faction Strength</div>
                                        <div class="stat-value">${formattedStrength}</div>
                                    </div>
                                    <div class="stat-icon">
                                        <i class="fas fa-fist-raised"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Faction Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-users me-2"></i> Members</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <h6 class="text-primary"><i class="fas fa-crown me-2"></i> Owner</h6>
                                        <div class="ms-3 mt-2">${ownerDisplay}</div>
                                    </div>

                                    <div class="mb-3">
                                        <h6 class="text-primary"><i class="fas fa-user-shield me-2"></i> Officers</h6>
                                        <div class="ms-3 mt-2">${officersDisplay}</div>
                                    </div>

                                    <div>
                                        <h6 class="text-primary"><i class="fas fa-user me-2"></i> Members</h6>
                                        <div class="ms-3 mt-2">${membersDisplay}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-handshake me-2"></i> Allies</h5>
                                </div>
                                <div class="card-body">
                                    <div class="allies-list">
                                        ${alliesDisplay}
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i> Join Requests</h5>
                                </div>
                                <div class="card-body">
                                    <div class="requests-list">
                                        ${requestsDisplay}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('#playerData').html(infoContent);
    }


    window.searchFaction = function(factionName) {
        document.getElementById('searchType').value = 'faction';
        document.getElementById('searchInput').value = factionName;
        performSearch();
    }

    // Faction deletion function
    window.deleteFaction = function(factionName) {
        // Check if user has required role
        if (!window.AUTH_DATA || window.AUTH_DATA.role !== 'DEVELOPER') {
            Swal.fire({
                icon: 'error',
                title: 'Permission Denied',
                text: 'You do not have permission to delete factions. DEVELOPER role required.'
            });
            return;
        }

        // Show confirmation dialog with reason input
        Swal.fire({
            title: `Delete Faction: ${factionName}`,
            html: `
                <div class="text-start">
                    <p class="mb-3"><strong>Warning:</strong> This action will permanently delete the faction and remove all members from it.</p>
                    <p class="mb-3">This action affects:</p>
                    <ul class="mb-3">
                        <li>All faction members will be removed</li>
                        <li>All allied factions will be updated</li>
                        <li>Faction data will be permanently deleted</li>
                    </ul>
                    <label for="deletion-reason" class="form-label"><strong>Reason for deletion:</strong></label>
                    <textarea id="deletion-reason" class="form-control" rows="3" placeholder="Enter reason for deleting this faction (e.g., inappropriate name, cheating members, etc.)"></textarea>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, Delete Faction',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const reason = document.getElementById('deletion-reason').value.trim();
                if (!reason) {
                    Swal.showValidationMessage('Please provide a reason for deletion');
                    return false;
                }
                return reason;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const reason = result.value;

                // Show loading
                Swal.fire({
                    title: 'Deleting Faction...',
                    text: 'Please wait while the faction is being deleted.',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Make API call to delete faction
                fetch('/adminapi/delete-faction.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        'X-Discord-ID': window.AUTH_DATA?.userId || '',
                        'X-Discord-Username': window.AUTH_DATA?.username || '',
                        'X-Discord-Role': window.AUTH_DATA?.role || '',
                        'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        factionName: factionName,
                        reason: reason,
                        csrf_token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Faction Deleted Successfully',
                            html: `
                                <div class="text-start">
                                    <p><strong>Faction:</strong> ${data.details.faction_name}</p>
                                    <p><strong>Members affected:</strong> ${data.details.members_affected}</p>
                                    <p><strong>Allies updated:</strong> ${data.details.allies_updated}</p>
                                </div>
                            `,
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Clear the search results
                            $('#playerData').html(`
                                <div class="empty-state">
                                    <i class="fas fa-check-circle text-success"></i>
                                    <h4>Faction Deleted</h4>
                                    <p>The faction "${factionName}" has been successfully deleted.</p>
                                </div>
                            `);
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Deletion Failed',
                            html: `
                                <div class="text-start">
                                    <p><strong>Error:</strong> ${data.error || 'Failed to delete faction'}</p>
                                    ${data.details ? `<p><strong>Details:</strong> ${data.details}</p>` : ''}
                                    ${data.debug_info ? `<p><strong>Debug Info:</strong> ${JSON.stringify(data.debug_info)}</p>` : ''}
                                </div>
                            `,
                            confirmButtonText: 'OK'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error deleting faction:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'An error occurred while deleting the faction. Please try again.',
                        confirmButtonText: 'OK'
                    });
                });
            }
        });
    }


    window.openPunishmentModal = function(xuid, playerName) {
        // Set form values
        document.getElementById('punishmentPlayerXuid').value = xuid;
        document.getElementById('punishmentPlayerName').value = playerName;

        // Set modal title with proper text handling
        const modalTitle = document.getElementById('punishmentModalLabel');
        if (modalTitle) {
            modalTitle.textContent = `Add Punishment for ${playerName}`;
            modalTitle.style.fontSize = '1.25rem';
            modalTitle.style.fontWeight = '600';
            modalTitle.style.color = 'white';
            modalTitle.style.lineHeight = '1.4';
            modalTitle.style.margin = '0';
            modalTitle.style.padding = '0';
            modalTitle.style.whiteSpace = 'nowrap';
            modalTitle.style.overflow = 'hidden';
            modalTitle.style.textOverflow = 'ellipsis';
        } else {
            console.warn('Punishment modal title element not found');
        }


        document.getElementById('punishmentForm').reset();


        const punishmentTypeSelect = document.getElementById('punishmentTypeSelect');
        if (punishmentTypeSelect) {
            punishmentTypeSelect.value = '';
        }


        const progressionInfo = document.getElementById('offenseProgressionInfo');
        if (progressionInfo) {
            progressionInfo.innerHTML = '';
        }


        const submitBtn = document.getElementById('submitPunishment');
        if (submitBtn) {
            submitBtn.disabled = false;
        }


        const punishmentModal = new bootstrap.Modal(document.getElementById('punishmentModal'));
        punishmentModal.show();


        loadPunishments(); // Now uses actual player name from global currentPlayerData
    }


    window.openNoteModal = function(xuid, playerName) {

        document.getElementById('notePlayerXuid').value = xuid;
        document.getElementById('notePlayerName').value = playerName;


        document.getElementById('noteModalLabel').textContent = `Add Note for ${playerName}`;


        document.getElementById('noteForm').reset();


        const noteModal = new bootstrap.Modal(document.getElementById('noteModal'));
        noteModal.show();
    }


    function submitPunishment() {
        const form = document.getElementById('punishmentForm');


        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }


        const xuid = document.getElementById('punishmentPlayerXuid').value;
        const username = document.getElementById('punishmentPlayerName').value;
        const type = document.getElementById('punishmentType').value;
        const reason = document.getElementById('punishmentReason').value;
        const duration = document.getElementById('punishmentDuration').value;
        const durationUnit = document.getElementById('punishmentDurationUnit').value;
        const isPublic = document.getElementById('punishmentPublic').checked;


        const data = {
            xuid: xuid,
            username: username,
            type: type,
            reason: reason,
            duration: durationUnit === 'permanent' ? -1 : parseInt(duration),
            duration_unit: durationUnit === 'permanent' ? 'permanent' : durationUnit,
            public: isPublic
        };


        const submitButton = document.getElementById('submitPunishment');
        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Applying...';


        // Get fresh CSRF token for punishment submission
        $.get('/adminapi/csrf-token', function(response) {
            const csrfToken = response.token;

            $.ajax({
                url: '/adminapi/punishments/add',
                method: 'POST',
                data: JSON.stringify(data),
                contentType: 'application/json',
                headers: {
                    'X-CSRF-Token': csrfToken
                },
            success: function(response) {

                const modal = bootstrap.Modal.getInstance(document.getElementById('punishmentModal'));
                modal.hide();


                Swal.fire({
                    icon: 'success',
                    title: 'Punishment Applied',
                    text: `The ${type} has been successfully applied to ${username}.`
                });


                loadPunishments(); // Now uses actual player name from global currentPlayerData


                form.reset();
            },
            error: function(xhr) {

                if (xhr.status === 401) {
                    handleAuthError(xhr);
                    return;
                }


                const errorMessage = xhr.responseJSON?.error || 'Failed to apply punishment';
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: errorMessage
                });
            },
            complete: function() {

                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            }
            });
        }).fail(function() {
            // If CSRF token fetch fails, try with existing token
            $.ajax({
                url: '/adminapi/punishments/add',
                method: 'POST',
                data: JSON.stringify(data),
                contentType: 'application/json',
                headers: {
                    'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('punishmentModal'));
                    modal.hide();

                    Swal.fire({
                        icon: 'success',
                        title: 'Punishment Applied',
                        text: 'The punishment has been successfully applied.',
                        timer: 3000,
                        showConfirmButton: false
                    });

                    loadPunishments();
                    form.reset();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error("Error applying punishment:", {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        textStatus: textStatus,
                        errorThrown: errorThrown
                    });

                    if (xhr.status === 401) {
                        handleAuthError(xhr);
                        return;
                    }

                    let errorMessage = 'Failed to apply punishment';
                    try {
                        if (xhr.responseText) {
                            const errorResponse = JSON.parse(xhr.responseText);
                            errorMessage = errorResponse.error || errorMessage;
                        }
                    } catch (e) {
                        console.error("Error parsing error response:", e);
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error Applying Punishment',
                        text: errorMessage
                    });
                },
                complete: function() {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }
            });
        });
    }

    window.loadPunishments = function(playerNameParam) {
        console.log('loadPunishments called with:', playerNameParam);

        // Get current player data - prefer the actual player name from the data over search input
        const currentPlayer = getCurrentPlayerData();
        const actualPlayerName = currentPlayer?.playerName;
        const searchInputName = document.getElementById('searchInput')?.value?.trim();

        // Use the actual player name from the data, fallback to parameter, then search input
        const playerName = actualPlayerName || playerNameParam || searchInputName;

        console.log('Actual player name from data:', actualPlayerName);
        console.log('Search input name:', searchInputName);
        console.log('Using player name:', playerName);

        if (!playerName) {
            console.log('No player name available for punishments');
            $('#punishmentsContent').html('<div class="alert alert-warning">No player selected</div>');
            return;
        }
        console.log('Loading punishments for:', playerName);

        $('#punishmentsContent').html(`
            <div class="loading-state">
                <div class="spinner"></div>
                <p>Loading punishment history...</p>
            </div>
        `);

        $.ajax({
            url: `/adminapi/punishments.php/list/?player=${encodeURIComponent(playerName)}`,
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                const punishments = Array.isArray(response) ? response : [];

                if (punishments.length === 0) {
                    $('#punishmentsContent').html(`
                        <div class="empty-state">
                            <i class="fas fa-check-circle" style="color: #10b981;"></i>
                            <h4>No Punishments Found</h4>
                            <p>This player has a clean record with no punishment history.</p>
                        </div>
                    `);
                    return;
                }


                const activePunishments = punishments.filter(p => p.is_active);
                const inactivePunishments = punishments.filter(p => !p.is_active);

                let punishmentsHTML = '';


                if (activePunishments.length > 0) {
                    punishmentsHTML += `
                        <div style="margin-bottom: 24px;">
                            <h5 style="color: #ef4444; margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-exclamation-circle"></i>
                                Active Punishments
                            </h5>
                            ${renderPunishmentsList(activePunishments)}
                        </div>
                    `;
                }


                punishmentsHTML += `
                    <div>
                        <h5 style="color: #9ca3af; margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-history"></i>
                            Punishment History
                        </h5>
                        ${inactivePunishments.length > 0 ?
                            renderPunishmentsList(inactivePunishments) :
                            '<div class="empty-state"><i class="fas fa-check-circle" style="color: #10b981;"></i><h4>No Previous Punishments</h4><p>No previous punishments found.</p></div>'
                        }
                    </div>
                `;

                $('#punishmentsContent').html(punishmentsHTML);
            },
            error: function(xhr) {

                if (xhr.status === 401) {
                    handleAuthError(xhr);
                    return;
                }

                const errorMessage = xhr.responseJSON?.error || 'Failed to load punishments';
                $('#punishmentsContent').html(`
                    <div class="empty-state">
                        <i class="fas fa-exclamation-circle text-danger"></i>
                        <h4>Error Loading Punishments</h4>
                        <p>${errorMessage}</p>
                    </div>
                `);
            }
        });
    };


    window.loadNotes = function(playerNameParam) {
        console.log('loadNotes called with:', playerNameParam);

        // Get current player data
        const currentPlayer = getCurrentPlayerData();
        const xuid = getCurrentPlayerXuid();
        const playerName = playerNameParam || getCurrentPlayerName();

        // Check if the notes content container exists
        const notesContainer = document.getElementById('notesContent');
        console.log('Notes container exists:', !!notesContainer);
        console.log('Current player XUID:', xuid);
        console.log('Current player name:', playerName);

        if (!xuid && !playerName) {
            console.log('No player XUID or name available for notes');
            $('#notesContent').html('<div class="alert alert-warning">No player selected</div>');
            return;
        }

        // Prefer XUID over username for better reliability
        const useXuid = !!xuid;
        console.log('Loading notes for:', useXuid ? `XUID: ${xuid}` : `Username: ${playerName}`);

        $('#notesContent').html(`
            <div class="loading-state">
                <div class="spinner"></div>
                <p>Loading staff notes...</p>
            </div>
        `);


        // Build API URL with appropriate parameter
        const notesUrl = useXuid
            ? `/adminapi/player/notes?xuid=${encodeURIComponent(xuid)}`
            : `/adminapi/player/notes?username=${encodeURIComponent(playerName)}`;

        $.ajax({
            url: notesUrl,
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log('Notes API call successful');
                console.log('API URL used:', notesUrl);
                console.log('Notes response:', response);
                const notes = Array.isArray(response) ? response : [];

                if (notes.length === 0) {
                    $('#notesContent').html(`
                        <div class="empty-state">
                            <i class="fas fa-sticky-note" style="color: #3b82f6;"></i>
                            <h4>No Notes Found</h4>
                            <p>There are no staff notes for this player yet.</p>
                        </div>
                    `);
                    return;
                }


                notes.sort((a, b) => {
                    const dateA = new Date(a.created_at || 0);
                    const dateB = new Date(b.created_at || 0);
                    return dateB - dateA;
                });


                const importantNotes = notes.filter(n => n.important);
                const regularNotes = notes.filter(n => !n.important);

                let notesHTML = '';


                if (importantNotes.length > 0) {
                    notesHTML += `
                        <div style="margin-bottom: 24px;">
                            <h5 style="color: #f59e0b; margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-exclamation-circle"></i>
                                Important Notes (${importantNotes.length})
                            </h5>
                            ${renderNotesList(importantNotes)}
                        </div>
                    `;
                }


                notesHTML += `
                    <div>
                        <h5 style="color: #3b82f6; margin-bottom: 16px; display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-sticky-note"></i>
                            Regular Notes (${regularNotes.length})
                        </h5>
                        ${regularNotes.length > 0 ?
                            renderNotesList(regularNotes) :
                            '<div class="empty-state"><i class="fas fa-sticky-note" style="color: #6b7280;"></i><h4>No Regular Notes</h4><p>There are no regular notes for this player yet.</p></div>'
                        }
                    </div>
                `;

                $('#notesContent').html(notesHTML);
            },
            error: function(xhr, textStatus, errorThrown) {
                console.error("Error loading notes:", {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    textStatus: textStatus,
                    errorThrown: errorThrown
                });


                if (xhr.status === 401) {
                    handleAuthError(xhr);
                    return;
                }


                let errorMessage = 'Failed to load notes';
                try {
                    if (xhr.responseText) {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMessage = errorResponse.error || errorMessage;
                    }
                } catch (e) {
                    console.error("Error parsing error response:", e);
                }

                $('#notesContent').html(`
                    <div class="empty-state">
                        <i class="fas fa-exclamation-circle text-danger"></i>
                        <h4>Error Loading Notes</h4>
                        <p>${errorMessage}</p>
                        <button class="btn btn-primary mt-3" onclick="loadNotes('${playerName}')">
                            <i class="fas fa-sync-alt me-2"></i> Try Again
                        </button>
                    </div>
                `);
            }
        });
    };


    function renderNotesList(notes) {

        const currentUserId = window.AUTH_DATA ? window.AUTH_DATA.userId : '';
        const currentUserRole = window.AUTH_DATA ? window.AUTH_DATA.role : '';

        return notes.map(note => {
            const type = note.type || 'other';
            const isImportant = note.important === true;
            const createdDate = formatDate(note.created_at || '');
            const staffMember = note.staff_member || 'Unknown Staff';
            const staffId = note.staff_id || '';
            const content = note.content || 'No content';
            const noteId = note.note_id || '';



            const canModify = (staffId === currentUserId) ||
                             (currentUserRole === 'DEVELOPER') ||
                             (currentUserRole === 'SUPERVISOR') ||
                             (currentUserRole === 'OWNER');


            let typeIcon;
            switch (type) {
                case 'behavior':
                    typeIcon = 'fa-user-shield';
                    break;
                case 'support':
                    typeIcon = 'fa-headset';
                    break;
                case 'payment':
                    typeIcon = 'fa-credit-card';
                    break;
                case 'bug':
                    typeIcon = 'fa-bug';
                    break;
                default:
                    typeIcon = 'fa-sticky-note';
            }




            return `
                <div class="info-card note-card ${isImportant ? 'important' : ''}" data-note-id="${noteId}" data-staff-id="${staffId}" style="margin-bottom: 16px; ${isImportant ? 'border-left: 4px solid #f59e0b;' : ''}">
                    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <i class="fas ${typeIcon}" style="color: #3b82f6;"></i>
                            <span style="color: #fff; font-weight: 600;">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                            ${isImportant ? '<span style="background: #f59e0b; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px; margin-left: 8px;"><i class="fas fa-exclamation-circle"></i> Important</span>' : ''}
                        </div>
                        <div style="display: flex; gap: 8px;">
                            ${canModify ? `
                                <button style="background: none; border: none; color: #9ca3af; cursor: pointer; padding: 4px;" title="Edit Note" onclick="editNote('${noteId}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button style="background: none; border: none; color: #ef4444; cursor: pointer; padding: 4px;" title="Delete Note" onclick="deleteNote('${noteId}')">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            ` : ''}
                        </div>
                    </div>
                    <div class="card-content">
                        <div style="color: #fff; margin-bottom: 16px; line-height: 1.5;">
                            ${content.replace(/\n/g, '<br>')}
                        </div>
                        <div style="display: flex; align-items: center; gap: 12px; padding-top: 16px; border-top: 1px solid #333; color: #9ca3af; font-size: 14px;">
                            <div>Added by ${staffMember}</div>
                            <div style="margin-left: auto;">
                                <i class="far fa-calendar-alt" style="margin-right: 4px;"></i>${createdDate}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }


    function submitNote() {
        const form = document.getElementById('noteForm');


        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }


        const xuid = document.getElementById('notePlayerXuid').value;
        const username = document.getElementById('notePlayerName').value;
        const type = document.getElementById('noteType').value;
        const content = document.getElementById('noteContent').value;
        const important = document.getElementById('noteImportant').checked;




        if (!xuid || !username) {
            Swal.fire({
                icon: 'error',
                title: 'Missing Information',
                text: 'Player XUID and username are required. Please try searching for the player again.'
            });
            return;
        }


        const data = {
            xuid: xuid,
            username: username,
            type: type,
            content: content,
            important: important
        };


        const submitButton = document.getElementById('submitNote');
        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';

        // Get fresh CSRF token for this request
        $.get('/admin/adminapi/csrf-token', function(response) {
            const csrfToken = response.token;

            $.ajax({
                url: '/admin/adminapi/player/add_note',
                method: 'POST',
                data: JSON.stringify(data),
                contentType: 'application/json',
                headers: {
                    'X-CSRF-Token': csrfToken
                },
            success: function(response) {



                const modal = bootstrap.Modal.getInstance(document.getElementById('noteModal'));
                modal.hide();


                Swal.fire({
                    icon: 'success',
                    title: 'Note Added',
                    text: `The note has been successfully added for ${username}.`
                });

                // Reload notes using XUID-based method
                loadNotes();


                form.reset();
            },
            error: function(xhr, textStatus, errorThrown) {
                console.error("Error adding note:", {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    textStatus: textStatus,
                    errorThrown: errorThrown
                });


                if (xhr.status === 401) {
                    handleAuthError(xhr);
                    return;
                }


                let errorMessage = 'Failed to add note';
                try {
                    if (xhr.responseText) {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMessage = errorResponse.error || errorMessage;
                    }
                } catch (e) {
                    console.error("Error parsing error response:", e);
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Error Adding Note',
                    text: errorMessage
                });
            },
            complete: function() {

                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            }
            });
        }).fail(function() {
            // If CSRF token fetch fails, try with existing token
            $.ajax({
                url: '/adminapi/player/add_note',
                method: 'POST',
                data: JSON.stringify(data),
                contentType: 'application/json',
                headers: {
                    'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('noteModal'));
                    modal.hide();

                    Swal.fire({
                        icon: 'success',
                        title: 'Note Added Successfully',
                        text: 'The note has been added to the player\'s record.',
                        timer: 3000,
                        showConfirmButton: false
                    });

                    loadPlayerNotes(xuid, username);
                    form.reset();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error("Error adding note:", {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        textStatus: textStatus,
                        errorThrown: errorThrown
                    });

                    if (xhr.status === 401) {
                        handleAuthError(xhr);
                        return;
                    }

                    let errorMessage = 'Failed to add note';
                    try {
                        if (xhr.responseText) {
                            const errorResponse = JSON.parse(xhr.responseText);
                            errorMessage = errorResponse.error || errorMessage;
                        }
                    } catch (e) {
                        console.error("Error parsing error response:", e);
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error Adding Note',
                        text: errorMessage
                    });
                },
                complete: function() {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }
            });
        });
    }


    function renderPunishmentsList(punishments) {
        if (!punishments || punishments.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-check-circle text-success"></i>
                    <h4>No Punishments Found</h4>
                    <p>This player has a clean record with no punishment history.</p>
                </div>
            `;
        }


        let html = '<div class="row g-4 punishment-grid">';

        punishments.forEach(p => {
            const type = p.punishment_type || 'warning';
            const isActive = p.active === true || p.is_active === true;


            const issuedDate = formatDateET(p.issued_at || '');
            const expiresDate = p.expires_at ? formatDateET(p.expires_at) : 'Never';


            let typeClass, typeIcon;
            switch (type.toLowerCase()) {
                case 'ban':
                    typeClass = 'danger';
                    typeIcon = 'fa-ban';
                    break;
                case 'mute':
                    typeClass = 'warning';
                    typeIcon = 'fa-microphone-slash';
                    break;
                case 'kick':
                    typeClass = 'info';
                    typeIcon = 'fa-boot';
                    break;
                default:
                    typeClass = 'secondary';
                    typeIcon = 'fa-exclamation-triangle';
            }

            html += `
                <div class="col-md-6 col-lg-4">
                    <div class="card punishment-card ${type}" data-id="${p.punishment_id || p.id}">
                        <div class="card-header d-flex justify-content-between align-items-center ${isActive ? 'bg-' + typeClass + ' text-white' : ''}">
                            <div class="d-flex align-items-center">
                                <div class="punishment-type-icon me-2">
                                    <i class="fas ${typeIcon}"></i>
                                </div>
                                <div>
                                    <span class="fw-bold">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                                    <span class="ms-2 small">${p.punishment_id || `#${p.id}`}</span>
                                    ${isActive ? '<span class="badge bg-light text-dark ms-2">Active</span>' :
                                               '<span class="badge bg-secondary ms-2">Expired</span>'}
                                </div>
                            </div>
                            <button class="btn btn-sm ${isActive ? 'btn-light' : 'btn-outline-secondary'}" onclick="viewPunishmentDetails('${p.punishment_id || p.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="player-name mb-2 fw-bold">
                                <i class="fas fa-user me-2"></i>${p.player_name || 'Unknown Player'}
                            </div>
                            <div class="punishment-reason mb-3 p-3 bg-light rounded">
                                <div class="text-muted small mb-1">Reason:</div>
                                <div class="fst-italic">${p.reason || 'No reason provided'}</div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="small text-muted">
                                    <div><i class="far fa-calendar-plus me-1"></i> ${issuedDate}</div>
                                    ${p.expires_at ? `<div><i class="far fa-calendar-times me-1"></i> ${expiresDate}</div>` :
                                                   '<div><i class="fas fa-infinity me-1"></i> Permanent</div>'}
                                </div>
                                <div class="small text-muted">
                                    <div>By: ${p.staff_name || p.staff_member || 'Unknown Staff'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    window.showPunishmentModal = function(playerName) {
        const modal = new bootstrap.Modal(document.getElementById('punishmentModal'));
        document.getElementById('punishmentForm').setAttribute('data-player', playerName);
        modal.show();
    };


    window.viewPunishmentDetails = function(id) {
        const modal = new bootstrap.Modal(document.getElementById('punishmentDetailsModal'));
        const modalContent = document.getElementById('punishmentDetailsContent');


        modalContent.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading punishment details...</p>
            </div>
        `;


        modal.show();


        fetch(`/adminapi/punishments/details.php/?id=${encodeURIComponent(id)}`, {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                'X-Discord-ID': window.AUTH_DATA?.userId || '',
                'X-Discord-Username': window.AUTH_DATA?.username || '',
                'X-Discord-Role': window.AUTH_DATA?.role || '',
                'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(punishment => {
            displayPunishmentDetails(punishment);
        })
        .catch(error => {
            console.error('Error loading punishment details:', error);
            modalContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Error loading punishment details. Please try again later.
                </div>
            `;
        });
    };


    function displayPunishmentDetails(punishment) {
        const modalContent = document.getElementById('punishmentDetailsContent');

        const isActive = punishment.active === true;
        const type = punishment.punishment_type || 'unknown';


        let typeClass, typeIcon;
        switch (type) {
            case 'ban':
                typeClass = 'danger';
                typeIcon = 'fa-ban';
                break;
            case 'mute':
                typeClass = 'warning';
                typeIcon = 'fa-microphone-slash';
                break;
            case 'warning':
                typeClass = 'info';
                typeIcon = 'fa-exclamation-triangle';
                break;
            default:
                typeClass = 'secondary';
                typeIcon = 'fa-question-circle';
        }


        const issuedDate = formatDateET(punishment.issued_at);
        const expiresDate = punishment.expires_at ? formatDateET(punishment.expires_at) : 'Never';
        const removedDate = punishment.removed_at ? formatDateET(punishment.removed_at) : null;


        document.getElementById('punishmentDetailsModalLabel').textContent =
            `${type.charAt(0).toUpperCase() + type.slice(1)} Details - ${punishment.player_name || 'Unknown Player'}`;


        let html = `
            <div class="punishment-details">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-${typeClass} text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas ${typeIcon} me-2"></i>
                                    ${type.charAt(0).toUpperCase() + type.slice(1)} Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>ID:</strong> ${punishment.punishment_id || `#${punishment.id}`}
                                </div>
                                <div class="mb-3">
                                    <strong>Status:</strong>
                                    ${isActive ?
                                        '<span class="badge bg-success">Active</span>' :
                                        '<span class="badge bg-secondary">Expired</span>'}
                                </div>
                                <div class="mb-3">
                                    <strong>Issued Date:</strong> ${issuedDate}
                                </div>
                                <div class="mb-3">
                                    <strong>Expires:</strong> ${punishment.expires_at ? expiresDate : 'Never'}
                                </div>
                                ${removedDate ? `
                                <div class="mb-3">
                                    <strong>Removed Date:</strong> ${removedDate}
                                </div>
                                <div class="mb-3">
                                    <strong>Removed By:</strong> ${punishment.removed_by_name || 'Unknown'}
                                </div>
                                <div class="mb-3">
                                    <strong>Removal Reason:</strong> ${punishment.removed_reason || 'No reason provided'}
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user me-2"></i>
                                    Player & Staff Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Player Name:</strong> ${punishment.player_name || 'Unknown Player'}
                                </div>
                                <div class="mb-3">
                                    <strong>Player XUID:</strong> ${punishment.player_xuid || 'Unknown'}
                                </div>
                                <div class="mb-3">
                                    <strong>Issued By:</strong> ${punishment.staff_name || 'Unknown Staff'}
                                </div>
                                <div class="mb-3">
                                    <strong>Staff ID:</strong> ${punishment.staff_id || 'Unknown'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            Violation Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong>Offense Type:</strong> ${punishment.offense_type ?
                                        formatOffenseType(punishment.offense_type) :
                                        '<em class="text-muted">Not specified</em>'}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong>Offense Count:</strong>
                                    ${punishment.offense_count ?
                                        `<span class="badge bg-${punishment.offense_count > 1 ? 'warning' : 'info'}">
                                            #${punishment.offense_count}
                                        </span>` :
                                        '<em class="text-muted">Not specified</em>'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                ${punishment.evidence ? `
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-camera me-2"></i>
                            Evidence
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="punishment-evidence p-3 bg-light rounded">
                            ${punishment.evidence.includes('http') ?
                                `<a href="${punishment.evidence}" target="_blank" class="d-block mb-2">
                                    <i class="fas fa-external-link-alt me-1"></i> View Evidence
                                </a>
                                <div class="text-muted small">${punishment.evidence}</div>`
                                : punishment.evidence}
                        </div>
                    </div>
                </div>
                ` : ''}

                ${punishment.staff_notes ? `
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clipboard me-2"></i>
                            Staff Notes
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="punishment-notes p-3 bg-light rounded">
                            ${punishment.staff_notes}
                        </div>
                    </div>
                </div>
                ` : ''}



                ${isActive ? `
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-danger" onclick="removePunishment('${punishment.punishment_id || punishment.id}', '${type.endsWith('s') ? type : type + 's'}')">
                        <i class="fas fa-times me-2"></i> Remove Punishment
                    </button>
                </div>
                ` : ''}
            </div>
        `;

        modalContent.innerHTML = html;
    }

    window.issuePunishment = function() {
        const playerName = document.getElementById('searchInput').value.trim();
        const type = document.getElementById('punishmentType').value;
        const reason = document.getElementById('punishmentReason').value.trim();
        const duration = document.getElementById('punishmentDuration').value.trim();

        if (!reason) {
            Swal.fire({
                icon: 'warning',
                title: 'Missing Information',
                text: 'Please provide a valid reason for the punishment'
            });
            document.getElementById('punishmentReason').focus();
            return;
        }

        const data = {
            player: playerName,
            reason: reason,
            duration: duration
        };

        $.ajax({
            url: `/adminapi/punishments/${type}/add`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function(response) {
                const modal = bootstrap.Modal.getInstance(document.getElementById('punishmentModal'));
                modal.hide();

                Swal.fire({
                    icon: 'success',
                    title: 'Punishment Issued',
                    text: response.message || 'Punishment has been issued successfully'
                });


                if ($('#punishments-tab').hasClass('active')) {
                    loadPunishments();
                }
            },
            error: function(xhr) {
                const errorMessage = xhr.responseJSON?.error || 'Failed to issue punishment';
                Swal.fire({
                    icon: 'error',
                    title: 'Punishment Failed',
                    text: errorMessage
                });
            }
        });
    };

    window.removePunishment = function(punishmentId, type) {
        Swal.fire({
            title: 'Remove Punishment',
            text: 'Are you sure you want to remove this punishment?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, remove it'
        }).then((result) => {
            if (result.isConfirmed) {

                Swal.fire({
                    title: 'Processing...',
                    text: 'Removing punishment...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });



                const punishmentType = type.endsWith('s') ? type : type + 's';




                fetch(`/adminapi/punishments.php/${punishmentType}/remove`, {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        'X-Discord-ID': window.AUTH_DATA?.userId || '',
                        'X-Discord-Username': window.AUTH_DATA?.username || '',
                        'X-Discord-Role': window.AUTH_DATA?.role || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        punishment_id: punishmentId,
                        reason: 'Manually removed by staff'
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || `HTTP error! Status: ${response.status}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {

                    const detailsModal = bootstrap.Modal.getInstance(document.getElementById('punishmentDetailsModal'));
                    if (detailsModal) {
                        detailsModal.hide();
                    }


                    Swal.fire({
                        icon: 'success',
                        title: 'Punishment Removed',
                        text: data.message || 'Punishment has been removed successfully'
                    });


                    loadPunishments();
                })
                .catch(error => {
                    console.error('Error removing punishment:', error);


                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: error.message || 'Failed to remove punishment'
                    });
                });
            }
        });
    };


    window.logout = function() {
        Swal.fire({
            title: 'Logout',
            text: 'Are you sure you want to log out?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, log out'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '/logout';
            }
        });
    };


    window.editPlayerNotes = function(playerName) {

        const currentNotes = document.querySelector('.notes-content').innerHTML.trim();

        Swal.fire({
            title: 'Edit Player Notes',
            html: `
                <div class="mb-3">
                    <textarea id="player-notes-editor" class="form-control" rows="10">${currentNotes === 'No notes available for this player.' ? '' : currentNotes}</textarea>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Save Notes',
            cancelButtonText: 'Cancel',
            width: '600px',
            preConfirm: () => {
                return document.getElementById('player-notes-editor').value;
            }
        }).then((result) => {
            if (result.isConfirmed) {

                document.querySelector('.notes-content').innerHTML = '<div class="spinner-border spinner-border-sm text-primary me-2"></div> Saving notes...';


                $.ajax({
                    url: '/adminapi/player/notes/edit.php',
                    method: 'POST',
                    data: JSON.stringify({
                        player: playerName,
                        notes: result.value
                    }),
                    contentType: 'application/json',
                    success: function() {
                        document.querySelector('.notes-content').innerHTML = result.value || 'No notes available for this player.';

                        Swal.fire({
                            icon: 'success',
                            title: 'Notes Saved',
                            text: 'Player notes have been updated successfully.',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000
                        });
                    },
                    error: function(xhr) {

                        if (xhr.status === 401) {
                            handleAuthError(xhr);
                            return;
                        }

                        const errorMessage = xhr.responseJSON?.error || 'Failed to save notes';

                        document.querySelector('.notes-content').innerHTML = currentNotes;

                        Swal.fire({
                            icon: 'error',
                            title: 'Error Saving Notes',
                            text: errorMessage
                        });
                    }
                });
            }
        });
    };


    // Legacy tab support - keeping for compatibility
    $(document).on('shown.bs.tab', 'button[data-bs-toggle="tab"]', function(e) {
        if (e.target.id === 'punishments-tab') {
            loadPunishments();
        } else if (e.target.id === 'notes-tab') {
            loadNotes();
        } else if (e.target.id === 'accounts-tab') {
            loadConnectedAccounts();
        }
    });


    window.editNote = function(noteId) {

        const noteCard = document.querySelector(`.note-card[data-note-id="${noteId}"]`);
        if (!noteCard) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Could not find the note to edit.'
            });
            return;
        }


        const currentUserId = window.AUTH_DATA ? window.AUTH_DATA.userId : '';
        const currentUserRole = window.AUTH_DATA ? window.AUTH_DATA.role : '';


        const staffId = noteCard.getAttribute('data-staff-id') || '';



        const canModify = (staffId === currentUserId) ||
                         (currentUserRole === 'DEVELOPER') ||
                         (currentUserRole === 'SUPERVISOR') ||
                         (currentUserRole === 'OWNER');

        if (!canModify) {
            Swal.fire({
                icon: 'error',
                title: 'Permission Denied',
                text: 'You do not have permission to edit this note. Only the creator or developers+ can edit notes.'
            });
            return;
        }


        const noteContent = noteCard.querySelector('.note-content').innerHTML.replace(/<br>/g, '\n');
        const noteType = noteCard.querySelector('.note-type-badge').className.split(' ')[1] || 'other';
        const isImportant = noteCard.classList.contains('important');
        const playerName = document.getElementById('searchInput').value.trim();


        Swal.fire({
            title: 'Edit Note',
            customClass: {
                htmlContainer: 'edit-note-container',
                popup: 'edit-note-popup'
            },
            html: `
                <div class="mb-3 text-start">
                    <label class="form-label">Note Type</label>
                    <select id="edit-note-type" class="form-select">
                        <option value="behavior" ${noteType === 'behavior' ? 'selected' : ''}>Player Behavior</option>
                        <option value="support" ${noteType === 'support' ? 'selected' : ''}>Support Interaction</option>
                        <option value="payment" ${noteType === 'payment' ? 'selected' : ''}>Payment Issue</option>
                        <option value="bug" ${noteType === 'bug' ? 'selected' : ''}>Bug Report</option>
                        <option value="other" ${noteType === 'other' ? 'selected' : ''}>Other</option>
                    </select>
                </div>
                <div class="mb-3 text-start">
                    <label class="form-label">Note Content</label>
                    <textarea id="edit-note-content" class="form-control" rows="5">${noteContent.trim()}</textarea>
                </div>
                <div class="form-check mb-3 d-flex align-items-center justify-content-start">
                    <input class="form-check-input" type="checkbox" id="edit-note-important" ${isImportant ? 'checked' : ''}>
                    <label class="form-check-label ms-2" for="edit-note-important">
                        <i class="fas fa-exclamation-circle text-danger me-1"></i> Mark as important
                    </label>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Save Changes',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                return {
                    type: document.getElementById('edit-note-type').value,
                    content: document.getElementById('edit-note-content').value,
                    important: document.getElementById('edit-note-important').checked
                };
            }
        }).then((result) => {
            if (result.isConfirmed) {

                noteCard.querySelector('.note-content').innerHTML = '<div class="spinner-border spinner-border-sm text-primary me-2"></div> Saving changes...';


                const data = {
                    note_id: noteId,
                    username: playerName,
                    type: result.value.type,
                    content: result.value.content,
                    important: result.value.important
                };


                // Get fresh CSRF token for edit request
                $.get('/admin/adminapi/csrf-token', function(response) {
                    const csrfToken = response.token;

                    $.ajax({
                        url: `/admin/adminapi/player/edit_note`,
                        method: 'POST',
                        data: JSON.stringify(data),
                        contentType: 'application/json',
                        headers: {
                            'X-CSRF-Token': csrfToken
                        },
                    success: function(data) {

                        Swal.fire({
                            icon: 'success',
                            title: 'Note Updated',
                            text: data.message || 'The note has been successfully updated.',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000
                        });


                        loadNotes(playerName);
                    },
                    error: function(xhr) {

                        if (xhr.status === 401) {
                            handleAuthError(xhr);
                            return;
                        }

                        const errorMessage = xhr.responseJSON?.error || 'Failed to update note';

                        Swal.fire({
                            icon: 'error',
                            title: 'Error Updating Note',
                            text: errorMessage
                        });


                        loadNotes(playerName);
                    }
                    });
                }).fail(function() {
                    // If CSRF token fetch fails, try with existing token
                    $.ajax({
                        url: `/adminapi/player/edit_note`,
                        method: 'POST',
                        data: JSON.stringify(data),
                        contentType: 'application/json',
                        headers: {
                            'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(data) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Note Updated',
                                text: 'The note has been updated successfully.',
                                timer: 3000,
                                showConfirmButton: false
                            });

                            loadNotes(playerName);
                        },
                        error: function(xhr) {
                            let errorMessage = 'Failed to update note';
                            if (xhr.responseJSON && xhr.responseJSON.error) {
                                errorMessage = xhr.responseJSON.error;
                            }

                            Swal.fire({
                                icon: 'error',
                                title: 'Error Updating Note',
                                text: errorMessage
                            });

                            loadNotes(playerName);
                        }
                    });
                });
            }
        });
    };


    window.deleteNote = function(noteId) {
        const playerName = document.getElementById('searchInput').value.trim();


        const noteCard = document.querySelector(`.note-card[data-note-id="${noteId}"]`);
        if (!noteCard) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Could not find the note to delete.'
            });
            return;
        }


        const currentUserId = window.AUTH_DATA ? window.AUTH_DATA.userId : '';
        const currentUserRole = window.AUTH_DATA ? window.AUTH_DATA.role : '';


        const staffId = noteCard.getAttribute('data-staff-id') || '';



        const canModify = (staffId === currentUserId) ||
                         (currentUserRole === 'DEVELOPER') ||
                         (currentUserRole === 'SUPERVISOR') ||
                         (currentUserRole === 'OWNER');

        if (!canModify) {
            Swal.fire({
                icon: 'error',
                title: 'Permission Denied',
                text: 'You do not have permission to delete this note. Only the creator or developers+ can delete notes.'
            });
            return;
        }

        Swal.fire({
            title: 'Delete Note',
            text: 'Are you sure you want to delete this note? This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it'
        }).then((result) => {
            if (result.isConfirmed) {

                noteCard.style.transition = 'opacity 0.3s ease';
                noteCard.style.opacity = '0.5';


                const data = {
                    note_id: noteId,
                    username: playerName
                };


                // Get fresh CSRF token for this request
                $.get('/admin/adminapi/csrf-token')
                .done(function(response) {
                    const csrfToken = response.token;
                    console.log('Delete note CSRF token:', csrfToken);

                    $.ajax({
                        url: `/admin/adminapi/player/delete_note`,
                        method: 'POST',
                        data: JSON.stringify(data),
                        contentType: 'application/json',
                        headers: {
                            'X-CSRF-Token': csrfToken
                        },
                    success: function() {

                        noteCard.addEventListener('transitionend', function() {
                            noteCard.remove();


                            updateNoteCounts();


                            checkEmptyNotesSections();
                        });
                        noteCard.style.opacity = '0';


                        const Toast = Swal.mixin({
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true
                        });

                        Toast.fire({
                            icon: 'success',
                            title: 'Note deleted successfully'
                        });
                    },
                    error: function(xhr) {
                        console.error('Delete note error:', xhr);
                        console.error('Status:', xhr.status);
                        console.error('Response:', xhr.responseText);

                        noteCard.style.opacity = '1';


                        if (xhr.status === 401) {
                            handleAuthError(xhr);
                            return;
                        }

                        const errorMessage = xhr.responseJSON?.error || 'Failed to delete note';

                        Swal.fire({
                            icon: 'error',
                            title: 'Error Deleting Note',
                            text: errorMessage
                        });
                    }
                    });
                })
                .fail(function(xhr, status, error) {
                    // Restore note card opacity on CSRF token error
                    noteCard.style.opacity = '1';

                    console.error('Error getting CSRF token:', error);
                    console.error('CSRF token response:', xhr.responseText);

                    Swal.fire({
                        icon: 'error',
                        title: 'Security Token Error',
                        text: 'Failed to get security token. Please refresh the page and try again.',
                        confirmButtonText: 'Refresh Page'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.reload();
                        }
                    });
                });
            }
        });
    };


    function updateNoteCounts() {

        const importantNotesCount = document.querySelectorAll('.important-notes-section .note-card').length;
        const importantNotesHeader = document.querySelector('.important-notes-section h5');
        if (importantNotesHeader) {
            importantNotesHeader.innerHTML = `
                <i class="fas fa-exclamation-circle"></i>
                Important Notes (${importantNotesCount})
            `;
        }


        const regularNotesCount = document.querySelectorAll('.regular-notes-section .note-card').length;
        const regularNotesHeader = document.querySelector('.regular-notes-section h5');
        if (regularNotesHeader) {
            regularNotesHeader.innerHTML = `
                <i class="fas fa-sticky-note"></i>
                Regular Notes (${regularNotesCount})
            `;
        }
    }


    function checkEmptyNotesSections() {

        const importantNotesSection = document.querySelector('.important-notes-section');
        if (importantNotesSection) {
            const importantNotes = importantNotesSection.querySelectorAll('.note-card');
            if (importantNotes.length === 0) {
                importantNotesSection.remove();
            }
        }


        const regularNotesSection = document.querySelector('.regular-notes-section');
        if (regularNotesSection) {
            const regularNotes = regularNotesSection.querySelectorAll('.note-card');
            if (regularNotes.length === 0) {
                regularNotesSection.innerHTML = `
                    <h5 class="mb-3">
                        <i class="fas fa-sticky-note"></i>
                        Regular Notes (0)
                    </h5>
                    <div class="notes-empty-state">
                        <i class="fas fa-sticky-note"></i>
                        <h5>No Regular Notes</h5>
                        <p>There are no regular notes for this player yet.</p>
                    </div>
                `;
            }
        }


        const notesContent = document.getElementById('notesContent');
        const noteCards = notesContent.querySelectorAll('.note-card');
        if (noteCards.length === 0) {
            notesContent.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-sticky-note" style="color: #3b82f6;"></i>
                    <h4>No Notes Found</h4>
                    <p>There are no staff notes for this player yet.</p>
                </div>
            `;
        }
    }

    // Load connected accounts for a player
    window.loadConnectedAccounts = function(playerNameParam) {
        console.log('loadConnectedAccounts called with:', playerNameParam);

        // Get current player data - prefer the actual player name from the data over search input
        const currentPlayer = getCurrentPlayerData();
        const actualPlayerName = currentPlayer?.playerName;
        const searchInputName = document.getElementById('searchInput')?.value?.trim();

        // Use the actual player name from the data, fallback to parameter, then search input
        const playerName = actualPlayerName || playerNameParam || searchInputName;

        // Check if the accounts content container exists
        const accountsContainer = document.getElementById('accountsContent');
        console.log('Accounts container exists:', !!accountsContainer);
        console.log('Actual player name from data:', actualPlayerName);
        console.log('Search input name:', searchInputName);
        console.log('Using player name:', playerName);

        if (!playerName) {
            console.log('No player name available for connected accounts');
            $('#accountsContent').html('<div class="alert alert-warning">No player selected</div>');
            return;
        }
        console.log('Loading connected accounts for:', playerName);

        $('#accountsContent').html(`
            <div class="loading-state">
                <div class="spinner"></div>
                <p>Loading connected accounts...</p>
            </div>
        `);

        const apiUrl = `/api/get-discord-account?username=${encodeURIComponent(playerName)}`;
        console.log('Connected accounts API URL:', apiUrl);

        $.ajax({
            url: apiUrl,
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log('Connected accounts API call successful');
                console.log('Connected accounts API response:', response);
                if (response.success && response.data.has_discord_account) {
                    const account = response.data;
                    $('#accountsContent').html(`
                        <div class="info-card">
                            <div class="card-header" style="background: #10b981; border-color: #10b981;">
                                <i class="fas fa-link" style="color: white;"></i>
                                <h3 style="color: white;">Account Linked</h3>
                            </div>
                            <div class="card-content">
                                <div class="info-item">
                                    <span class="label">Username</span>
                                    <span class="value">${account.discord_username || 'Unknown'}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">User ID</span>
                                    <span class="value monospace">${account.discord_id || 'Unknown'}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Email</span>
                                    <span class="value">${account.discord_email || 'Not provided'}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Xbox Username</span>
                                    <span class="value">${account.xbox_username || 'Unknown'}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Linked Date</span>
                                    <span class="value">${account.linked_at ? new Date(account.linked_at).toLocaleDateString() : 'Unknown'}</span>
                                </div>
                                <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #333;">
                                    <button class="action-btn" style="background: #ef4444; border-color: #ef4444;" onclick="unlinkDiscordAccount('${playerName}')">
                                        <i class="fas fa-unlink"></i>
                                        Unlink Account
                                    </button>
                                </div>
                            </div>
                        </div>
                    `);
                } else {
                    $('#accountsContent').html(`
                        <div class="empty-state">
                            <i class="fas fa-unlink" style="color: #6b7280;"></i>
                            <h4>No Connected Accounts</h4>
                            <p>This player has not linked their account.</p>
                        </div>
                    `);
                }
            },
            error: function(xhr) {
                if (xhr.status === 401) {
                    handleAuthError(xhr);
                    return;
                }

                const errorMessage = xhr.responseJSON?.message || 'Failed to load connected accounts';
                $('#accountsContent').html(`
                    <div class="empty-state">
                        <i class="fas fa-exclamation-circle" style="color: #ef4444;"></i>
                        <h4>Error Loading Connected Accounts</h4>
                        <p>${errorMessage}</p>
                    </div>
                `);
            }
        });
    };

    // Unlink Discord account function
    window.unlinkDiscordAccount = function(playerName) {
        Swal.fire({
            title: 'Unlink Discord Account?',
            text: `Are you sure you want to unlink the Discord account for ${playerName}? This action cannot be undone.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, unlink it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '/adminapi/unlink-discord',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ username: playerName }),
                    headers: {
                        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Account Unlinked',
                                text: response.message
                            });
                            // Reload the connected accounts tab
                            loadConnectedAccounts(playerName);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: response.message || 'Failed to unlink account'
                            });
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 401) {
                            handleAuthError(xhr);
                            return;
                        }

                        const errorMessage = xhr.responseJSON?.message || 'Failed to unlink account';
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: errorMessage
                        });
                    }
                });
            }
        });
    }

    // Rank colors mapping (from player.php)
    const rankColors = {
        "player": "",
        "c": "#00FF00",
        "builder": "#000000",
        "admin": "#0000FF",
        "mlp": "#ADD8E6",
        "yt": "#FF0000",
        "youtuber": "linear-gradient(to right, #FF0000 50%, #FFFFFF 50%)",
        "support": "#D3D3F7",
        "moderator": "#00FF00",
        "vip": "#90EE90",
        "owner": "#ADD8E6",
        "mgp": "#8B0000",
        "mvp": "#FFFF00",
        "trainee": "#FFFFFF",
        "mmp": "#FFFF00",
        "secretary": "#FFC0CB"
    };

    window.getRankColor = function(rankName) {
        const rankLower = rankName.toLowerCase();
        return rankColors[rankLower] || "#FFFFFF";
    }

    window.getRankBadgeStyle = function(rankName) {
        const color = getRankColor(rankName);
        if (color.includes('gradient')) {
            return `background: ${color}; color: #000000;`;
        } else if (color === "#FFFFFF" || color === "#FFFF00") {
            return `background-color: ${color}; color: #000000;`;
        } else if (color === "#000000") {
            return `background-color: ${color}; color: #FFFFFF;`;
        } else {
            return `background-color: ${color}; color: #FFFFFF;`;
        }
    }

    // Rank Management Functions - Make them global
    window.openRankModal = function(xuid, playerName, currentRank) {
        // Show warning about player needing to be offline
        Swal.fire({
            title: 'Important Notice',
            html: `
                <div class="text-start">
                    <div class="alert alert-warning d-flex align-items-center mb-3" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            <strong></strong> The player must be offline before changing their rank.
                            Rank changes will not take effect if the player is currently online.
                        </div>
                    </div>
                    <p>Do you want to proceed with changing <strong>${playerName}</strong>'s rank?</p>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, proceed',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Set player data in the modal
                document.getElementById('rankPlayerXuid').value = xuid;
                document.getElementById('rankPlayerName').value = playerName;
                document.getElementById('rankCurrentRank').textContent = currentRank.toUpperCase();

                // Reset form
                document.getElementById('rankForm').reset();
                document.getElementById('rankSelect').value = currentRank;

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('rankModal'));
                modal.show();
            }
        });
    }

    window.submitRankChange = function() {
        const form = document.getElementById('rankForm');
        const formData = new FormData(form);

        const xuid = formData.get('xuid');
        const username = formData.get('username');
        const newRank = formData.get('new_rank');
        const reason = formData.get('reason');

        // Validate required fields
        if (!newRank) {
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Please select a rank'
            });
            return;
        }

        if (!reason.trim()) {
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Please provide a reason for the rank change'
            });
            return;
        }

        // Show confirmation dialog
        Swal.fire({
            title: 'Confirm Rank Change',
            html: `Are you sure you want to change <strong>${username}</strong>'s rank to <strong>${newRank.toUpperCase()}</strong>?<br><br><strong>Reason:</strong> ${reason}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, change rank',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                performRankChange(xuid, username, newRank, reason);
            }
        });
    }

    window.performRankChange = function(xuid, username, newRank, reason) {
        // Show loading state
        const submitButton = document.getElementById('submitRankChange');
        const originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Changing Rank...';
        submitButton.disabled = true;

        const requestData = {
            xuid: xuid,
            username: username,
            new_rank: newRank,
            reason: reason
        };

        $.ajax({
            url: '/adminapi/player-rank',
            method: 'POST',
            data: JSON.stringify(requestData),
            contentType: 'application/json',
            headers: {
                'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                // Reset button
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;

                if (response.success) {
                    // Hide modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('rankModal'));
                    modal.hide();

                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Rank Changed Successfully',
                        text: `${username}'s rank has been changed to ${newRank.toUpperCase()}`,
                        timer: 3000,
                        showConfirmButton: false
                    });

                    // Refresh player data to show new rank
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput.value === username) {
                        performSearch();
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Rank Change Failed',
                        text: response.message || 'Failed to change player rank'
                    });
                }
            },
            error: function(xhr) {
                // Reset button
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;

                if (xhr.status === 401) {
                    handleAuthError(xhr);
                    return;
                }

                const errorMessage = xhr.responseJSON?.message || 'Failed to change player rank';
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: errorMessage
                });
            }
        });
    }
});