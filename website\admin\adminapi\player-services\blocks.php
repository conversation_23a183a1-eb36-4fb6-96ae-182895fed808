<?php
/**
 * Player Services Blocks API
 * Manages blocking Discord users from specific services
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';

// Require admin role or higher
if (!is_authenticated() || !has_role(ROLE_ADMIN)) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied - Admin role required']);
    exit;
}

// Set JSON content type
header('Content-Type: application/json');

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetBlocks();
            break;
        case 'POST':
            handleAddBlock();
            break;
        case 'DELETE':
            handleRemoveBlock();
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    error_log("Player Services Blocks API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleGetBlocks() {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Failed to connect to database");
        }

        // Get blocks with pagination to prevent memory issues
        $limit = isset($_GET['limit']) ? min(intval($_GET['limit']), 1000) : 100; // Max 1000, default 100
        $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;

        $blocks = $db->player_service_blocks->find([], [
            'sort' => ['created_at' => -1],
            'limit' => $limit,
            'skip' => $offset
        ])->toArray();

        // Format blocks for display
        $formattedBlocks = array_map(function($block) {
            // Format created_at for display
            $formattedDate = 'Unknown';

            if (isset($block['created_at'])) {
                // Debug: Log what type of data we're getting
                error_log("Block created_at type: " . gettype($block['created_at']) . ", value: " . print_r($block['created_at'], true));

                if ($block['created_at'] instanceof MongoDB\BSON\UTCDateTime) {
                    try {
                        $date = $block['created_at']->toDateTime();
                        $date->setTimezone(new DateTimeZone('America/New_York'));
                        $formattedDate = $date->format('M j, Y g:i A') . ' ET';
                    } catch (Exception $e) {
                        error_log("Error formatting MongoDB date: " . $e->getMessage());
                        $formattedDate = 'Date Error';
                    }
                } elseif (is_string($block['created_at'])) {
                    // Try to parse string dates
                    try {
                        $date = new DateTime($block['created_at']);
                        $date->setTimezone(new DateTimeZone('America/New_York'));
                        $formattedDate = $date->format('M j, Y g:i A') . ' ET';
                    } catch (Exception $e) {
                        error_log("Error parsing string date: " . $block['created_at'] . " - " . $e->getMessage());
                        $formattedDate = 'Date Parse Error';
                    }
                } elseif (is_numeric($block['created_at'])) {
                    // Handle timestamp
                    try {
                        $date = new DateTime();
                        $date->setTimestamp($block['created_at']);
                        $date->setTimezone(new DateTimeZone('America/New_York'));
                        $formattedDate = $date->format('M j, Y g:i A') . ' ET';
                    } catch (Exception $e) {
                        error_log("Error formatting timestamp: " . $block['created_at'] . " - " . $e->getMessage());
                        $formattedDate = 'Timestamp Error';
                    }
                } else {
                    // Unknown format
                    error_log("Unknown date format for created_at: " . print_r($block['created_at'], true));
                    $formattedDate = 'Unknown Format';
                }
            }

            return [
                'discord_id' => $block['discord_id'] ?? '',
                'blocked_services' => $block['blocked_services'] ?? '',
                'reason' => $block['reason'] ?? '',
                'notes' => $block['notes'] ?? '',
                'created_at' => $formattedDate,
                'created_by' => $block['created_by'] ?? 'Unknown'
            ];
        }, $blocks);

        echo json_encode([
            'success' => true,
            'blocks' => $formattedBlocks
        ]);

    } catch (Exception $e) {
        error_log("Error getting blocks: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'Failed to retrieve blocks'
        ]);
    }
}

function handleAddBlock() {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
            return;
        }

        $discordId = trim($input['discord_id'] ?? '');
        $blockedServices = trim($input['blocked_services'] ?? '');
        $reason = trim($input['reason'] ?? '');
        $notes = trim($input['notes'] ?? '');

        // Validate required fields
        if (empty($discordId) || empty($blockedServices) || empty($reason)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            return;
        }

        // Validate Discord ID format
        if (!preg_match('/^\d{17,19}$/', $discordId)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid Discord ID format']);
            return;
        }

        // Validate blocked services
        $validServices = ['all', 'appeals', 'reports', 'tickets', 'applications'];
        $servicesList = explode(',', $blockedServices);
        foreach ($servicesList as $service) {
            if (!in_array(trim($service), $validServices)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid service type']);
                return;
            }
        }

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Failed to connect to database");
        }

        // Check if user is already blocked
        $existingBlock = $db->player_service_blocks->findOne(['discord_id' => $discordId]);
        if ($existingBlock) {
            http_response_code(409);
            echo json_encode(['success' => false, 'message' => 'User is already blocked']);
            return;
        }

        // Create the block
        $blockData = [
            'discord_id' => $discordId,
            'blocked_services' => $blockedServices,
            'reason' => $reason,
            'notes' => $notes,
            'created_at' => new MongoDB\BSON\UTCDateTime(),
            'created_by' => $_SESSION['discord_username'] ?? 'Unknown',
            'created_by_id' => $_SESSION['discord_user_id'] ?? 'Unknown'
        ];

        $result = $db->player_service_blocks->insertOne($blockData);

        if ($result->getInsertedCount() > 0) {
            // Log the action to staff activity log
            try {
                $db->staff_activity_log->insertOne([
                    'user_id' => $_SESSION['discord_user_id'] ?? 'Unknown',
                    'username' => $_SESSION['discord_username'] ?? 'Unknown',
                    'action' => "Blocked Discord ID {$discordId} from {$blockedServices}",
                    'type' => 'player_services',
                    'target' => $discordId,
                    'details' => "Services: {$blockedServices}\nReason: {$reason}",
                    'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                    'description' => "Blocked player services access"
                ]);
            } catch (Exception $e) {
                error_log("Error logging block action: " . $e->getMessage());
            }

            echo json_encode(['success' => true, 'message' => 'Block added successfully']);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to add block']);
        }

    } catch (Exception $e) {
        error_log("Error adding block: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to add block']);
    }
}

function handleRemoveBlock() {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
            return;
        }

        $discordId = trim($input['discord_id'] ?? '');

        if (empty($discordId)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Discord ID is required']);
            return;
        }

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Failed to connect to database");
        }

        // Get the block before removing it for logging
        $block = $db->player_service_blocks->findOne(['discord_id' => $discordId]);
        if (!$block) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Block not found']);
            return;
        }

        // Remove the block
        $result = $db->player_service_blocks->deleteOne(['discord_id' => $discordId]);

        if ($result->getDeletedCount() > 0) {
            // Log the action to staff activity log
            try {
                $db->staff_activity_log->insertOne([
                    'user_id' => $_SESSION['discord_user_id'] ?? 'Unknown',
                    'username' => $_SESSION['discord_username'] ?? 'Unknown',
                    'action' => "Unblocked Discord ID {$discordId} from {$block['blocked_services']}",
                    'type' => 'player_services',
                    'target' => $discordId,
                    'details' => "Services: {$block['blocked_services']}",
                    'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                    'description' => "Unblocked player services access"
                ]);
            } catch (Exception $e) {
                error_log("Error logging unblock action: " . $e->getMessage());
            }

            echo json_encode(['success' => true, 'message' => 'Block removed successfully']);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to remove block']);
        }

    } catch (Exception $e) {
        error_log("Error removing block: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to remove block']);
    }
}

/**
 * Check if a Discord user is blocked from a specific service
 * @param string $discordId Discord user ID
 * @param string $service Service to check (appeals, reports, tickets)
 * @return array Block information or null if not blocked
 */
function isUserBlockedFromService($discordId, $service) {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Failed to connect to database");
        }

        $block = $db->player_service_blocks->findOne(['discord_id' => $discordId]);
        
        if (!$block) {
            return null;
        }

        $blockedServices = $block['blocked_services'];
        
        // Check if blocked from all services
        if ($blockedServices === 'all') {
            return $block;
        }

        // Check if blocked from specific service
        $servicesList = explode(',', $blockedServices);
        if (in_array($service, $servicesList)) {
            return $block;
        }

        return null;

    } catch (Exception $e) {
        error_log("Error checking service block: " . $e->getMessage());
        return null;
    }
}
?>
