ref: error-driver-bulkwriteexception
content: |
  :php:`MongoDB\\Driver\\Exception\\BulkWriteException
  <mongodb-driver-exception-bulkwriteexception>` for errors related to the write
  operation. Users should inspect the value returned by :php:`getWriteResult()
  <mongodb-driver-writeexception.getwriteresult>` to determine the nature of the
  error.
---
ref: error-driver-invalidargumentexception
content: |
  :php:`MongoDB\\Driver\\Exception\\InvalidArgumentException
  <mongodb-driver-exception-invalidargumentexception>` for errors related to the
  parsing of parameters or options at the driver level.
---
ref: error-driver-runtimeexception
content: |
  :php:`MongoDB\\Driver\\Exception\\RuntimeException
  <mongodb-driver-exception-runtimeexception>` for other errors at the driver
  level (e.g. connection errors).
---
ref: error-badmethodcallexception-write-result
content: |
  :phpclass:`MongoDB\\Exception\\BadMethodCallException` if this method is
  called and the write operation used an unacknowledged :manual:`write concern
  </reference/write-concern>`.
---
ref: error-invalidargumentexception
content: |
  :phpclass:`MongoDB\\Exception\\InvalidArgumentException` for errors related to
  the parsing of parameters or options.
---
ref: error-unexpectedvalueexception
content: |
  :phpclass:`MongoDB\\Exception\\UnexpectedValueException` if the command
  response from the server was malformed.
---
ref: error-unsupportedexception
content: |
  :phpclass:`MongoDB\\Exception\\UnsupportedException` if options are used and
  not supported by the selected server (e.g. ``collation``, ``readConcern``,
  ``writeConcern``).
---
ref: error-gridfs-filenotfoundexception
content: |
  :phpclass:`MongoDB\\GridFS\\Exception\\FileNotFoundException` if no file was
  found for the selection criteria.
---
ref: error-gridfs-corruptfileexception
content: |
  :phpclass:`MongoDB\\GridFS\\Exception\\CorruptFileException` if the file's
  metadata or chunk documents contain unexpected or invalid data.
...
