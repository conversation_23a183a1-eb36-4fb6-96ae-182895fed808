<?php
/**
 * Auth Data Script
 * This file generates the AUTH_DATA JavaScript object that is used by the frontend.
 */


$userData = get_user_data();


?>
<script>
window.AUTH_DATA = {
    authenticated: <?php echo is_authenticated() ? 'true' : 'false'; ?>,
    userId: "<?php echo htmlspecialchars($_SESSION['discord_user_id'] ?? ''); ?>",
    username: "<?php echo htmlspecialchars($_SESSION['discord_username'] ?? ''); ?>",
    email: "<?php echo htmlspecialchars($_SESSION['discord_email'] ?? ''); ?>",
    role: "<?php echo htmlspecialchars($_SESSION['discord_user_role'] ?? ''); ?>",
    hasRequiredRole: <?php echo has_role(ROLE_TRAINEE) ? 'true' : 'false'; ?>,
    sessionId: "<?php echo session_id(); ?>",
    avatar: "<?php echo htmlspecialchars($_SESSION['discord_avatar'] ?? ''); ?>"
};
</script>
<meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>"><?php
