# Enable URL rewriting
RewriteEngine On

# Prevent directory listing
# First approach: Deny access to directory browsing
RewriteCond %{REQUEST_FILENAME} -d
RewriteCond %{REQUEST_FILENAME}/index.php !-f
RewriteCond %{REQUEST_FILENAME}/index.html !-f
RewriteRule ^(.*)$ - [F,L]

# Handle API endpoints without .php extension
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/?$ $1.php [L]

# Handle nested API endpoints
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/([^/]+)/?$ $1/$2.php [L]

# Specific rules for appeals endpoints
RewriteRule ^appeals/([^/]+)/approve$ appeals/approve.php?id=$1 [L,QSA]
RewriteRule ^appeals/([^/]+)/deny$ appeals/deny.php?id=$1 [L,QSA]

# If a directory or a file exists, use it directly
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# Otherwise, redirect to the index.php file in the requested directory
RewriteRule ^([^/]+)/$ $1/index.php [L]
RewriteRule ^([^/]+)/([^/]+)/$ $1/$2/index.php [L]

# Allow CORS
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, X-CSRF-TOKEN, X-Discord-ID, X-Discord-Username, X-Discord-Role, X-Discord-Avatar, X-Session-ID, X-Requested-With"
    Header set Access-Control-Allow-Credentials "true"

    # Handle OPTIONS method for CORS preflight requests
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>
