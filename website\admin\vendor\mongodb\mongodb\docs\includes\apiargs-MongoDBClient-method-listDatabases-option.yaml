arg_name: option
name: authorizedDatabases
type: boolean
description: |
  A flag that determines which databases are returned based on the user
  privileges when access control is enabled. For more information, see the
  `listDatabases command documentation <https://mongodb.com/docs/manual/reference/command/listDatabases/>`_.

  For servers < 4.0.5, this option is ignored.

  .. versionadded:: 1.7
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.13
---
arg_name: option
name: filter
type: array|object
description: |
  A query expression to filter the list of databases.

  You can specify a query expression for database fields (e.g. ``name``,
  ``sizeOnDisk``, ``empty``).

  .. versionadded:: 1.3
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: maxTimeMS
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
