================================
MongoDB\\Collection::aggregate()
================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Collection::aggregate()

   Executes an :manual:`aggregation framework pipeline
   </core/aggregation-pipeline>` operation on the collection.

   .. code-block:: php

      function aggregate(array $pipeline, array $options = []): Traversable

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBCollection-method-aggregate-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBCollection-method-aggregate-option.rst

Return Values
-------------

A :php:`MongoDB\\Driver\\Cursor <class.mongodb-driver-cursor>` or
:php:`ArrayIterator <arrayiterator>` object. In both cases, the return value
will be :php:`Traversable <traversable>`.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unexpectedvalueexception.rst
.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

.. _php-coll-agg-method-behavior:

Behavior
--------

:phpmethod:`MongoDB\\Collection::aggregate()`'s return value depends on the
MongoDB server version and whether the ``useCursor`` option is specified. If
``useCursor`` is ``true``, a :php:`MongoDB\\Driver\\Cursor
<class.mongodb-driver-cursor>` object is returned. If ``useCursor`` is
``false``, an :php:`ArrayIterator <arrayiterator>` is returned that wraps the
``result`` array from the command response document. In both cases, the return
value will be :php:`Traversable <traversable>`.

Examples
--------

The following aggregation example uses a collection called ``names`` and groups
the ``first_name`` field together, counts the total number of results in each
group, and sorts the results by name.

.. code-block:: php

   <?php

   $collection = (new MongoDB\Client)->test->names;

   $cursor = $collection->aggregate(
       [
           ['$group' => ['_id' => '$first_name', 'name_count' => ['$sum' => 1]]],
           ['$sort' => ['_id' => 1]],
       ]
   );

See Also
--------

- :phpmethod:`MongoDB\\Database::aggregate()`
- :manual:`aggregate </reference/command/aggregate>` command reference in the
  MongoDB manual
- :manual:`Aggregation Pipeline </core/aggregation-pipeline>` documentation in
  the MongoDB Manual
