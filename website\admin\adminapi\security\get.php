<?php
/**
 * Security Settings Get API Endpoint
 * Retrieves current security settings like session timeout, login attempts, and IP whitelist
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check authentication
if (!isset($_SESSION['discord_user_id'])) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check authorization - only DEVELOPER or higher can view security settings
if (!has_role(ROLE_DEVELOPER)) {
    error_log("Security Settings API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'message' => 'Developer privileges required']);
    exit;
}

// Only accept GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get database connection
    $mongo = get_mongo_connection();
    $db = $mongo->selectDatabase('mmc');
    
    // Default settings
    $settings = [
        'sessionTimeout' => 60, // Default: 60 minutes
        'loginAttempts' => 5,   // Default: 5 attempts
        'enableIpWhitelist' => false,
        'ipWhitelist' => '',
        // Current IP removed for security - available in server logs if needed
    ];
    
    // Check if settings collection exists
    $collections = [];
    foreach ($db->listCollections() as $collectionInfo) {
        $collections[] = $collectionInfo->getName();
    }
    
    if (in_array('system_settings', $collections)) {
        $collection = $db->selectCollection('system_settings');
        
        // Get session timeout
        $sessionTimeout = $collection->findOne(['setting_name' => 'session_timeout']);
        if ($sessionTimeout && isset($sessionTimeout['value'])) {
            $settings['sessionTimeout'] = intval($sessionTimeout['value']);
        }
        
        // Get login attempts
        $loginAttempts = $collection->findOne(['setting_name' => 'login_attempts']);
        if ($loginAttempts && isset($loginAttempts['value'])) {
            $settings['loginAttempts'] = intval($loginAttempts['value']);
        }
        
        // Get IP whitelist
        $ipWhitelist = $collection->findOne(['setting_name' => 'ip_whitelist']);
        if ($ipWhitelist) {
            $settings['enableIpWhitelist'] = isset($ipWhitelist['enabled']) ? (bool)$ipWhitelist['enabled'] : false;
            
            if (isset($ipWhitelist['ips']) && is_array($ipWhitelist['ips'])) {
                // Format IP addresses as a string, one per line
                $settings['ipWhitelist'] = implode("\n", $ipWhitelist['ips']);
            }
        }
    }
    
    // Return settings
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'settings' => $settings
    ]);
    
} catch (Exception $e) {
    error_log("Security Settings API Error: " . $e->getMessage());
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode([
        'success' => false,
        'message' => 'An internal server error occurred. Please try again later.'
    ]);
}
