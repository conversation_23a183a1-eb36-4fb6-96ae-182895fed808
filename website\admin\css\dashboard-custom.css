/* Custom styles for dashboard */

/* Activity items */
.activity-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0; /* Ensures text wrapping works properly */
}

.activity-content h6 {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.activity-content p {
    margin-bottom: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
    white-space: pre-line; /* Preserves line breaks and spacing */
    word-break: break-word; /* Ensures long words don't overflow */
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    white-space: nowrap;
    margin-left: 1rem;
}

/* Announcement styling */
.bg-gradient-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    color: white;
}
