<?php
/**
 * Player Account Portal - Reports
 * View and manage submitted reports
 */

require_once __DIR__ . '/../includes/core.php';
require_once __DIR__ . '/../includes/player-auth.php';
require_once __DIR__ . '/../includes/layout-unified.php';

// Require player authentication
require_player_auth();

$player_data = get_player_data();
$submissions = get_player_submissions($player_data['discord_id']);
$reports = $submissions['reports'];

$pageTitle = "My Reports - MassacreMC";
renderHeader($pageTitle, ['/css/services.css', '/css/account-portal.css']);
renderNavbar();
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-5 fw-bold">
                        <i class="fas fa-flag me-3 text-danger"></i>My Reports
                    </h1>
                    <p class="lead text-muted">View and track your submitted player reports</p>
                </div>
                <a href="/report" class="btn btn-danger">
                    <i class="fas fa-plus me-2"></i>New Report
                </a>
            </div>

            <?php if (empty($reports)): ?>
            <!-- No Reports -->
            <div class="text-center py-5">
                <i class="fas fa-flag fa-5x text-muted mb-4"></i>
                <h3>No Reports Submitted</h3>
                <p class="text-muted mb-4">You haven't submitted any player reports yet.</p>
                <a href="/report" class="btn btn-danger">
                    <i class="fas fa-plus me-2"></i>Submit a Report
                </a>
            </div>
            <?php else: ?>
            
            <!-- Reports List -->
            <div class="row">
                <?php foreach ($reports as $report): ?>
                <div class="col-12 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1">
                                    Player Report
                                </h5>
                                <small class="text-muted">
                                    Report ID: <?php echo htmlspecialchars($report['report_id'] ?? $report['_id']); ?> •
                                    Submitted: <?php echo $report['created_at']->toDateTime()->format('M j, Y g:i A'); ?>
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php
                                    echo $report['status'] === 'Pending' ? 'secondary' :
                                        (($report['status'] === 'Resolved' || $report['status'] === 'Accepted') ? 'success' :
                                        (($report['status'] === 'Dismissed' || $report['status'] === 'Denied') ? 'danger' : 'info'));
                                ?> fs-6">
                                    <?php echo $report['status']; ?>
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <p class="mb-2"><strong>Reported Player:</strong> <?php echo htmlspecialchars($report['offender_name']); ?></p>
                                    <p class="mb-2"><strong>Rule Violation:</strong>
                                        <span class="badge bg-warning text-dark">
                                            <?php echo htmlspecialchars($report['rule_broken']); ?>
                                        </span>
                                    </p>
                                    <p class="mb-3"><strong>Evidence/Description:</strong></p>
                                    <div class="border p-3 rounded" style="background-color: #f8f9fa !important; color: #333 !important; border: 1px solid #dee2e6 !important;">
                                        <?php echo nl2br(htmlspecialchars($report['evidence'])); ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <?php if (isset($report['last_updated'])): ?>
                                    <p class="mb-1"><strong>Last Updated:</strong>
                                        <?php echo $report['last_updated']->toDateTime()->format('M j, Y g:i A'); ?>
                                    </p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Status-specific information -->
                            <?php if ($report['status'] === 'Resolved' || $report['status'] === 'Accepted'): ?>
                            <div class="alert alert-success mt-3" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Report <?php echo $report['status']; ?>!</strong> Thank you for helping keep our community safe. Appropriate action has been taken.
                            </div>
                            <?php elseif ($report['status'] === 'Dismissed'): ?>
                            <div class="alert alert-secondary mt-3" role="alert">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Report Dismissed.</strong> After review, no action was deemed necessary for this report. You can contact suport if you have any questions.
                            </div>
                            <?php elseif ($report['status'] === 'Denied'): ?>
                            <div class="alert alert-danger mt-3" role="alert">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>Report Denied.</strong> This report was reviewed and denied due to insufficient evidence or invalid claims.
                            </div>
                            <?php elseif ($report['status'] === 'Pending'): ?>
                            <div class="alert alert-warning mt-3" role="alert">
                                <i class="fas fa-clock me-2"></i>
                                <strong>Under Review.</strong> Your report is being investigated by our moderation team.
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <?php endif; ?>

            <!-- Help Section -->
            <div class="row mt-5">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-flag fa-3x text-info mb-3"></i>
                            <h5>Report Guidelines</h5>
                            <p class="text-muted">Learn about our reporting process and what makes a successful report.</p>
                            <a href="/rules" class="btn btn-outline-info">View Guidelines</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-question-circle fa-3x text-info mb-3"></i>
                            <h5>Need Help?</h5>
                            <p class="text-muted">Contact our support team if you have any questions or concerns.</p>
                            <a href="/help" class="btn btn-outline-primary">Contact Support</a>
                        </div>
                    </div>
                </div>
            </div>


            <!-- Back to Dashboard -->
            <div class="text-center mt-4">
                <a href="/account/" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<?php
renderFooter(['/js/account-portal.js', '/js/account-portal-mobile.js']);
?>
