======================================
MongoDB\\GridFS\\Bucket::__construct()
======================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::__construct()

   Constructs a new :phpclass:`Bucket <MongoDB\\GridFS\\Bucket>` instance.

   .. code-block:: php

      function __construct(MongoDB\Driver\Manager $manager, string $databaseName, array $options = [])

   This constructor has the following parameters:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-construct-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-construct-option.rst

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-invalidargumentexception.rst

Behavior
--------

If you construct a Bucket explicitly, the Bucket inherits any options
from the :php:`MongoDB\\Driver\\Manager <class.mongodb-driver-manager>` object.
If you select the Bucket from a :phpclass:`Database <MongoDB\\Database>` object,
the Bucket inherits its options from that object.

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   var_dump($bucket);

The output would then resemble::

   object(MongoDB\GridFS\Bucket)#3053 (2) {
     ["bucketName"]=>
     string(4) "test"
     ["databaseName"]=>
     string(11) "phplib_test"
   }

See Also
--------

- :phpmethod:`MongoDB\\Database::selectGridFSBucket()`
