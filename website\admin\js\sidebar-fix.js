/**
 * Fix for toggleSidebar function to handle different page layouts
 * This script overrides the original toggleSidebar function to make it more robust
 */
(function() {

    document.addEventListener('DOMContentLoaded', function() {

        window.toggleSidebar = function() {
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content') || document.querySelector('.admin-content');
            const body = document.body;
            const backdrop = document.querySelector('.sidebar-backdrop');

            if (sidebar) {
                sidebar.classList.toggle('active');
            }
            
            if (content) {
                content.classList.toggle('active');
            }
            
            if (body) {
                body.classList.toggle('sidebar-open');
            }


            if (backdrop) {
                if (body.classList.contains('sidebar-open')) {
                    backdrop.style.display = 'block';

                    setTimeout(() => {
                        backdrop.style.opacity = '1';
                    }, 10);
                } else {
                    backdrop.style.opacity = '0';

                    setTimeout(() => {
                        backdrop.style.display = 'none';
                    }, 300); // Match the transition duration
                }
            }
        };


        if (!document.querySelector('.sidebar-backdrop')) {
            const backdrop = document.createElement('div');
            backdrop.className = 'sidebar-backdrop';
            backdrop.style.display = 'none';
            backdrop.style.position = 'fixed';
            backdrop.style.top = '0';
            backdrop.style.left = '0';
            backdrop.style.width = '100%';
            backdrop.style.height = '100%';
            backdrop.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            backdrop.style.zIndex = '1030';
            backdrop.style.opacity = '0';
            backdrop.style.transition = 'opacity 0.3s ease';
            

            backdrop.addEventListener('click', function() {
                toggleSidebar();
            });
            
            document.body.appendChild(backdrop);
        }
    });
})();
