/* Punishment System Styles */

/* Punishment History Section */
.punishment-history {
    margin-top: 2rem;
}

.punishment-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.punishment-history-header h3 {
    margin-bottom: 0;
}

/* Punishment Cards */
.punishment-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.punishment-card {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    background-color: var(--bg-card);
    border: none;
}

.punishment-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
}

.punishment-card .card-header {
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.punishment-card .card-body {
    padding: 1.25rem;
}

/* Punishment Types */
.punishment-card.ban {
    border-left: 4px solid var(--danger-color);
}

.punishment-card.ban .card-header {
    background-color: rgba(231, 76, 60, 0.1);
}

.punishment-card.mute {
    border-left: 4px solid var(--warning-color);
}

.punishment-card.mute .card-header {
    background-color: rgba(243, 156, 18, 0.1);
}

.punishment-card.warning {
    border-left: 4px solid var(--info-color);
}

.punishment-card.warning .card-header {
    background-color: rgba(52, 152, 219, 0.1);
}

/* Punishment Content */
.punishment-reason {
    font-style: italic;
    color: var(--text-secondary);
    padding: 0.75rem 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius-sm);
    margin-bottom: 1rem;
    border-left: 3px solid rgba(0, 0, 0, 0.1);
}

.punishment-meta {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.5rem;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.punishment-meta div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Active Punishment Indicator */
.active-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    background-color: rgba(231, 76, 60, 0.15);
    color: var(--danger-color);
}

.active-indicator i {
    font-size: 0.7rem;
}

/* Punishment Modal */
.punishment-modal .modal-content {
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.punishment-modal .modal-header {
    background-color: var(--primary-color);
    color: var(--text-light);
    border-bottom: none;
}

.punishment-modal .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: white !important;
    line-height: 1.4;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 40px); /* Account for close button */
}

.punishment-modal .modal-body {
    padding: 1.5rem;
}

.punishment-modal .form-label {
    font-weight: 600;
    color: var(--text-dark);
}

.punishment-modal .form-text {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Punishment Type Selector */
.punishment-type-selector {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.punishment-type-option {
    flex: 1;
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    border: 2px solid var(--border-color);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.punishment-type-option:hover {
    border-color: var(--accent-color);
    background-color: rgba(58, 134, 255, 0.05);
}

.punishment-type-option.selected {
    border-color: var(--accent-color);
    background-color: rgba(58, 134, 255, 0.1);
}

.punishment-type-option i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.punishment-type-option.ban i {
    color: var(--danger-color);
}

.punishment-type-option.mute i {
    color: var(--warning-color);
}

.punishment-type-option.warning i {
    color: var(--info-color);
}

.punishment-type-option span {
    font-weight: 600;
}

/* Duration Selector */
.duration-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.duration-option {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.duration-option:hover {
    border-color: var(--accent-color);
    background-color: rgba(58, 134, 255, 0.05);
}

.duration-option.selected {
    border-color: var(--accent-color);
    background-color: rgba(58, 134, 255, 0.1);
    color: var(--accent-color);
}

/* Custom Duration Input */
.custom-duration {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.custom-duration input {
    width: 80px;
}

/* Reason Templates */
.punishment-reason-group {
    position: relative;
}

.templates-dropdown-wrapper {
    position: relative;
}

.reason-templates {
    margin-bottom: 1rem;
}

.reason-templates .dropdown-menu,
.dropdown-menu.punishment-templates {
    width: 100%;
    padding: 0.5rem;
    max-height: 200px;
    overflow-y: auto;
    position: absolute;
    z-index: 1070;
    bottom: 100%;
    top: auto;
    margin-bottom: 5px;
}

.reason-template-item {
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.reason-template-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Confirmation Section */
.confirmation-section {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    margin-top: 1.5rem;
}

.confirmation-section .form-check {
    margin-bottom: 0.5rem;
}

/* Empty State */
.empty-punishments {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.empty-punishments i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-punishments h4 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.empty-punishments p {
    max-width: 400px;
    margin: 0 auto;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .punishment-list {
        grid-template-columns: 1fr;
    }

    .punishment-type-selector {
        flex-direction: column;
        gap: 0.5rem;
    }

    .punishment-meta {
        flex-direction: column;
        align-items: flex-start;
    }
}
