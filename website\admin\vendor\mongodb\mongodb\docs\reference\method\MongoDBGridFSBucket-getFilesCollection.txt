=============================================
MongoDB\\GridFS\\Bucket::getFilesCollection()
=============================================

.. versionadded:: 1.2

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::getFilesCollection()

   Returns the files collection used by the bucket.

   .. code-block:: php

      function getFilesCollection(): MongoDB\Collection

Return Values
-------------

A :phpclass:`MongoDB\\Collection` object for the files collection.

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $filesCollection = $bucket->getFilesCollection();

   var_dump($filesCollection->getCollectionName());

The output would then resemble::

   string(8) "fs.files"

