<?php
/**
 * Admin API endpoint to unlink Discord accounts from players
 * Admin only functionality
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/db_access.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Require authentication and minimum role (Admin+ can unlink accounts)
require_auth(ROLE_ADMIN);

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Verify CSRF token
$csrf_token = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? $_POST['csrf_token'] ?? '';
if (empty($csrf_token) || !verify_csrf_token($csrf_token)) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['username'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Username is required']);
        exit;
    }
    
    $username = trim($input['username']);
    $staff_id = $_SESSION['discord_user_id'];

    if (empty($username)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Username cannot be empty']);
        exit;
    }

    // Validate username format
    if (strlen($username) > 50 || !preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid username format']);
        exit;
    }

    // Validate staff ID exists
    if (empty($staff_id)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Staff authentication required']);
        exit;
    }
    
    // Perform the unlink operation
    $success = unlink_player_discord_account($username, $staff_id);
    
    if ($success) {
        // Log the action for audit trail
        secure_log("Discord account unlinked", "admin_action", [
            'staff_id' => $staff_id,
            'staff_username' => $_SESSION['discord_username'] ?? 'Unknown',
            'target_username' => $username,
            'action' => 'unlink_discord_account'
        ]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Discord account successfully unlinked from ' . htmlspecialchars($username)
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Failed to unlink Discord account. Player may not have a linked account.'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error in adminapi/unlink-discord.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
