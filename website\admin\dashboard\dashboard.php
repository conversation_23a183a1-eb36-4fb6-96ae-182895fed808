<?php

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 


require_auth(ROLE_TRAINEE); // TRAINEE_ROLE or higher
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard | MassacreMC</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Sentry with Error Tracking -->
    <script
        data-cfasync="false"
        src="https://js-de.sentry-cdn.com/9a6bbd71c1cbe403f1fd74ccad8cee64.min.js"
        crossorigin="anonymous"
    ></script>
    <?php include '../includes/auth-data.php'; ?>
    <link href="../css/admin-common.css?v=1" rel="stylesheet">
    <link href="../css/modern-theme.css?v=1" rel="stylesheet">
    <style>
        /* Announcement styles */
        .announcement-item {
            transition: all 0.2s ease;
        }

        .announcement-item:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .bg-light-warning {
            background-color: rgba(255, 193, 7, 0.1);
            border-left: 3px solid #ffc107;
        }
    </style>
    <link href="../css/mobile.css?v=1" rel="stylesheet">
    <link href="../css/dashboard.css?v=1" rel="stylesheet">
    <link href="../css/dashboard-custom.css?v=1" rel="stylesheet">

    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
</head>
<body>
    <!-- Include standard navbar -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Include standard sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div id="content" class="main-content">
        <div class="page-container">
            <!-- Content Header -->
            <div class="content-header">
                <h1>Dashboard</h1>
                <p class="text-muted">Welcome back, <?php echo htmlspecialchars($_SESSION['discord_username'] ?? 'Staff'); ?>!</p>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-3 col-sm-6">
                                    <a href="/dashboard/playerinfo" class="btn btn-secondary w-100 p-3 d-flex flex-column align-items-center">
                                        <i class="fas fa-search mb-2" style="font-size: 1.5rem;"></i>
                                        <span>Search Player</span>
                                    </a>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <a href="/dashboard/reports" class="btn btn-secondary w-100 p-3 d-flex flex-column align-items-center">
                                        <i class="fas fa-flag mb-2" style="font-size: 1.5rem;"></i>
                                        <span>View Reports</span>
                                    </a>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <a href="/dashboard/appeals" class="btn btn-secondary w-100 p-3 d-flex flex-column align-items-center">
                                        <i class="fas fa-gavel mb-2" style="font-size: 1.5rem;"></i>
                                        <span>View Appeals</span>
                                    </a>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <a href="/dashboard/punishments" class="btn btn-secondary w-100 p-3 d-flex flex-column align-items-center">
                                        <i class="fas fa-ban mb-2" style="font-size: 1.5rem;"></i>
                                        <span>Manage Punishments</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Announcements and Online Staff -->
            <div class="row g-3 mb-4">
                <div class="col-lg-8">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title">Recent Announcements</h5>
                            <button class="btn btn-icon btn-sm btn-secondary" id="refreshAnnouncements">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush" id="announcementsList">
                                <div class="text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title">Online Staff</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush" id="onlineStaff">
                                <div class="text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <!-- Include common footer with scripts -->
    <?php include '../includes/footer.php'; ?>

    <!-- Page specific scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="../js/dashboard.js?v=1"></script>


</body>
</html>