=============================
MongoDB\\Collection::rename()
=============================

.. versionadded:: 1.10

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Collection::rename()

   Rename the collection.

   .. code-block:: php

      function rename(string $toCollectionName, ?string $toDatabaseName = null, array $options = []): array|object

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBCollection-method-rename-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBCollection-method-rename-option.rst

Return Values
-------------

An array or object with the result document of the :manual:`renameCollection
</reference/command/renameCollection>` command. The return type will depend on the
``typeMap`` option.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Example
-------

The following operation renames the ``restaurants`` collection in the ``test``
database to ``places``:

.. code-block:: php

   <?php

   $collection = (new MongoDB\Client)->test->restaurants;

   $result = $collection->rename('places');

   var_dump($result);

The output would then resemble::

   object(MongoDB\Model\BSONDocument)#9 (1) {
     ["storage":"ArrayObject":private]=>
     array(1) {
       ["ok"]=>
       float(1)
     }
   }

See Also
--------

- :phpmethod:`MongoDB\\Database::renameCollection()`
- :manual:`renameCollection </reference/command/renameCollection>` command reference in the MongoDB
  manual
