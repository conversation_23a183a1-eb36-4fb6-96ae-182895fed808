===================================
MongoDB\\Model\\IndexInfo::isText()
===================================

.. versionadded:: 1.4

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\IndexInfo::isText()

   Return whether the index is a :manual:`text </core/index-text>` index.

   .. code-block:: php

      function isText(): boolean

Return Values
-------------

A boolean indicating whether the index is a text index.

Examples
--------

.. code-block:: php

   <?php

   $collection = (new MongoDB\Client)->selectCollection('test', 'restaurants');

   $collection->createIndex(['name' => 'text']);

   foreach ($collection->listIndexes() as $index) {
       if ($index->isText()) {
           printf("%s has default language: %d\n", $index->getName(), $index['default_language']);
       }
   }

The output would then resemble::

   name_text has default language: english

See Also
--------

- :phpmethod:`MongoDB\\Collection::createIndex()`
- :phpmethod:`MongoDB\\Collection::listIndexes()`
- :manual:`Text Indexes </core/index-text>` reference in the MongoDB
  manual
