<?php
require_once __DIR__ . '/db_access.php';

/**
 * Get notifications for a specific user
 *
 * @param string $userId The user ID to get notifications for
 * @param int $limit Maximum number of notifications to return
 * @param bool $unreadOnly Whether to only return unread notifications
 * @param array $excludeTypes Array of notification types to exclude (e.g., ['announcement'])
 * @return array Array of notification objects
 */
function get_user_notifications($userId, $limit = 5, $unreadOnly = false, $excludeTypes = []) {
    try {
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');
        $collection = $db->selectCollection('notifications');
        $readMarkersCollection = $db->selectCollection('notification_read_markers');


        $userFilter = ['user_id' => $userId];
        if ($unreadOnly) {
            $userFilter['read'] = false;
        }


        if (!empty($excludeTypes)) {
            $userFilter['type'] = ['$nin' => $excludeTypes];
        }

        $userNotifications = $collection->find($userFilter, [
            'sort' => ['created_at' => -1]
        ])->toArray();


        $globalFilter = ['user_id' => 'all'];


        if (!empty($excludeTypes)) {
            $globalFilter['type'] = ['$nin' => $excludeTypes];
        }

        $globalNotifications = $collection->find($globalFilter, [
            'sort' => ['created_at' => -1]
        ])->toArray();


        $readMarkers = [];
        $globalNotificationIds = array_map(function($notification) {
            return (string)$notification['_id'];
        }, $globalNotifications);

        if (!empty($globalNotificationIds)) {
            $markers = $readMarkersCollection->find([
                'notification_id' => ['$in' => $globalNotificationIds],
                'user_id' => $userId
            ])->toArray();

            foreach ($markers as $marker) {
                $readMarkers[$marker['notification_id']] = true;
            }
        }


        $processedGlobalNotifications = [];
        foreach ($globalNotifications as $document) {
            $notificationId = (string)$document['_id'];
            $isRead = isset($readMarkers[$notificationId]);


            if ($unreadOnly && $isRead) {
                continue;
            }

            $notification = [
                'id' => $notificationId,
                'type' => $document['type'] ?? 'info',
                'title' => $document['title'] ?? 'Notification',
                'message' => $document['message'] ?? '',
                'created_at' => $document['created_at'] ?? date('c'),
                'read' => $isRead,
                'link' => $document['link'] ?? null,
                'icon' => get_notification_icon($document['type'] ?? 'info')
            ];

            $processedGlobalNotifications[] = $notification;
        }


        $processedUserNotifications = [];
        foreach ($userNotifications as $document) {
            $notification = [
                'id' => (string)$document['_id'],
                'type' => $document['type'] ?? 'info',
                'title' => $document['title'] ?? 'Notification',
                'message' => $document['message'] ?? '',
                'created_at' => $document['created_at'] ?? date('c'),
                'read' => $document['read'] ?? false,
                'link' => $document['link'] ?? null,
                'icon' => get_notification_icon($document['type'] ?? 'info')
            ];

            $processedUserNotifications[] = $notification;
        }


        $allNotifications = array_merge($processedUserNotifications, $processedGlobalNotifications);


        usort($allNotifications, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });


        return array_slice($allNotifications, 0, $limit);
    } catch (Exception $e) {
        error_log('Error getting notifications: ' . $e->getMessage());
        return [];
    }
}

/**
 * Get the total count of unread notifications for a user
 *
 * @param string $userId The user ID to count notifications for
 * @param array $excludeTypes Array of notification types to exclude (e.g., ['announcement'])
 * @return int Number of unread notifications
 */
function get_unread_notification_count($userId, $excludeTypes = []) {
    try {
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');
        $collection = $db->selectCollection('notifications');
        $readMarkersCollection = $db->selectCollection('notification_read_markers');


        $userFilter = [
            'user_id' => $userId,
            'read' => false
        ];


        if (!empty($excludeTypes)) {
            $userFilter['type'] = ['$nin' => $excludeTypes];
        }

        $userUnreadCount = $collection->countDocuments($userFilter);


        $globalFilter = ['user_id' => 'all'];


        if (!empty($excludeTypes)) {
            $globalFilter['type'] = ['$nin' => $excludeTypes];
        }

        $globalNotifications = $collection->find($globalFilter)->toArray();


        $readMarkers = [];
        $globalNotificationIds = array_map(function($notification) {
            return (string)$notification['_id'];
        }, $globalNotifications);

        if (!empty($globalNotificationIds)) {
            $markers = $readMarkersCollection->find([
                'notification_id' => ['$in' => $globalNotificationIds],
                'user_id' => $userId
            ])->toArray();

            foreach ($markers as $marker) {
                $readMarkers[$marker['notification_id']] = true;
            }
        }


        $globalUnreadCount = 0;
        foreach ($globalNotifications as $notification) {
            $notificationId = (string)$notification['_id'];
            if (!isset($readMarkers[$notificationId])) {
                $globalUnreadCount++;
            }
        }

        return $userUnreadCount + $globalUnreadCount;
    } catch (Exception $e) {
        error_log('Error counting notifications: ' . $e->getMessage());
        return 0;
    }
}

/**
 * Mark a notification as read
 *
 * @param string $notificationId The ID of the notification to mark as read
 * @param string $userId The user ID who owns the notification
 * @return bool Whether the operation was successful
 */
function mark_notification_read($notificationId, $userId) {
    try {
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');
        $collection = $db->selectCollection('notifications');


        $notification = $collection->findOne(['_id' => new MongoDB\BSON\ObjectId($notificationId)]);

        if ($notification && $notification['user_id'] === 'all') {


            $readMarkersCollection = $db->selectCollection('notification_read_markers');

            $existingMarker = $readMarkersCollection->findOne([
                'notification_id' => (string)$notification['_id'],
                'user_id' => $userId
            ]);

            if (!$existingMarker) {

                $readMarkersCollection->insertOne([
                    'notification_id' => (string)$notification['_id'],
                    'user_id' => $userId,
                    'read_at' => date('c')
                ]);
            }

            return true;
        } else {

            $result = $collection->updateOne(
                [
                    '_id' => new MongoDB\BSON\ObjectId($notificationId),
                    'user_id' => $userId
                ],
                ['$set' => ['read' => true]]
            );

            return $result->getModifiedCount() > 0;
        }
    } catch (Exception $e) {
        error_log('Error marking notification as read: ' . $e->getMessage());
        return false;
    }
}

/**
 * Mark all notifications as read for a user
 *
 * @param string $userId The user ID to mark all notifications as read for
 * @return bool Whether the operation was successful
 */
function mark_all_notifications_read($userId) {
    try {
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');
        $collection = $db->selectCollection('notifications');


        $result = $collection->updateMany(
            [
                'user_id' => $userId,
                'read' => false
            ],
            ['$set' => ['read' => true]]
        );


        $globalNotifications = $collection->find(['user_id' => 'all']);
        $readMarkersCollection = $db->selectCollection('notification_read_markers');

        foreach ($globalNotifications as $notification) {

            $existingMarker = $readMarkersCollection->findOne([
                'notification_id' => (string)$notification['_id'],
                'user_id' => $userId
            ]);

            if (!$existingMarker) {

                $readMarkersCollection->insertOne([
                    'notification_id' => (string)$notification['_id'],
                    'user_id' => $userId,
                    'read_at' => date('c')
                ]);
            }
        }

        return true;
    } catch (Exception $e) {
        error_log('Error marking all notifications as read: ' . $e->getMessage());
        return false;
    }
}

/**
 * Create a new notification
 *
 * @param string $userId The user ID to create the notification for (or 'all' for global)
 * @param string $type The type of notification (info, warning, success, danger)
 * @param string $title The notification title
 * @param string $message The notification message
 * @param string|null $link Optional link to include with the notification
 * @return bool Whether the operation was successful
 */
function create_notification($userId, $type, $title, $message, $link = null) {
    try {
        $mongo = get_mongo_connection();
        $db = $mongo->selectDatabase('mmc');
        $collection = $db->selectCollection('notifications');


        $message = str_replace("\r\n", "\n", $message); // Normalize line breaks


        $notification = [
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'created_at' => date('c'),
            'read' => false
        ];


        if ($link !== null) {
            $notification['link'] = $link;
        }


        $result = $collection->insertOne($notification);

        return $result->getInsertedCount() > 0;
    } catch (Exception $e) {
        error_log('Error creating notification: ' . $e->getMessage());
        return false;
    }
}

/**
 * Create a maintenance notification for all users
 *
 * @param string $title The notification title
 * @param string $message The notification message
 * @param string $scheduledTime The scheduled maintenance time (in Eastern Time)
 * @param int $durationMinutes The expected duration in minutes
 * @return bool Whether the operation was successful
 */
function create_maintenance_notification($title, $message, $scheduledTime, $durationMinutes) {

    $formattedMessage = $message . "\n\nScheduled Time: " . $scheduledTime . " ET";
    $formattedMessage .= "\nExpected Duration: " . format_duration($durationMinutes);

    // Use 'maintenance' type instead of 'warning' to properly categorize
    return create_notification('all', 'maintenance', $title, $formattedMessage);
}

/**
 * Format a duration in minutes to a human-readable string
 *
 * @param int $minutes The duration in minutes
 * @return string Formatted duration string
 */
function format_duration($minutes) {
    if ($minutes < 60) {
        return $minutes . ' minute' . ($minutes != 1 ? 's' : '');
    } else {
        $hours = floor($minutes / 60);
        $mins = $minutes % 60;

        $result = $hours . ' hour' . ($hours != 1 ? 's' : '');
        if ($mins > 0) {
            $result .= ' ' . $mins . ' minute' . ($mins != 1 ? 's' : '');
        }

        return $result;
    }
}

/**
 * Get the appropriate icon for a notification type
 *
 * @param string $type The notification type
 * @return string The Font Awesome icon class
 */
function get_notification_icon($type) {
    switch ($type) {
        case 'success':
            return 'fa-check-circle';
        case 'warning':
            return 'fa-exclamation-triangle';
        case 'danger':
            return 'fa-exclamation-circle';
        case 'maintenance':
            return 'fa-tools';
        case 'report':
            return 'fa-flag';
        case 'appeal':
            return 'fa-balance-scale';
        case 'punishment':
            return 'fa-gavel';
        case 'announcement':
            return 'fa-bullhorn';
        case 'info':
        default:
            return 'fa-info-circle';
    }
}
