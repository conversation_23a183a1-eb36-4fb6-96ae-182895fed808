=========================================
MongoDB\\UpdateResult::getUpsertedCount()
=========================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\UpdateResult::getUpsertedCount()

   Return the number of documents that were upserted.

   .. code-block:: php

      function getUpsertedCount(): integer

   This method should only be called if the write was acknowledged.

Return Values
-------------

The total number of documents that were upserted. This should be either ``0`` or
``1`` for an acknowledged update or replace operation, depending on whether an
upsert occurred.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-badmethodcallexception-write-result.rst

See Also
--------

- :php:`MongoDB\\Driver\\WriteResult::getUpsertedCount()
  <manual/en/mongodb-driver-writeresult.getupsertedcount.php>`
