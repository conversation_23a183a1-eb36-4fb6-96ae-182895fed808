version: 2
updates:
  # Enable version updates for npm (Node.js/TypeScript dependencies)
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "krist"
    assignees:
      - "krist"
    commit-message:
      prefix: "npm"
      include: "scope"
    labels:
      - "dependencies"
      - "npm"
    allow:
      - dependency-type: "all"
    groups:
      typescript-ecosystem:
        patterns:
          - "typescript"
          - "@types/*"
          - "ts-node"
        update-types:
          - "minor"
          - "patch"

  # Enable version updates for Composer (PHP dependencies)
  - package-ecosystem: "composer"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "krist"
    assignees:
      - "krist"
    commit-message:
      prefix: "composer"
      include: "scope"
    labels:
      - "dependencies"
      - "php"
      - "composer"
    allow:
      - dependency-type: "all"
    groups:
      php-core:
        patterns:
          - "vlucas/phpdotenv"
          - "guzzlehttp/guzzle"
          - "mongodb/mongodb"
        update-types:
          - "minor"
          - "patch"

  # Enable version updates for Composer in admin directory
  - package-ecosystem: "composer"
    directory: "/admin"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "krist"
    assignees:
      - "krist"
    commit-message:
      prefix: "admin-composer"
      include: "scope"
    labels:
      - "dependencies"
      - "php"
      - "composer"
      - "admin"
    allow:
      - dependency-type: "all"

  # Enable version updates for Docker
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "krist"
    assignees:
      - "krist"
    commit-message:
      prefix: "docker"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"
    allow:
      - dependency-type: "all"

  # Enable version updates for GitHub Actions (if any workflow files exist)
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "krist"
    assignees:
      - "krist"
    commit-message:
      prefix: "actions"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
    allow:
      - dependency-type: "all"
