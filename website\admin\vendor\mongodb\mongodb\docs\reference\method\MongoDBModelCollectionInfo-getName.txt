=========================================
MongoDB\\Model\\CollectionInfo::getName()
=========================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\CollectionInfo::getName()

   Return the collection name.

   .. code-block:: php

      function getName(): string

Return Values
-------------

The collection name. This corresponds to the ``name`` field returned in the
``listCollections`` command reply.

Examples
--------

.. code-block:: php

   <?php

   $info = new CollectionInfo(['name' => 'foo']);

   echo $info->getName();

The output would then resemble::

   foo

See Also
--------

- :phpmethod:`MongoDB\\Collection::getCollectionName()`
- :manual:`listCollections </reference/command/listCollections>` command
  reference in the MongoDB manual
