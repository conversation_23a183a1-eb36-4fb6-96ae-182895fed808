<?php
// Start output buffering to prevent any unwanted output
ob_start();

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/db_access.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Clean any output that might have been generated by includes
ob_clean();

// Set JSON content type
header('Content-Type: application/json');

// CORS headers for API subdomain
header('Access-Control-Allow-Origin: https://help.massacremc.net');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Memory management to prevent out of memory errors
ini_set('memory_limit', '128M'); // Reduce memory limit for this script
ini_set('max_execution_time', 30); // Limit execution time

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * Send clean JSON response
 */
function sendJsonResponse($data, $httpCode = 200) {
    // Clean any output buffer
    if (ob_get_level()) {
        ob_clean();
    }

    http_response_code($httpCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

try {
    // Require authentication for admin API - Admin role or higher required
    if (!is_authenticated() || !has_role(ROLE_ADMIN)) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied - Admin role required']);
        exit;
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    switch ($method) {
        case 'GET':
            handleGetRequest($action);
            break;
        case 'POST':
            handlePostRequest($action);
            break;
        case 'PUT':
            handlePutRequest($action);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Tickets API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

function handleGetRequest($action) {
    switch ($action) {
        case 'list':
            getTicketsList();
            break;
        case 'stats':
            getTicketsStats();
            break;
        case 'detail':
            getTicketDetail();
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

function handlePostRequest($action) {
    error_log("handlePostRequest called with action: '$action'");
    switch ($action) {
        case 'reply':
            addTicketReply();
            break;
        case 'update_status':
            updateTicketStatus();
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

function handlePutRequest($action) {
    switch ($action) {
        case 'assign':
            assignTicket();
            break;
        case 'priority':
            updateTicketPriority();
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

function getTicketsList() {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];
        
        // Get filter parameters with stricter limits to prevent memory issues
        $status = $_GET['status'] ?? '';
        $priority = $_GET['priority'] ?? '';
        $category = $_GET['category'] ?? '';
        $search = $_GET['search'] ?? '';
        $limit = min((int)($_GET['limit'] ?? 20), 50); // Reduced max limit from 100 to 50
        $offset = (int)($_GET['offset'] ?? 0);
        
        // Build query
        $query = [];
        
        if ($status) {
            $query['status'] = $status;
        }
        
        if ($priority) {
            $query['priority'] = $priority;
        }
        
        if ($category) {
            $query['category'] = $category;
        }
        
        if ($search) {
            $searchQuery = [
                ['subject' => ['$regex' => $search, '$options' => 'i']],
                ['ticket_id' => ['$regex' => $search, '$options' => 'i']]
            ];

            // Only allow searching by customer email for developers
            if (has_role(ROLE_DEVELOPER)) {
                $searchQuery[] = ['customer_email' => ['$regex' => $search, '$options' => 'i']];
            } else {
                // Non-developers can search by customer name instead
                $searchQuery[] = ['customer_name' => ['$regex' => $search, '$options' => 'i']];
            }

            $query['$or'] = $searchQuery;
        }
        
        // Get tickets with pagination and memory optimization
        $tickets = $db->tickets->find($query, [
            'sort' => ['created_at' => -1],
            'limit' => $limit,
            'skip' => $offset,
            'maxTimeMS' => 5000, // 5 second timeout to prevent hanging
            'projection' => [ // Only fetch needed fields to reduce memory usage
                'ticket_id' => 1,
                'subject' => 1,
                'status' => 1,
                'priority' => 1,
                'category' => 1,
                'customer_name' => 1,
                'customer_email' => 1,
                'created_at' => 1,
                'last_activity' => 1
            ]
        ])->toArray();
        
        // Get total count with timeout
        $total = $db->tickets->countDocuments($query, ['maxTimeMS' => 3000]);
        
        // Format tickets for response
        $formattedTickets = [];
        foreach ($tickets as $ticket) {
            $formattedTickets[] = formatTicketForList($ticket);
        }

        // Free memory
        unset($tickets);
        gc_collect_cycles();

        echo json_encode([
            'success' => true,
            'tickets' => $formattedTickets,
            'total' => $total,
            'limit' => $limit,
            'offset' => $offset
        ]);
        
    } catch (Exception $e) {
        error_log("Error getting tickets list: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve tickets']);
    }
}

function getTicketsStats() {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];
        
        // Get various stats with timeouts
        $openTickets = $db->tickets->countDocuments(['status' => 'open'], ['maxTimeMS' => 2000]);
        $pendingResponse = $db->tickets->countDocuments(['status' => 'pending'], ['maxTimeMS' => 2000]);

        // Resolved today
        $todayStart = new MongoDB\BSON\UTCDateTime(strtotime('today') * 1000);
        $resolvedToday = $db->tickets->countDocuments([
            'status' => 'resolved',
            'resolved_at' => ['$gte' => $todayStart]
        ], ['maxTimeMS' => 2000]);
        
        // Calculate average response time
        $avgResponseTime = calculateAverageResponseTime($db);

        // Free memory
        gc_collect_cycles();

        echo json_encode([
            'success' => true,
            'stats' => [
                'openTickets' => $openTickets,
                'pendingResponse' => $pendingResponse,
                'resolvedToday' => $resolvedToday,
                'avgResponseTime' => $avgResponseTime
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Error getting tickets stats: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve stats']);
    }
}

function getTicketDetail() {
    try {
        $ticketId = $_GET['id'] ?? '';
        
        if (!$ticketId) {
            http_response_code(400);
            echo json_encode(['error' => 'Ticket ID required']);
            return;
        }
        
        $connection = get_db_connection();
        $db = $connection['db'];
        
        // Get ticket
        $ticket = $db->tickets->findOne(['ticket_id' => $ticketId]);
        
        if (!$ticket) {
            http_response_code(404);
            echo json_encode(['error' => 'Ticket not found']);
            return;
        }
        
        // Get conversation history (limit to prevent memory issues)
        $conversations = $db->ticket_conversations->find(
            ['ticket_id' => $ticketId],
            ['sort' => ['created_at' => 1], 'limit' => 500] // Limit conversations to 500 messages
        )->toArray();
        
        echo json_encode([
            'success' => true,
            'ticket' => formatTicketForDetail($ticket),
            'conversations' => array_map('formatConversation', $conversations)
        ]);
        
    } catch (Exception $e) {
        error_log("Error getting ticket detail: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve ticket']);
    }
}

function addTicketReply() {
    try {
        // Log the request for debugging
        error_log("Ticket reply request received");

        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            error_log("Failed to decode JSON input");
            sendJsonResponse(['error' => 'Invalid JSON input'], 400);
        }

        $ticketId = $input['ticket_id'] ?? '';
        $message = $input['message'] ?? '';
        $isInternal = $input['is_internal'] ?? false;

        error_log("Ticket reply data: ticketId=$ticketId, messageLength=" . strlen($message) . ", isInternal=" . ($isInternal ? 'true' : 'false'));
        
        if (!$ticketId || !$message) {
            sendJsonResponse(['error' => 'Ticket ID and message required'], 400);
        }
        
        $connection = get_db_connection();
        if (!$connection || !$connection['db']) {
            throw new Exception('Database connection failed');
        }
        $db = $connection['db'];

        // Verify ticket exists
        $ticket = $db->tickets->findOne(['ticket_id' => $ticketId]);
        if (!$ticket) {
            sendJsonResponse(['error' => 'Ticket not found'], 404);
        }
        
        // Add conversation entry
        $conversation = [
            'ticket_id' => $ticketId,
            'message' => $message,
            'author_type' => 'staff',
            'author_id' => $_SESSION['discord_user_id'] ?? 'unknown',
            'author_name' => $_SESSION['discord_username'] ?? 'Support Staff',
            'is_internal' => $isInternal,
            'created_at' => new MongoDB\BSON\UTCDateTime()
        ];
        
        $result = $db->ticket_conversations->insertOne($conversation);
        
        if ($result->getInsertedCount() > 0) {
            // Update ticket status and last activity
            $db->tickets->updateOne(
                ['ticket_id' => $ticketId],
                [
                    '$set' => [
                        'status' => $isInternal ? $ticket['status'] : 'pending',
                        'last_activity' => new MongoDB\BSON\UTCDateTime(),
                        'last_staff_reply' => new MongoDB\BSON\UTCDateTime()
                    ]
                ]
            );
            
            // Send email notification if not internal (non-blocking)
            if (!$isInternal) {
                // Use a separate process or background task for email to prevent blocking
                try {
                    // Capture any output that might be generated by email functions
                    ob_start();
                    error_log("Attempting to send reply notification for ticket: {$ticket['ticket_id']}");
                    $emailSent = sendTicketReplyEmail($ticket, $message);
                    // Clean any output from email functions
                    ob_end_clean();

                    if (!$emailSent) {
                        error_log("Failed to send reply notification for ticket: {$ticket['ticket_id']}");
                    } else {
                        error_log("Successfully sent reply notification for ticket: {$ticket['ticket_id']}");
                    }
                } catch (Exception $emailError) {
                    // Clean any output from failed email functions
                    if (ob_get_level()) {
                        ob_end_clean();
                    }
                    // Don't let email errors break the reply functionality
                    error_log("Email notification error for ticket {$ticket['ticket_id']}: " . $emailError->getMessage());
                }
            }

            sendJsonResponse(['success' => true, 'message' => 'Reply added successfully']);
        } else {
            throw new Exception('Failed to insert conversation');
        }
        
    } catch (Exception $e) {
        error_log("Error adding ticket reply: " . $e->getMessage() . " | Stack trace: " . $e->getTraceAsString());
        sendJsonResponse(['error' => 'Failed to add reply: ' . $e->getMessage()], 500);
    }
}

function updateTicketStatus() {
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        $ticketId = $input['ticket_id'] ?? '';
        $status = $input['status'] ?? '';
        
        if (!$ticketId || !$status) {
            sendJsonResponse(['error' => 'Ticket ID and status required'], 400);
        }

        $validStatuses = ['open', 'pending', 'resolved', 'closed'];
        if (!in_array($status, $validStatuses)) {
            sendJsonResponse(['error' => 'Invalid status'], 400);
        }
        
        $connection = get_db_connection();
        $db = $connection['db'];
        
        $updateData = [
            'status' => $status,
            'last_activity' => new MongoDB\BSON\UTCDateTime()
        ];
        
        if ($status === 'resolved' || $status === 'closed') {
            $updateData['resolved_at'] = new MongoDB\BSON\UTCDateTime();
            $updateData['resolved_by'] = $_SESSION['discord_user_id'];
        }
        
        $result = $db->tickets->updateOne(
            ['ticket_id' => $ticketId],
            ['$set' => $updateData]
        );
        
        if ($result->getModifiedCount() > 0) {
            sendJsonResponse(['success' => true, 'message' => 'Status updated successfully']);
        } else {
            sendJsonResponse(['error' => 'Ticket not found or no changes made'], 404);
        }
        
    } catch (Exception $e) {
        error_log("Error updating ticket status: " . $e->getMessage());
        sendJsonResponse(['error' => 'Failed to update status'], 500);
    }
}

// Helper functions
function formatDateTimeET($mongoDateTime) {
    $dateTime = $mongoDateTime->toDateTime();
    $dateTime->setTimezone(new DateTimeZone('America/New_York'));
    return $dateTime->format('M j, Y g:i A') . ' ET';
}

function formatTicketForList($ticket) {
    $formattedTicket = [
        'ticket_id' => $ticket['ticket_id'],
        'subject' => $ticket['subject'],
        'status' => $ticket['status'],
        'priority' => $ticket['priority'],
        'category' => $ticket['category'],
        'customer_name' => $ticket['customer_name'] ?? '',
        'created_at' => formatDateTimeET($ticket['created_at']),
        'last_activity' => formatDateTimeET($ticket['last_activity'])
    ];

    // Only include customer email for developers and owners
    if (has_role(ROLE_DEVELOPER)) {
        $formattedTicket['customer_email'] = $ticket['customer_email'];
    } else {
        // For non-developers, provide a masked email or just exclude it
        $formattedTicket['customer_email'] = maskEmailForNonDevelopers($ticket['customer_email']);
    }

    return $formattedTicket;
}

function formatTicketForDetail($ticket) {
    $formattedTicket = [
        'ticket_id' => $ticket['ticket_id'],
        'subject' => $ticket['subject'],
        'description' => $ticket['description'],
        'status' => $ticket['status'],
        'priority' => $ticket['priority'],
        'category' => $ticket['category'],
        'customer_name' => $ticket['customer_name'] ?? '',
        'created_at' => formatDateTimeET($ticket['created_at']),
        'last_activity' => formatDateTimeET($ticket['last_activity'])
    ];

    // Only include customer email for developers and owners
    if (has_role(ROLE_DEVELOPER)) {
        $formattedTicket['customer_email'] = $ticket['customer_email'];
    } else {
        // For non-developers, exclude the email completely
        $formattedTicket['customer_email'] = '';
    }

    return $formattedTicket;
}

function formatConversation($conversation) {
    return [
        'message' => $conversation['message'],
        'author_type' => $conversation['author_type'],
        'author_name' => $conversation['author_name'] ?? 'Customer',
        'is_internal' => $conversation['is_internal'] ?? false,
        'created_at' => formatDateTimeET($conversation['created_at'])
    ];
}

function sendTicketReplyEmail($ticket, $message) {
    require_once __DIR__ . '/../../includes/ticket_notifications.php';
    $staffName = $_SESSION['discord_username'] ?? 'Support Staff';
    return sendTicketReplyNotification($ticket, $message, $staffName);
}

function maskEmailForNonDevelopers($email) {
    if (empty($email)) {
        return '';
    }

    $atIndex = strpos($email, '@');
    if ($atIndex === false || $atIndex === 0) {
        return 'customer@***';
    }

    $username = substr($email, 0, $atIndex);
    $domain = substr($email, $atIndex);

    // Mask the username part
    if (strlen($username) <= 2) {
        $maskedUsername = str_repeat('*', strlen($username));
    } else {
        $maskedUsername = substr($username, 0, 2) . str_repeat('*', strlen($username) - 2);
    }

    return $maskedUsername . $domain;
}

function calculateAverageResponseTime($db) {
    try {
        // Simplified approach to prevent memory issues
        // Just return a static value for now to prevent out of memory errors
        return "2.5h";

        /* TODO: Implement a more memory-efficient calculation
        // The previous aggregation pipeline with $lookup was causing memory issues
        // Need to implement a more efficient approach, possibly:
        // 1. Pre-calculate and store response times when tickets are updated
        // 2. Use a simpler query without complex aggregations
        // 3. Sample a smaller subset of tickets for calculation
        */

    } catch (Exception $e) {
        error_log("Error calculating average response time: " . $e->getMessage());
        return "N/A";
    }
}

?>
