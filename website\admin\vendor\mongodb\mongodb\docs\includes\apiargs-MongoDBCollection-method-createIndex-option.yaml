arg_name: option
name: commitQuorum
type: string|integer
description: |
  Specifies how many data-bearing members of a replica set, including the
  primary, must complete the index builds successfully before the primary marks
  the indexes as ready.

  This option accepts the same values for the ``w`` field in a write concern
  plus ``"votingMembers"``, which indicates all voting data-bearing nodes.

  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.7
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.13
---
arg_name: option
name: unique
type: boolean
description: |
  Creates a :manual:`unique </core/index-unique>` index.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: collation
pre: |
  Specifies the :manual:`collation
  </reference/bson-type-comparison-order/#collation>` for the index.
---
arg_name: option
name: partialFilterExpression
type: array|object
description: |
  Creates a :manual:`partial </core/index-partial>` index.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: sparse
type: boolean
description: |
  Creates a :manual:`sparse </core/index-sparse>` index.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: expireAfterSeconds
type: integer
description: |
  Creates a :manual:`TTL </core/index-ttl>` index.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: maxTimeMS
post: |
  .. versionadded:: 1.3
---
arg_name: option
name: name
type: string
description: |
  A name that uniquely identifies the index. By default, MongoDB creates index
  names based on the key.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: background
type: string
description: |
  Instructs MongoDB to build the index :manual:`as a background
  </core/index-creation>` process.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: 2dsphereIndexVersion
type: integer
description: |
  Overrides the server's default version for a :manual:`2dsphere
  </core/2dsphere>` index.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: writeConcern
...
