<?php
/**
 * Database Initialization Script
 * Creates necessary collections and indexes if they don't exist
 */


require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/db_access.php';

echo "Starting database initialization...\n";

try {

    $connection = get_db_connection();
    $db = $connection['db'];

    if (!$db) {
        throw new Exception("Database connection failed");
    }

    echo "Connected to database successfully.\n";


    $collections = [];
    foreach ($db->listCollections() as $collectionInfo) {
        $collections[] = $collectionInfo->getName();
    }

    echo "Existing collections: " . implode(', ', $collections) . "\n";


    if (!in_array('reports', $collections)) {
        echo "Creating reports collection...\n";
        $db->createCollection('reports');
        echo "Reports collection created successfully.\n";


        echo "Creating indexes for reports collection...\n";
        $db->reports->createIndex(['report_id' => 1], ['unique' => true]);
        $db->reports->createIndex(['status' => 1]);
        $db->reports->createIndex(['created_at' => -1]);
        echo "Indexes created successfully.\n";


        echo "Inserting sample report...\n";
        $db->reports->insertOne([
            'report_id' => 'SAMPLE-001',
            'offender_name' => 'SamplePlayer',
            'rule_broken' => 'Sample Rule Violation',
            'evidence' => 'This is a sample report for testing purposes.',
            'status' => 'Pending',
            'created_at' => new MongoDB\BSON\UTCDateTime(time() * 1000),
            'reporter_name' => 'SystemAdmin',
            'reporter_id' => 'SYSTEM'
        ]);
        echo "Sample report inserted successfully.\n";
    } else {
        echo "Reports collection already exists.\n";
    }


    if (!in_array('appeals', $collections)) {
        echo "Creating appeals collection...\n";
        $db->createCollection('appeals');
        echo "Appeals collection created successfully.\n";


        echo "Creating indexes for appeals collection...\n";
        $db->appeals->createIndex(['id' => 1], ['unique' => true]);
        $db->appeals->createIndex(['status' => 1]);
        $db->appeals->createIndex(['created_at' => -1]);
        echo "Indexes created successfully.\n";
    } else {
        echo "Appeals collection already exists.\n";
    }


    if (!in_array('staff_activity', $collections)) {
        echo "Creating staff_activity collection...\n";
        $db->createCollection('staff_activity');
        echo "Staff activity collection created successfully.\n";


        echo "Creating indexes for staff_activity collection...\n";
        $db->staff_activity->createIndex(['staff_id' => 1]);
        $db->staff_activity->createIndex(['last_active' => -1]);
        echo "Indexes created successfully.\n";
    } else {
        echo "Staff activity collection already exists.\n";
    }

    // Create tickets collection
    if (!in_array('tickets', $collections)) {
        echo "Creating tickets collection...\n";
        $db->createCollection('tickets');
        echo "Tickets collection created successfully.\n";

        // Create indexes for tickets collection
        echo "Creating indexes for tickets collection...\n";
        $db->tickets->createIndex(['ticket_id' => 1], ['unique' => true]);
        $db->tickets->createIndex(['status' => 1]);
        $db->tickets->createIndex(['priority' => 1]);
        $db->tickets->createIndex(['category' => 1]);
        $db->tickets->createIndex(['customer_email' => 1]);
        $db->tickets->createIndex(['created_at' => -1]);
        $db->tickets->createIndex(['last_activity' => -1]);
        echo "Indexes created successfully.\n";
    } else {
        echo "Tickets collection already exists.\n";
    }

    // Create ticket_conversations collection
    if (!in_array('ticket_conversations', $collections)) {
        echo "Creating ticket_conversations collection...\n";
        $db->createCollection('ticket_conversations');
        echo "Ticket conversations collection created successfully.\n";

        // Create indexes for ticket_conversations collection
        echo "Creating indexes for ticket_conversations collection...\n";
        $db->ticket_conversations->createIndex(['ticket_id' => 1]);
        $db->ticket_conversations->createIndex(['created_at' => -1]);
        $db->ticket_conversations->createIndex(['author_type' => 1]);
        echo "Indexes created successfully.\n";
    } else {
        echo "Ticket conversations collection already exists.\n";
    }

    // Create player_accounts collection
    if (!in_array('player_accounts', $collections)) {
        echo "Creating player_accounts collection...\n";
        $db->createCollection('player_accounts');
        echo "Player accounts collection created successfully.\n";

        // Create indexes for player_accounts collection
        echo "Creating indexes for player_accounts collection...\n";
        $db->player_accounts->createIndex(['discord_id' => 1], ['unique' => true]);
        $db->player_accounts->createIndex(['discord_email' => 1]);
        $db->player_accounts->createIndex(['minecraft_usernames' => 1]);
        $db->player_accounts->createIndex(['verified_usernames' => 1]);
        $db->player_accounts->createIndex(['created_at' => -1]);
        $db->player_accounts->createIndex(['last_login' => -1]);
        echo "Indexes created successfully.\n";
    } else {
        echo "Player accounts collection already exists.\n";
    }

    echo "Database initialization completed successfully.\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}