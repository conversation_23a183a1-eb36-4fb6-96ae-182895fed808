===========================================
MongoDB\\GridFS\\Bucket::uploadFromStream()
===========================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::uploadFromStream()

   Creates a new GridFS file and copies the contents of a readable stream to it.

   .. code-block:: php

      function uploadFromStream(string $filename, $source, array $options = []): mixed

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-uploadFromStream-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-uploadFromStream-option.rst

Return Values
-------------

The ``_id`` field of the metadata document associated with the newly created
GridFS file. If the ``_id`` option is not specified, a new
:php:`MongoDB\\BSON\\ObjectId <class.mongodb-bson-objectid>` object will be used
by default.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $stream = fopen('php://temp', 'w+b');
   fwrite($stream, "foobar");
   rewind($stream);

   $id = $bucket->uploadFromStream('filename', $stream);

   var_dump($id);

The output would then resemble::

   object(MongoDB\BSON\ObjectId)#3009 (1) {
     ["oid"]=>
     string(24) "5acf81017e21e816e538d883"
   }

See Also
--------

- :phpmethod:`MongoDB\\GridFS\\Bucket::openUploadStream()`
