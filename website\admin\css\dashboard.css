/* Dashboard specific styles */

/* Role badges */
.role-badge {
    padding: 0.35rem 0.65rem;
    border-radius: 50rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.role-owner {
    background-color: rgba(155, 89, 182, 0.15) !important;
    color: #9b59b6 !important;
}

.role-developer {
    background-color: rgba(41, 128, 185, 0.15) !important;
    color: #2980b9 !important;
}

.role-supervisor {
    background-color: rgba(22, 160, 133, 0.15) !important;
    color: #16a085 !important;
}

.role-admin {
    background-color: rgba(231, 76, 60, 0.15) !important;
    color: #e74c3c !important;
}

.role-moderator {
    background-color: rgba(243, 156, 18, 0.15) !important;
    color: #f39c12 !important;
}

.role-trainee {
    background-color: rgba(142, 68, 173, 0.15) !important;
    color: #8e44ad !important;
}

.role-unauthorized {
    background-color: rgba(149, 165, 166, 0.15) !important;
    color: #95a5a6 !important;
}

/* Stats cards */
.stats-card {
    position: relative;
    z-index: 1;
}

.stats-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.stats-info h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: var(--text-dark);
}

.stats-info p {
    color: var(--text-muted);
    margin-bottom: 0;
    font-size: 0.875rem;
}

.card .stretched-link {
    text-decoration: none;
    color: inherit;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

/* Gradient backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--accent-color) 0%, #1a56cc 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #27ae60 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #2980b9 100%);
}

/* Quick action buttons */
.btn-light {
    background-color: var(--bg-card);
    border-color: var(--border-color);
    color: var(--text-dark);
    transition: all 0.2s ease;
}

.btn-light:hover {
    background-color: var(--bg-hover);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

/* Activity log */
.activity-item {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background-color: var(--bg-hover);
}

.activity-icon {
    background-color: var(--bg-light);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-dark);
    margin-right: 1rem;
}

.activity-content {
    flex: 1;
}

.activity-content h6 {
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.activity-content p {
    margin-bottom: 0;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.activity-time {
    color: var(--text-muted);
    font-size: 0.75rem;
    white-space: nowrap;
}

/* Online staff */
.staff-item {
    padding: 0.75rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
}

.staff-item:last-child {
    border-bottom: none;
}

.staff-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-right: 0.75rem;
    background-color: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.staff-info {
    flex: 1;
}

.staff-info h6 {
    margin-bottom: 0.125rem;
    font-weight: 600;
    font-size: 0.9375rem;
}

.staff-info p {
    margin-bottom: 0;
    color: var(--text-muted);
    font-size: 0.75rem;
}

.staff-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success-color);
}

/* Server status */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.online {
    background-color: var(--success-color);
}

.status-indicator.offline {
    background-color: var(--danger-color);
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .stats-card {
        flex-direction: column;
        text-align: center;
    }

    .stats-icon {
        margin-bottom: 1rem;
        margin-right: 0;
    }
}

@media (max-width: 576px) {
    .activity-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .activity-icon {
        margin-bottom: 0.75rem;
    }

    .activity-time {
        margin-top: 0.5rem;
        align-self: flex-start;
    }
}