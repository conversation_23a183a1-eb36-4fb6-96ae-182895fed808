==========================================
MongoDB\\Model\\CollectionInfo::isCapped()
==========================================

.. deprecated:: 1.9

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\CollectionInfo::isCapped()

   Return whether the collection is a :manual:`capped collection
   </core/capped-collections>`.

   .. code-block:: php

      function isCapped(): boolean

Return Values
-------------

A boolean indicating whether the collection is a capped collection.

This method is deprecated in favor of using
:phpmethod:`MongoDB\\Model\\CollectionInfo::getOptions()` and accessing the
``capped`` key.

Examples
--------

.. code-block:: php

   <?php

   $info = new CollectionInfo([
       'name' => 'foo',
       'options' => [
           'capped' => true,
           'size' => 1048576,
      ]
   ]);

   var_dump($info->isCapped());

The output would then resemble::

   bool(true)

See Also
--------

- :phpmethod:`MongoDB\\Model\\CollectionInfo::getCappedMax()`
- :phpmethod:`MongoDB\\Model\\CollectionInfo::getCappedSize()`
- :manual:`Capped Collections </core/capped-collections>` in the MongoDB manual
- :manual:`listCollections </reference/command/listCollections>` command
  reference in the MongoDB manual
