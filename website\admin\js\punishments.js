/**
 * Punishments Page JavaScript
 * Handles loading, filtering, and managing punishments
 */

/**
 * Format a date in Eastern Time
 * @param {string|Date} dateInput - Date to format
 * @returns {string} Formatted date string with ET timezone
 */
function formatDateET(dateInput) {
    if (!dateInput) return 'Unknown';

    try {
        const date = new Date(dateInput);


        if (isNaN(date.getTime())) {
            return 'Unknown';
        }


        const options = {
            timeZone: 'America/New_York',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };

        return date.toLocaleString('en-US', options) + ' ET';
    } catch (e) {
        return 'Unknown';
    }
}

/**
 * Format offense type for display
 * @param {string} offenseType - The offense type to format
 * @returns {string} Formatted offense type
 */
function formatOffenseType(offenseType) {
    if (!offenseType) return '';


    let formatted = offenseType.replace(/_/g, ' ');


    formatted = formatted.split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

    return formatted;
}

document.addEventListener('DOMContentLoaded', function() {

    loadPunishments();


    document.getElementById('applyFilters').addEventListener('click', function() {
        loadPunishments();
    });

    document.getElementById('resetFilters').addEventListener('click', function() {
        resetFilters();
    });




    const addPunishmentModal = document.getElementById('addPunishmentModal');
    if (addPunishmentModal) {

        addPunishmentModal.addEventListener('hidden.bs.modal', function() {
            document.getElementById('punishmentForm').reset();
            const submitBtn = document.getElementById('submitPunishment');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-gavel me-2"></i>Issue Punishment';
            }
        });
    }


    const submitBtn = document.getElementById('submitPunishment');
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            issuePunishment();
        });
    }


    document.querySelectorAll('.punishment-template-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const template = this.getAttribute('data-template');
            document.getElementById('punishmentReason').value = template;
        });
    });


    localStorage.setItem('punishmentsViewMode', 'grid');
    setViewMode('grid');
});

/**
 * Load punishments with current filters
 */
function loadPunishments() {
    const punishmentsContent = document.getElementById('punishmentsContent');


    punishmentsContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading punishments...</p>
        </div>
    `;


    const type = document.getElementById('filterType').value;
    const status = document.getElementById('filterStatus').value;
    const player = document.getElementById('filterPlayer').value.trim();
    const staff = document.getElementById('filterStaff').value.trim();


    let queryParams = [];
    if (type !== 'all') queryParams.push(`type=${encodeURIComponent(type)}`);
    if (status !== 'all') queryParams.push(`status=${encodeURIComponent(status)}`);
    if (player) queryParams.push(`player=${encodeURIComponent(player)}`);
    if (staff) queryParams.push(`staff=${encodeURIComponent(staff)}`);

    const queryString = queryParams.length > 0 ? `?${queryParams.join('&')}` : '';


    fetch(`/adminapi/punishments.php/list${queryString}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'X-Discord-ID': window.AUTH_DATA?.user_id || '',
            'X-Discord-Username': window.AUTH_DATA?.username || '',
            'X-Discord-Role': window.AUTH_DATA?.role || '',
            'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            displayPunishments(data);
        })
        .catch(error => {
            punishmentsContent.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-circle text-danger"></i>
                    <h4>Error Loading Punishments</h4>
                    <p>There was a problem loading the punishments. Please try again later.</p>
                </div>
            `;
        });
}

/**
 * Display punishments in the current view mode
 * @param {Array} punishments - Array of punishment objects
 */
function displayPunishments(punishments) {
    const punishmentsContent = document.getElementById('punishmentsContent');


    localStorage.setItem('punishmentsViewMode', 'grid');


    if (!punishments || punishments.length === 0) {
        punishmentsContent.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-check-circle text-success"></i>
                <h4>No Punishments Found</h4>
                <p>No punishments match your current filters. Try adjusting your filters or adding a new punishment.</p>
            </div>
        `;
        return;
    }


    punishments.sort((a, b) => {
        const dateA = new Date(a.issued_at || 0);
        const dateB = new Date(b.issued_at || 0);
        return dateB - dateA;
    });


    displayPunishmentsGrid(punishments);
}

/**
 * Display punishments in grid view
 * @param {Array} punishments - Array of punishment objects
 */
function displayPunishmentsGrid(punishments) {
    const punishmentsContent = document.getElementById('punishmentsContent');

    let html = '<div class="row g-4 punishment-grid">';

    punishments.forEach(punishment => {

        const isActive = punishment.active === true && punishment.expired !== true;
        const type = punishment.punishment_type || 'unknown';


        let typeClass, typeIcon;
        switch (type) {
            case 'ban':
                typeClass = 'danger';
                typeIcon = 'fa-ban';
                break;
            case 'mute':
                typeClass = 'warning';
                typeIcon = 'fa-microphone-slash';
                break;
            case 'warning':
                typeClass = 'info';
                typeIcon = 'fa-exclamation-triangle';
                break;
            default:
                typeClass = 'secondary';
                typeIcon = 'fa-question-circle';
        }


        const issuedDate = formatDateET(punishment.issued_at);
        const expiresDate = punishment.expires_at ? formatDateET(punishment.expires_at) : 'Never';

        html += `
            <div class="col-md-6 col-lg-4">
                <div class="card punishment-card ${type}" data-id="${punishment.punishment_id}">
                    <div class="card-header d-flex justify-content-between align-items-center ${isActive ? 'bg-' + typeClass + ' text-white' : ''}">
                        <div class="d-flex align-items-center">
                            <div class="punishment-type-icon me-2">
                                <i class="fas ${typeIcon}"></i>
                            </div>
                            <div>
                                <span class="fw-bold">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                                <span class="ms-2 small">#${punishment.punishment_id}</span>
                                ${isActive ? '<span class="badge bg-light text-dark ms-2">Active</span>' :
                                           '<span class="badge bg-secondary ms-2">Expired</span>'}
                            </div>
                        </div>
                        <button class="btn btn-sm ${isActive ? 'btn-light' : 'btn-outline-secondary'}" onclick="viewPunishmentDetails('${punishment.punishment_id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="player-name mb-2 fw-bold">
                            <i class="fas fa-user me-2"></i>${punishment.player_name || 'Unknown Player'}
                        </div>
                        <div class="punishment-reason mb-3 p-3 bg-light rounded">
                            <div class="text-muted small mb-1">Reason:</div>
                            <div class="fst-italic">${punishment.reason || 'No reason provided'}</div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="small text-muted">
                                <div><i class="far fa-calendar-plus me-1"></i> ${issuedDate}</div>
                                ${punishment.expires_at ? `<div><i class="far fa-calendar-times me-1"></i> ${expiresDate}</div>` :
                                                       '<div><i class="fas fa-infinity me-1"></i> Permanent</div>'}
                            </div>
                            <div class="small text-muted">
                                <div>By: ${punishment.staff_name || 'Unknown Staff'}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    punishmentsContent.innerHTML = html;


    document.querySelectorAll('.punishment-card').forEach(card => {
        card.addEventListener('click', function(e) {

            if (!e.target.closest('button')) {
                viewPunishmentDetails(this.dataset.id);
            }
        });
    });
}

/**
 * Display punishments in list view
 * @param {Array} punishments - Array of punishment objects
 */
function displayPunishmentsList(punishments) {
    const punishmentsContent = document.getElementById('punishmentsContent');

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Player</th>
                        <th>Reason</th>
                        <th>Issued By</th>
                        <th>Issued Date</th>
                        <th>Expires</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    punishments.forEach(punishment => {

        const isActive = punishment.active === true && punishment.expired !== true;
        const type = punishment.punishment_type || 'unknown';


        let typeClass, typeIcon;
        switch (type) {
            case 'ban':
                typeClass = 'danger';
                typeIcon = 'fa-ban';
                break;
            case 'mute':
                typeClass = 'warning';
                typeIcon = 'fa-microphone-slash';
                break;
            case 'warning':
                typeClass = 'info';
                typeIcon = 'fa-exclamation-triangle';
                break;
            default:
                typeClass = 'secondary';
                typeIcon = 'fa-question-circle';
        }


        const issuedDate = formatDateET(punishment.issued_at);
        const expiresDate = punishment.expires_at ? formatDateET(punishment.expires_at) : 'Never';

        html += `
            <tr data-id="${punishment.punishment_id}" class="punishment-row ${isActive ? 'table-' + typeClass + '-light' : ''}">
                <td>
                    <span class="badge bg-${typeClass}">
                        <i class="fas ${typeIcon} me-1"></i>
                        ${type.charAt(0).toUpperCase() + type.slice(1)}
                    </span>
                </td>
                <td>${punishment.player_name || 'Unknown Player'}</td>
                <td class="text-truncate" style="max-width: 200px;">${punishment.reason || 'No reason provided'}</td>
                <td>${punishment.staff_name || 'Unknown Staff'}</td>
                <td>${issuedDate}</td>
                <td>${punishment.expires_at ? expiresDate : 'Never'}</td>
                <td>${isActive ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-secondary">Expired</span>'}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="viewPunishmentDetails('${punishment.punishment_id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="removePunishment('${punishment.punishment_id}', '${type}')">
                        <i class="fas fa-times"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    punishmentsContent.innerHTML = html;


    document.querySelectorAll('.punishment-row').forEach(row => {
        row.addEventListener('click', function(e) {

            if (!e.target.closest('button')) {
                viewPunishmentDetails(this.dataset.id);
            }
        });
    });
}

/**
 * Set the view mode (grid only)
 * @param {string} mode - 'grid' only
 */
function setViewMode(mode) {

    localStorage.setItem('punishmentsViewMode', 'grid');





    const punishments = document.querySelector('.punishment-grid, .table-responsive');
    if (punishments) {
        loadPunishments();
    }
}

/**
 * Reset all filters to default values
 */
function resetFilters() {
    document.getElementById('filterType').value = 'all';
    document.getElementById('filterStatus').value = 'all';
    document.getElementById('filterPlayer').value = '';
    document.getElementById('filterStaff').value = '';


    loadPunishments();
}

/**
 * View punishment details
 * @param {string} id - Punishment ID (either punishment_id or MongoDB _id)
 */
function viewPunishmentDetails(id) {
    const modal = new bootstrap.Modal(document.getElementById('punishmentDetailsModal'));
    const modalContent = document.getElementById('punishmentDetailsContent');


    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading punishment details...</p>
        </div>
    `;


    modal.show();


    fetch(`/adminapi/punishments/details.php/?id=${encodeURIComponent(id)}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'X-Discord-ID': window.AUTH_DATA?.user_id || '',
            'X-Discord-Username': window.AUTH_DATA?.username || '',
            'X-Discord-Role': window.AUTH_DATA?.role || '',
            'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(punishment => {
            displayPunishmentDetails(punishment);
        })
        .catch(error => {
            modalContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Error loading punishment details. Please try again later.
                </div>
            `;
        });
}

/**
 * Display punishment details in the modal
 * @param {Object} punishment - Punishment object
 */
function displayPunishmentDetails(punishment) {
    const modalContent = document.getElementById('punishmentDetailsContent');
    const removeBtn = document.getElementById('removePunishmentBtn');


    const isActive = punishment.active === true && punishment.expired !== true;
    const type = punishment.punishment_type || 'unknown';


    let typeClass, typeIcon;
    switch (type) {
        case 'ban':
            typeClass = 'danger';
            typeIcon = 'fa-ban';
            break;
        case 'mute':
            typeClass = 'warning';
            typeIcon = 'fa-microphone-slash';
            break;
        case 'warning':
            typeClass = 'info';
            typeIcon = 'fa-exclamation-triangle';
            break;
        default:
            typeClass = 'secondary';
            typeIcon = 'fa-question-circle';
    }


    const issuedDate = formatDateET(punishment.issued_at);
    const expiresDate = punishment.expires_at ? formatDateET(punishment.expires_at) : 'Never';
    const removedDate = punishment.removed_at ? formatDateET(punishment.removed_at) : null;


    document.getElementById('punishmentDetailsModalLabel').textContent =
        `${type.charAt(0).toUpperCase() + type.slice(1)} Details - ${punishment.player_name || 'Unknown Player'}`;


    let html = `
        <div class="punishment-details">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header bg-${typeClass} text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas ${typeIcon} me-2"></i>
                                ${type.charAt(0).toUpperCase() + type.slice(1)} Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>ID:</strong> ${punishment.punishment_id}
                            </div>
                            <div class="mb-3">
                                <strong>Status:</strong>
                                ${isActive ?
                                    '<span class="badge bg-success">Active</span>' :
                                    '<span class="badge bg-secondary">Expired</span>'}
                            </div>
                            <div class="mb-3">
                                <strong>Issued Date:</strong> ${issuedDate}
                            </div>
                            <div class="mb-3">
                                <strong>Expires:</strong> ${punishment.expires_at ? expiresDate : 'Never'}
                            </div>
                            ${removedDate ? `
                            <div class="mb-3">
                                <strong>Removed Date:</strong> ${removedDate}
                            </div>
                            <div class="mb-3">
                                <strong>Removed By:</strong> ${punishment.removed_by_name || 'Unknown'}
                            </div>
                            <div class="mb-3">
                                <strong>Removal Reason:</strong> ${punishment.removed_reason || 'No reason provided'}
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>
                                Player Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Player Name:</strong> ${punishment.player_name || 'Unknown Player'}
                            </div>
                            <div class="mb-3">
                                <strong>XUID:</strong> ${punishment.player_xuid || 'Unknown'}
                            </div>
                            <div class="mb-3">
                                <strong>Issued By:</strong> ${punishment.staff_name || 'Unknown Staff'}
                            </div>
                            <div class="mb-3">
                                <strong>Staff ID:</strong> ${punishment.staff_id || 'Unknown'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Violation Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Offense Type:</strong> ${punishment.offense_type ?
                                    formatOffenseType(punishment.offense_type) :
                                    '<em class="text-muted">Not specified</em>'}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Offense Count:</strong>
                                ${punishment.offense_count ?
                                    `<span class="badge bg-${punishment.offense_count > 1 ? 'warning' : 'info'}">
                                        #${punishment.offense_count}
                                    </span>` :
                                    '<em class="text-muted">Not specified</em>'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-camera me-2"></i>
                        Evidence
                    </h5>
                </div>
                <div class="card-body">
                    <div class="punishment-evidence p-3 rounded">
                        ${punishment.evidence ?
                            (punishment.evidence.includes('http') ?
                                `<a href="${punishment.evidence}" target="_blank" class="d-block mb-2">
                                    <i class="fas fa-external-link-alt me-1"></i> View Evidence
                                </a>
                                <div class="text-muted small">${punishment.evidence}</div>`
                                : punishment.evidence)
                            : '<em class="text-muted">No evidence provided</em>'}
                    </div>
                </div>
            </div>

            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard me-2"></i>
                        Staff Notes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="punishment-notes p-3 rounded">
                        ${punishment.staff_notes ? punishment.staff_notes : '<em class="text-muted">No staff notes</em>'}
                    </div>
                </div>
            </div>


        </div>
    `;

    modalContent.innerHTML = html;


    // Always show the remove button for admins
    removeBtn.style.display = 'block';
    removeBtn.onclick = function() {
        const punishmentType = type.endsWith('s') ? type : type + 's';
        removePunishment(punishment.punishment_id, punishmentType);
    };
}

/**
 * Issue a new punishment
 */
function issuePunishment() {
    const playerName = document.getElementById('playerName').value.trim();
    const typeElements = document.getElementsByName('punishmentType');
    let type;


    for (const element of typeElements) {
        if (element.checked) {
            type = element.value;
            break;
        }
    }

    const reason = document.getElementById('punishmentReason').value.trim();
    const duration = document.getElementById('punishmentDuration').value;


    if (!playerName) {
        Swal.fire({
            icon: 'warning',
            title: 'Missing Information',
            text: 'Please enter a player name'
        });
        return;
    }

    if (!reason) {
        Swal.fire({
            icon: 'warning',
            title: 'Missing Information',
            text: 'Please provide a reason for the punishment'
        });
        return;
    }


    const data = {
        player: playerName,
        reason: reason,
        duration: duration
    };


    const submitBtn = document.getElementById('submitPunishment');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Processing...';


    fetch(`/adminapi/punishments.php/${type}s/add`, {
        method: 'POST',
        credentials: 'include',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'X-Discord-ID': window.AUTH_DATA?.user_id || '',
            'X-Discord-Username': window.AUTH_DATA?.username || '',
            'X-Discord-Role': window.AUTH_DATA?.role || '',
            'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                throw new Error(data.error || `HTTP error! Status: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(data => {
        try {

            const modalElement = document.getElementById('addPunishmentModal');
            if (modalElement) {

                let modal = bootstrap.Modal.getInstance(modalElement);


                if (!modal) {
                    modal = new bootstrap.Modal(modalElement);
                }


                modal.hide();


                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }


                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }
        } catch (modalError) {

            if (typeof $ !== 'undefined') {
                $('#addPunishmentModal').modal('hide');
            }
        }


        Swal.fire({
            icon: 'success',
            title: 'Punishment Issued',
            text: data.message || 'Punishment has been issued successfully'
        });


        loadPunishments();
    })
    .catch(error => {

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: error.message || 'Failed to issue punishment'
        });
    })
    .finally(() => {

        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;


        document.getElementById('punishmentForm').reset();
    });
}

/**
 * Remove a punishment
 * @param {string} id - Punishment ID (either punishment_id or MongoDB _id)
 * @param {string} type - Punishment type (bans, mutes, warnings)
 */
function removePunishment(id, type) {

    const previouslyFocusedElement = document.activeElement;


    const modalElement = document.getElementById('punishmentDetailsModal');
    const detailsModal = bootstrap.Modal.getInstance(modalElement);

    if (detailsModal) {
        try {

            if (modalElement.hasAttribute('aria-hidden')) {
                modalElement.removeAttribute('aria-hidden');
            }


            if (document.activeElement && modalElement.contains(document.activeElement)) {
                document.activeElement.blur();
            }


            window.isClosingPunishmentModal = true;


            detailsModal.hide();


            const cleanupModal = () => {

                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }


                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            };


            cleanupModal();


            setTimeout(() => {

                cleanupModal();

                window.isClosingPunishmentModal = false;
                showRemovePunishmentConfirmation(id, type, previouslyFocusedElement);
            }, 300);
        } catch (error) {

            window.isClosingPunishmentModal = false;
            showRemovePunishmentConfirmation(id, type, previouslyFocusedElement);
        }
    } else {
        showRemovePunishmentConfirmation(id, type, previouslyFocusedElement);
    }
}

/**
 * Show confirmation dialog for removing a punishment
 * @param {string} id - Punishment ID
 * @param {string} type - Punishment type
 * @param {Element} [previouslyFocusedElement] - Element that had focus before this process started
 */
function showRemovePunishmentConfirmation(id, type, previouslyFocusedElement) {

    if (typeof Swal === 'undefined') {
        alert('Error: SweetAlert2 library is not loaded. Please refresh the page and try again.');
        return;
    }


    Swal.fire({
        title: 'Remove Punishment',
        text: 'Are you sure you want to remove this punishment?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, remove it',

        focusConfirm: true,

        willClose: () => {

            if (previouslyFocusedElement && typeof previouslyFocusedElement.focus === 'function') {
                setTimeout(() => {
                    try {
                        previouslyFocusedElement.focus();
                    } catch (e) {

                    }
                }, 10);
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {





            const punishmentType = type.endsWith('s') ? type : type + 's'; // Ensure we have the plural form



            fetch(`/adminapi/punishments.php/${punishmentType}/remove`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'X-Discord-ID': window.AUTH_DATA?.user_id || '',
                    'X-Discord-Username': window.AUTH_DATA?.username || '',
                    'X-Discord-Role': window.AUTH_DATA?.role || '',
                    'X-Discord-Avatar': window.AUTH_DATA?.avatar || '',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    punishment_id: id,
                    reason: 'Manually removed by staff'
                })
            })
            .then(response => {



                if (response.status === 200 || response.status === 204) {
                    return response.text().then(text => {
                        try {

                            return JSON.parse(text);
                        } catch (e) {


                            return {
                                success: true,
                                message: 'Punishment removed successfully',
                                synthetic: true // Flag to indicate this is not from the server
                            };
                        }
                    });
                }


                if (!response.ok) {

                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('text/html')) {

                        return response.text().then(html => {


                            if (html.toLowerCase().includes('success') ||
                                html.toLowerCase().includes('removed') ||
                                html.toLowerCase().includes('deleted')) {
                                return {
                                    success: true,
                                    message: 'Punishment removed successfully',
                                    synthetic: true
                                };
                            }
                            throw new Error(`Server error: ${response.status} ${response.statusText}`);
                        });
                    }


                    return response.text().then(text => {
                        try {
                            const data = JSON.parse(text);
                            throw new Error(data.error || data.message || `HTTP error! Status: ${response.status}`);
                        } catch (e) {
                            throw new Error(`Server error: ${response.status} ${response.statusText}`);
                        }
                    });
                }


                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {

                        return {
                            success: true,
                            message: 'Punishment removed successfully',
                            synthetic: true
                        };
                    }
                });
            })
            .then(data => {


                const detailsModal = bootstrap.Modal.getInstance(document.getElementById('punishmentDetailsModal'));
                if (detailsModal) {
                    detailsModal.hide();
                }


                Swal.fire({
                    icon: 'success',
                    title: 'Punishment Removed',
                    text: data.message || 'Punishment has been removed successfully',

                    footer: data.synthetic ?
                        '<small class="text-muted">Note: The server returned an unusual response, but the punishment was likely removed successfully.</small>' :
                        undefined
                });


                loadPunishments();
            })
            .catch(error => {


                const errorMessage = error.message || 'Failed to remove punishment';
                const containsHtml = errorMessage.includes('<') && errorMessage.includes('>');


                if (containsHtml) {

                    Swal.fire({
                        icon: 'warning',
                        title: 'Warning',
                        html: `
                            <p>The server returned an error, but the punishment may have been removed successfully.</p>
                            <p>Please check the punishment list to confirm.</p>
                        `,
                        footer: '<small class="text-muted">The page will reload to refresh the punishment list.</small>'
                    }).then(() => {

                        loadPunishments();
                    });
                } else {

                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: errorMessage
                    });
                }
            });
        }
    });
}
