/* Modern Theme - Additional styling for the new UI */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* Enhanced Card Styles */
.card-hover-effect {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-hover-effect:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Glass Morphism Effects */
.glass-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-dark {
    background: rgba(30, 42, 56, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--accent-color) 0%, #1a56cc 100%);
    color: white;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #27ae60 100%);
    color: white;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
    color: white;
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
    color: white;
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #2980b9 100%);
    color: white;
}

.bg-gradient-dark {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0f172a 100%);
    color: white;
}

/* Enhanced Shadows */
.shadow-hover {
    transition: box-shadow 0.2s ease;
}

.shadow-hover:hover {
    box-shadow: var(--shadow-lg);
}

/* Animated Elements */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Modern Navigation */
.modern-nav {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-md);
}

.modern-nav-item {
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    transition: all 0.2s ease;
}

.modern-nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.modern-nav-item.active {
    background-color: var(--accent-color);
    color: white;
}

/* Enhanced Buttons */
.btn-modern {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    z-index: -1;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.btn-modern:hover::before {
    transform: translateX(0);
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--accent-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    background-color: var(--accent-hover);
}

.fab i {
    font-size: 1.5rem;
}

/* Modern Search Bar */
.search-modern {
    position: relative;
}

.search-modern input {
    padding: 0.75rem 1rem 0.75rem 3rem;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    background-color: var(--bg-card);
    width: 100%;
    transition: all 0.3s ease;
}

.search-modern input:focus {
    box-shadow: 0 0 0 3px rgba(58, 134, 255, 0.25);
    border-color: var(--accent-color);
}

.search-modern i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

/* Punishment System Styles */
.punishment-card {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow-sm);
}

.punishment-card:hover {
    box-shadow: var(--shadow-md);
}

.punishment-card .card-header {
    padding: 1rem 1.25rem;
    font-weight: 600;
}

.punishment-card .card-body {
    padding: 1.25rem;
}

.punishment-card.ban {
    border-left: 4px solid var(--danger-color);
}

.punishment-card.mute {
    border-left: 4px solid var(--warning-color);
}

.punishment-card.warning {
    border-left: 4px solid var(--info-color);
}

.punishment-meta {
    display: flex;
    justify-content: space-between;
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: 1rem;
}

.punishment-reason {
    font-style: italic;
    color: var(--text-secondary);
    padding: 0.75rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius-sm);
    margin-bottom: 1rem;
}

/* Player Notes Styles */
.note-card {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
}

.note-card:hover {
    box-shadow: var(--shadow-md);
}

.note-card .card-header {
    padding: 0.75rem 1rem;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.note-card .card-body {
    padding: 1rem;
}

.note-card.important {
    border-left: 4px solid var(--danger-color);
}

.note-meta {
    display: flex;
    justify-content: space-between;
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-top: 0.5rem;
}

.note-content {
    color: var(--text-dark);
    line-height: 1.5;
}

/* Player Info Styles */
.player-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.player-avatar {
    width: 64px;
    height: 64px;
    border-radius: 12px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-right: 1rem;
}

.player-info h2 {
    margin-bottom: 0.25rem;
}

.player-badges {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.player-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.stat-card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
}

.stat-card .stat-title {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.stat-card .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
}

/* Dark Mode Toggle - Hidden (only dark mode supported) */
.dark-mode-toggle {
    display: none !important;
}
