/* Punishment Styles */

.punishment-card {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    overflow: hidden;
}

.punishment-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.punishment-card .card-header {
    padding: 1rem 1.25rem;
}

.punishment-type-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.punishment-reason {
    border-left: 3px solid var(--border-color);
}

.staff-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

.punishment-dates {
    text-align: right;
    font-size: 0.875rem;
}

/* Punishment types */
.punishment-card.border-danger .punishment-reason {
    border-left-color: var(--danger-color);
}

.punishment-card.border-warning .punishment-reason {
    border-left-color: var(--warning-color);
}

.punishment-card.border-info .punishment-reason {
    border-left-color: var(--info-color);
}

/* Empty state */
.punishment-empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    color: var(--text-muted);
    background-color: var(--bg-card);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    margin-top: 1rem;
}

.punishment-empty-state i {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-muted);
    opacity: 0.3;
}

.punishment-empty-state h4 {
    margin-bottom: 0.75rem;
    color: var(--text-dark);
    font-weight: 600;
}

.punishment-empty-state p {
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Punishment Modal */
.punishment-modal .modal-header {
    background-color: var(--primary-color);
    color: white;
}

.punishment-modal .modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1rem;
}

.punishment-type-selector {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.punishment-type-selector .type-option {
    flex: 1;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.punishment-type-selector .type-option:hover {
    background-color: var(--bg-hover);
}

.punishment-type-selector .type-option.active {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.1);
}

.punishment-type-selector .type-option.ban {
    border-left: 3px solid var(--danger-color);
}

.punishment-type-selector .type-option.mute {
    border-left: 3px solid var(--warning-color);
}

.punishment-type-selector .type-option.kick {
    border-left: 3px solid var(--info-color);
}

.punishment-type-selector .type-option i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-muted);
}

.punishment-type-selector .type-option.active i {
    color: var(--primary-color);
}

.punishment-type-selector .type-option.ban.active i {
    color: var(--danger-color);
}

.punishment-type-selector .type-option.mute.active i {
    color: var(--warning-color);
}

.punishment-type-selector .type-option.kick.active i {
    color: var(--info-color);
}
