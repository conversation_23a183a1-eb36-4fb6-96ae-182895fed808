===============================================
MongoDB\\Model\\CollectionInfo::getCappedSize()
===============================================

.. deprecated:: 1.9

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\CollectionInfo::getCappedSize()

   Return the size limit for the capped collection in bytes. This correlates
   with the ``size`` option for
   :phpmethod:`MongoDB\\Database::createCollection()`.

   .. code-block:: php

      function getCappedSize(): integer|null

Return Values
-------------

The size limit for the capped collection in bytes. If the collection is not
capped, ``null`` will be returned.

This method is deprecated in favor of using
:phpmethod:`MongoDB\\Model\\CollectionInfo::getOptions()` and accessing the
``size`` key.

Examples
--------

.. code-block:: php

   <?php

   $info = new CollectionInfo([
       'name' => 'foo',
       'options' => [
           'capped' => true,
           'size' => 1048576,
       ]
   ]);

   var_dump($info->getCappedSize());

The output would then resemble::

   int(1048576)

See Also
--------

- :phpmethod:`MongoDB\\Model\\CollectionInfo::getCappedMax()`
- :phpmethod:`MongoDB\\Model\\CollectionInfo::isCapped()`
- :phpmethod:`MongoDB\\Database::createCollection()`
- :manual:`Capped Collections </core/capped-collections>` in the MongoDB manual
- :manual:`listCollections </reference/command/listCollections>` command
  reference in the MongoDB manual
