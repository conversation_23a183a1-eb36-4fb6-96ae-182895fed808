/* Mobile fixes for Admin Panel */

/* General mobile improvements */
@media (max-width: 767.98px) {
    /* Fix for tables on mobile */
    .table-responsive {
        border: 0;
        width: 100%;
        margin-bottom: 15px;
        overflow-y: hidden;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }

    /* Adjust card layouts for mobile */
    .card-header {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .card-header > div:not(:first-child) {
        margin-top: 1rem;
        width: 100%;
    }

    .card-header .btn {
        margin-bottom: 0.5rem;
    }

    /* Fix button groups on mobile */
    .btn-group {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
    }

    .btn-group .btn {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Adjust grid layouts */
    .punishment-list {
        grid-template-columns: 1fr !important;
    }

    /* Fix for DataTables on mobile */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        text-align: left !important;
        float: none !important;
        width: 100% !important;
        margin-bottom: 0.5rem;
    }

    /* Fix for filters on mobile */
    .audit-log-filters .row > div {
        margin-bottom: 1rem;
    }

    /* Fix for buttons on mobile */
    .btn {
        margin-bottom: 0.5rem;
    }

    /* Fix for modals on mobile */
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-body {
        padding: 1rem;
    }

    /* Fix for dropdown menus on mobile */
    .dropdown-menu {
        width: 100% !important;
        max-width: 100% !important;
    }
}

/* Specific fixes for Audit Log page */
@media (max-width: 767.98px) {
    /* Card-based layout fixes */
    .audit-card-container {
        grid-template-columns: 1fr !important;
    }

    .audit-card {
        margin-bottom: 1rem;
    }

    .audit-card .card-header {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .audit-details {
        max-width: 100%;
        max-height: 120px;
        overflow-y: auto;
    }

    /* Filter adjustments */
    .audit-log-filters .row > div {
        margin-bottom: 0.75rem;
    }
}

/* Specific fixes for Notes in Player Info page */
@media (max-width: 767.98px) {
    /* Fix note card layout on mobile */
    .note-card .card-header {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .note-staff-info {
        flex-wrap: wrap;
    }

    .note-staff-info > div {
        margin-bottom: 0.5rem;
    }

    .note-staff-avatar {
        width: 32px !important;
        height: 32px !important;
        min-width: 32px !important;
        min-height: 32px !important;
        margin-right: 0.5rem !important;
        overflow: hidden !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        position: relative !important;
        background-color: var(--primary-color) !important;
        color: white !important;
        font-weight: bold !important;
    }

    .note-staff-avatar .discord-avatar {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        z-index: 1 !important;
    }

    .note-date {
        width: 100%;
        justify-content: flex-start;
        margin-left: 0 !important;
        margin-top: 0.5rem;
    }
}

/* Specific fixes for Player Search page */
@media (max-width: 767.98px) {
    /* Fix search input container */
    .search-input-container .input-group {
        flex-direction: column;
    }

    .search-input-container .form-select {
        width: 100% !important;
        border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
        margin-bottom: 0;
        height: 48px;
    }

    .search-input-container .position-relative {
        width: 100%;
    }

    .search-input-container .form-control {
        border-radius: 0;
        border-left: 1px solid var(--border-color);
        border-right: 1px solid var(--border-color);
        height: 48px;
        font-size: 16px; /* Prevents iOS zoom on focus */
    }

    .search-input-container .btn-primary {
        border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
        width: 100%;
        height: 48px;
    }

    /* Fix search suggestions */
    #searchSuggestions {
        width: 100% !important;
        max-height: 250px;
        overflow-y: auto;
    }

    /* Improve search history display */
    .search-history {
        margin-top: 1.5rem;
    }

    .history-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .history-tag {
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
}

/* Specific fixes for Punishments page */
@media (max-width: 767.98px) {
    /* Fix for punishment page layout */
    .admin-content {
        width: 100% !important;
        margin-left: 0 !important;
        padding: 1rem !important;
        overflow-x: hidden !important;
    }

    /* Fix for punishment cards */
    .punishment-card {
        margin-bottom: 1rem;
        width: 100% !important;
    }

    .punishment-list {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
    }

    .punishment-card .card-header {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .punishment-meta {
        flex-direction: column;
        align-items: flex-start;
    }

    .punishment-meta > div {
        margin-bottom: 0.5rem;
    }

    /* Fix for punishment filters */
    .filters-container .row > div {
        margin-bottom: 0.75rem;
    }

    /* Fix for punishment buttons */
    .punishment-actions .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }

    /* Fix for punishment details modal */
    .punishment-details .row {
        flex-direction: column;
    }

    .punishment-details .col-md-6 {
        width: 100%;
        margin-bottom: 1rem;
    }
}

/* Staff Management Card Layout */
.staff-card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.75rem;
    padding: 0.5rem;
}

/* Fix for staff management buttons */
.staff-card .btn {
    position: relative;
    z-index: 1; /* Lower z-index than sidebar */
}

/* Fix for staff management page layout */
@media (max-width: 767.98px) {
    .staff-card-container {
        grid-template-columns: 1fr;
    }

    .staff-card {
        margin-bottom: 1rem;
    }

    /* Fix for staff management filters */
    #staffSearch, #roleFilter, #statusFilter {
        margin-bottom: 0.75rem;
    }
}

.staff-card {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    background-color: var(--bg-card);
    border: none;
    position: relative;
}

.staff-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
}

.staff-card .card-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.staff-card .staff-avatar {
    width: 70px;
    height: 70px;
    margin-right: 1.25rem;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.staff-card .card-body {
    padding: 1.5rem;
}

.staff-card .staff-info {
    flex: 1;
}

.staff-card .staff-name {
    font-size: 1.35rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    color: var(--text-dark);
}

.staff-card .staff-role {
    margin-bottom: 0;
}

.staff-card .role-badge {
    padding: 0.4rem 0.75rem;
    border-radius: 50rem;
    font-size: 0.8rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.staff-card .badge {
    padding: 0.4rem 0.75rem;
    border-radius: 50rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.staff-card .text-muted {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary) !important;
    margin-bottom: 0.25rem;
}

.staff-card .text-monospace {
    font-family: 'Roboto Mono', monospace;
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    display: inline-block;
}

.staff-card .staff-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.staff-card .btn {
    flex: 1;
    margin: 0 0.35rem;
    padding: 0.5rem 0.75rem;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.staff-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.staff-card .btn:first-child {
    margin-left: 0;
}

.staff-card .btn:last-child {
    margin-right: 0;
}

/* Status indicator */
.staff-card .badge.bg-success {
    background-color: #2ecc71 !important;
    color: white;
}

.staff-card .badge.bg-secondary {
    background-color: rgba(41, 128, 185, 0.15) !important;
    color: #2980b9 !important;
}

/* Dark mode fixes */
body.dark-mode .staff-card .card-header {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .staff-card {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
}

body.dark-mode .staff-card .text-monospace {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .staff-card .staff-name {
    color: var(--text-light);
}

@media (max-width: 767.98px) {
    .staff-card-container {
        grid-template-columns: 1fr;
    }
}
