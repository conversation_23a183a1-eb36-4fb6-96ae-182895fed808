<?php
/**
 * Initialize Staff Activity Log
 * This script creates the staff_activity_log collection and adds sample data
 */


require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/db_access.php';


header('Content-Type: text/plain');

echo "Starting staff activity log initialization...\n\n";

try {

    $connection = get_db_connection();
    $db = $connection['db'];

    if (!$db) {
        throw new Exception("Database connection failed");
    }

    echo "Connected to database successfully.\n";


    $collections = [];
    foreach ($db->listCollections() as $collectionInfo) {
        $collections[] = $collectionInfo->getName();
    }

    echo "Existing collections: " . implode(', ', $collections) . "\n\n";


    $collection_exists = in_array('staff_activity_log', $collections);
    echo "Staff activity log collection exists: " . ($collection_exists ? "YES" : "NO") . "\n";


    if (!$collection_exists) {
        echo "Creating staff_activity_log collection...\n";
        $db->createCollection('staff_activity_log');
        echo "Staff activity log collection created successfully.\n\n";


        echo "Creating indexes for staff_activity_log collection...\n";
        $db->staff_activity_log->createIndex(['timestamp' => -1]);
        $db->staff_activity_log->createIndex(['user_id' => 1]);
        $db->staff_activity_log->createIndex(['username' => 1]);
        echo "Indexes created successfully.\n\n";
    }


    $count = $db->staff_activity_log->countDocuments();
    echo "Found {$count} existing staff activity records.\n\n";


    if ($count == 0) {
        echo "Adding sample staff activity records...\n";


        $staff_members = [];
        try {
            $staff_cursor = $db->staff_activity->find();
            foreach ($staff_cursor as $staff) {
                $staff_members[] = [
                    'user_id' => $staff->user_id ?? '',
                    'username' => $staff->username ?? 'Unknown Staff'
                ];
            }
        } catch (Exception $e) {
            echo "Error getting staff members: " . $e->getMessage() . "\n";

            $staff_members = [
                ['user_id' => '1157062586819416085', 'username' => 'groundzeromike'],
                ['user_id' => '987654321098765432', 'username' => 'mod_user']
            ];
        }

        echo "Found " . count($staff_members) . " staff members.\n";


        if (empty($staff_members)) {
            $staff_members = [
                ['user_id' => '1157062586819416085', 'username' => 'groundzeromike'],
                ['user_id' => '987654321098765432', 'username' => 'mod_user']
            ];
        }


        $activity_types = [
            ['type' => 'report', 'actions' => ['Accepted', 'Denied', 'Reviewed']],
            ['type' => 'appeal', 'actions' => ['Approved', 'Denied', 'Reviewed']],
            ['type' => 'ban', 'actions' => ['Added', 'Removed', 'Modified']],
            ['type' => 'mute', 'actions' => ['Added', 'Removed', 'Modified']],
            ['type' => 'warning', 'actions' => ['Added', 'Removed']],
            ['type' => 'announcement', 'actions' => ['Sent global', 'Sent targeted']]
        ];


        $player_names = [
            'PlayerOne', 'PlayerTwo', 'CoolGamer123', 'MinecraftPro', 'DiamondMiner',
            'EmeraldHunter', 'NetherExplorer', 'EndermanSlayer', 'RedstoneWizard', 'VillagerTrader'
        ];


        $sample_activities = [];

        for ($i = 0; $i < 20; $i++) {

            $staff = $staff_members[array_rand($staff_members)];


            $activity_type = $activity_types[array_rand($activity_types)];
            $type = $activity_type['type'];
            $action = $activity_type['actions'][array_rand($activity_type['actions'])];


            $player = $player_names[array_rand($player_names)];


            $timestamp = new MongoDB\BSON\UTCDateTime((time() - rand(0, 7 * 24 * 60 * 60)) * 1000);


            $activity = [
                'user_id' => $staff['user_id'],
                'username' => $staff['username'],
                'action' => $action,
                'type' => $type,
                'target' => $player,
                'details' => "Sample activity for demonstration",
                'timestamp' => $timestamp,
                'staff' => $staff['username']  // Duplicate for compatibility with dashboard.js
            ];

            $sample_activities[] = $activity;
        }


        usort($sample_activities, function($a, $b) {
            return $b['timestamp']->toDateTime()->getTimestamp() - $a['timestamp']->toDateTime()->getTimestamp();
        });


        foreach ($sample_activities as $activity) {
            try {
                $result = $db->staff_activity_log->insertOne($activity);
                echo "Inserted activity record with ID: " . $result->getInsertedId() . "\n";
            } catch (Exception $e) {
                echo "Error inserting activity record: " . $e->getMessage() . "\n";
            }
        }

        echo "Sample staff activity records added successfully.\n\n";
    } else {
        echo "Staff activity log already has data. Skipping sample data creation.\n\n";
    }

    echo "Staff activity log initialization completed successfully.\n";

} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
