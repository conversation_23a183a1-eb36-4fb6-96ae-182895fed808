source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.13
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: typeMap
post: |
  This will be used for the returned command result document.
---
source:
  file: apiargs-common-option.yaml
  ref: session
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: writeConcern
---
arg_name: option
name: dropTarget
type: boolean
description: |
  If ``true``, MongoDB will drop the target before renaming the collection. The
  default value is ``false``.
interface: phpmethod
operation: ~
optional: true
...
