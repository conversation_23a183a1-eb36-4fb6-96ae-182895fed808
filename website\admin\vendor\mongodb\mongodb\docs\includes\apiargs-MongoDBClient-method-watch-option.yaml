---
source:
  file: apiargs-method-watch-option.yaml
  ref: batchSize
---
source:
  file: apiargs-common-option.yaml
  ref: collation
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  The comment can be any valid BSON type for server versions 4.4 and above.
  Earlier server versions only support string values.

  .. versionadded:: 1.13
---
source:
  file: apiargs-method-watch-option.yaml
  ref: fullDocument
---
source:
  file: apiargs-method-watch-option.yaml
  ref: fullDocumentBeforeChange
post: |
  .. versionadded: 1.13
---
source:
  file: apiargs-method-watch-option.yaml
  ref: maxAwaitTimeMS
---
source:
  file: apiargs-MongoDBClient-common-option.yaml
  ref: readConcern
---
source:
  file: apiargs-MongoDBClient-common-option.yaml
  ref: readPreference
post: |
  This is used for both the initial change stream aggregation and for
  server selection during an automatic resume.
---
source:
  file: apiargs-method-watch-option.yaml
  ref: resumeAfter
---
source:
  file: apiargs-common-option.yaml
  ref: session
---
source:
  file: apiargs-method-watch-option.yaml
  ref: showExpandedEvents
post: |
  .. versionadded:: 1.13
---
source:
  file: apiargs-method-watch-option.yaml
  ref: startAfter
post: |
  .. versionadded: 1.5
---
source:
  file: apiargs-method-watch-option.yaml
  ref: startAtOperationTime
---
source:
  file: apiargs-MongoDBClient-common-option.yaml
  ref: typeMap
...
