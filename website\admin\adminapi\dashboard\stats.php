<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/auth.php';



if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


if (!is_authenticated()) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}


if (!has_role(ROLE_TRAINEE)) {
    error_log("Dashboard Stats API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    http_response_code(403);
    echo json_encode(['error' => 'Permission denied. You do not have the required role to access dashboard stats.']);
    exit;
}


refresh_session();

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {

        error_log("Dashboard API: Getting database connection");
        $db_connection = get_db_connection();

        if (!$db_connection || !$db_connection['db']) {
            throw new Exception("Failed to connect to database");
        }

        error_log("Dashboard API: Got database connection, fetching stats");
        $stats = get_dashboard_stats();


        error_log("Dashboard API: Active bans count: " . ($stats['activeBans'] ?? 'undefined'));
        error_log("Dashboard API: Active mutes count: " . ($stats['activeMutes'] ?? 'undefined'));
        error_log("Dashboard API: Stats data: " . json_encode($stats));

        if (empty($stats)) {
            echo json_encode([
                'pendingReports' => 0,
                'highPriorityReports' => 0,
                'pendingAppeals' => 0,
                'activeBans' => 0,
                'activeMutes' => 0,
                'onlineStaff' => []
            ]);
        } else {
            echo json_encode($stats);
        }
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    error_log("Dashboard API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error',
        'message' => $e->getMessage(),
        'pendingReports' => 0,
        'highPriorityReports' => 0,
        'pendingAppeals' => 0,
        'activeBans' => 0,
        'activeMutes' => 0,
        'onlineStaff' => []
    ]);
}
?>