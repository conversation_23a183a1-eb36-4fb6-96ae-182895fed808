/* Dark Mode Fixes for specific components */

/* Fix for dropdown items in dark mode */
body.dark-mode .dropdown-menu {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
    color: var(--text-light);
}

body.dark-mode .dropdown-item,
body.dark-mode #punishmentModal .dropdown-item,
body.dark-mode .punishment-modal .dropdown-item,
body.dark-mode .reason-templates .dropdown-menu div,
body.dark-mode #punishmentModal .reason-templates .dropdown-menu div {
    color: var(--text-light) !important;
}

body.dark-mode .dropdown-item:hover,
body.dark-mode .dropdown-item:focus,
body.dark-mode #punishmentModal .dropdown-item:hover,
body.dark-mode #punishmentModal .dropdown-item:focus,
body.dark-mode .punishment-modal .dropdown-item:hover,
body.dark-mode .punishment-modal .dropdown-item:focus,
body.dark-mode .reason-templates .dropdown-menu div:hover,
body.dark-mode #punishmentModal .reason-templates .dropdown-menu div:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light) !important;
}

/* Fix for punishment reason templates in dark mode */
body.dark-mode .reason-template-item,
body.dark-mode .dropdown-menu .reason-template-item,
body.dark-mode #punishmentModal .dropdown-menu .reason-template-item,
body.dark-mode .punishment-modal .dropdown-menu .reason-template-item,
body.dark-mode #punishmentModal .reason-templates .dropdown-menu div,
body.dark-mode #punishmentModal .dropdown-menu div.reason-template-item {
    color: var(--text-light) !important;
    background-color: var(--secondary-color);
    border-radius: var(--border-radius-sm);
    margin-bottom: 2px;
    padding: 8px 12px;
}

body.dark-mode .reason-template-item:hover,
body.dark-mode .dropdown-menu .reason-template-item:hover,
body.dark-mode #punishmentModal .dropdown-menu .reason-template-item:hover,
body.dark-mode .punishment-modal .dropdown-menu .reason-template-item:hover,
body.dark-mode #punishmentModal .reason-templates .dropdown-menu div:hover,
body.dark-mode #punishmentModal .dropdown-menu div.reason-template-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Ensure dropdown button for templates is visible */
body.dark-mode .reason-templates .btn-outline-secondary,
body.dark-mode #punishmentModal .reason-templates .btn-outline-secondary,
body.dark-mode .punishment-modal .reason-templates .btn-outline-secondary {
    color: var(--text-light);
    border-color: var(--border-color-dark);
    background-color: rgba(255, 255, 255, 0.1);
}

/* Fix for punishment modal dropdown menu */
body.dark-mode #punishmentModal .dropdown-menu,
body.dark-mode .punishment-modal .dropdown-menu {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
    color: var(--text-light);
}

/* Fix for punishment templates dropdown */
body.dark-mode .dropdown-menu.punishment-templates {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
    padding: 8px;
    max-height: 200px;
    overflow-y: auto;
    position: absolute;
    z-index: 1070;
    bottom: 100%;
    top: auto;
    margin-bottom: 5px;
}

body.dark-mode .punishment-template-option {
    color: var(--text-light);
    background-color: var(--primary-color);
    border-radius: var(--border-radius-sm);
    padding: 8px 12px;
    margin-bottom: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

body.dark-mode .punishment-template-option:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Fix for form backgrounds in dark mode */
body.dark-mode .bg-light {
    background-color: var(--primary-color) !important;
    color: var(--text-light);
}

body.dark-mode .punishment-reason {
    background-color: rgba(255, 255, 255, 0.05) !important;
    color: var(--text-light);
}

/* Fix for modal content in dark mode */
body.dark-mode .modal-content {
    background-color: var(--secondary-color);
    color: var(--text-light);
}

body.dark-mode .modal-header,
body.dark-mode .modal-footer {
    border-color: var(--border-color-dark);
}

/* Fix for form controls in dark mode */
body.dark-mode .form-control,
body.dark-mode .form-select {
    background-color: var(--primary-color);
    border-color: var(--border-color-dark);
    color: var(--text-light);
}

body.dark-mode .form-control:focus,
body.dark-mode .form-select:focus {
    border-color: var(--accent-color);
    background-color: var(--primary-color);
}

/* Enhanced dark dropdown styling for consistency across all admin pages */
.form-select {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

.form-select:focus {
    background-color: #2d3748 !important;
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    color: #e2e8f0 !important;
}

.form-select option {
    background-color: #2d3748 !important;
    color: #e2e8f0 !important;
}

/* Specific styling for appeal info UI dropdowns */
body.dark-mode #appealDetails .form-select,
body.dark-mode .appeal-details .form-select,
body.dark-mode .report-modal .form-select {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

body.dark-mode #appealDetails .form-select:focus,
body.dark-mode .appeal-details .form-select:focus,
body.dark-mode .report-modal .form-select:focus {
    background-color: #2d3748 !important;
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    color: #e2e8f0 !important;
}

body.dark-mode #appealDetails .form-select option,
body.dark-mode .appeal-details .form-select option,
body.dark-mode .report-modal .form-select option {
    background-color: #2d3748 !important;
    color: #e2e8f0 !important;
}

/* Fix for form text in dark mode */
body.dark-mode .form-text {
    color: var(--text-muted);
}

/* Fix for form check in dark mode */
body.dark-mode .form-check-input {
    background-color: var(--primary-color);
    border-color: var(--border-color-dark);
}

body.dark-mode .form-check-input:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* Fix for table in dark mode */
body.dark-mode .table {
    color: var(--text-light);
}

body.dark-mode .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Fix for card in dark mode */
body.dark-mode .card {
    background-color: var(--secondary-color);
    border-color: var(--border-color-dark);
}

body.dark-mode .card-header {
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom-color: var(--border-color-dark);
}

body.dark-mode .card-footer {
    background-color: rgba(0, 0, 0, 0.2);
    border-top-color: var(--border-color-dark);
}

/* Fix for alerts in dark mode */
body.dark-mode .alert-light {
    background-color: var(--primary-color);
    color: var(--text-light);
    border-color: var(--border-color-dark);
}

/* Fix for badges in dark mode */
body.dark-mode .badge.bg-light {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: var(--text-light) !important;
}

/* Fix for text colors in dark mode */
body.dark-mode .text-dark {
    color: var(--text-light) !important;
}

body.dark-mode .text-muted {
    color: var(--text-muted) !important;
}

/* Fix for buttons in dark mode */
body.dark-mode .btn-light {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

body.dark-mode .btn-light:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--text-light);
}

body.dark-mode .btn-outline-secondary {
    color: var(--text-muted);
    border-color: var(--border-color-dark);
}

body.dark-mode .btn-outline-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

/* Fix for SweetAlert2 in dark mode */
body.dark-mode .swal2-popup {
    background-color: var(--secondary-color);
    color: var(--text-light);
}

body.dark-mode .swal2-title {
    color: var(--text-light);
}

body.dark-mode .swal2-content {
    color: var(--text-light);
}

body.dark-mode .swal2-html-container {
    color: var(--text-light);
}
