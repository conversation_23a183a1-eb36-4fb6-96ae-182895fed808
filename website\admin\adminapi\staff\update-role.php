<?php
/**
 * Update Staff Role API Endpoint
 * Allows admins to update a staff member's role
 */


require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/security.php';


header('Content-Type: application/json');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


error_log("Update Staff Role API Request - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Update Staff Role API Request - Session ID: " . session_id());
error_log("Update Staff Role API Request - User ID: " . ($_SESSION['discord_user_id'] ?? 'Not set'));
error_log("Update Staff Role API Request - User Role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));


if (!is_authenticated()) {
    error_log("Update Staff Role API - Authentication failed");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}


if (!has_role(ROLE_ADMIN)) {
    error_log("Update Staff Role API - Authorization failed - User role: " . ($_SESSION['discord_user_role'] ?? 'Unknown'));
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access - Admin role required']);
    exit;
}


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {

        $json = file_get_contents('php://input');
        $data = json_decode($json, true);


        if (empty($data['user_id']) || empty($data['role'])) {
            error_log("Update Staff Role API - Missing required fields");
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            exit;
        }


        $valid_roles = [ROLE_ADMIN, ROLE_MODERATOR, ROLE_TRAINEE, ROLE_SUPERVISOR, ROLE_DEVELOPER, ROLE_OWNER, 'UNAUTHORIZED'];
        if (!in_array($data['role'], $valid_roles)) {
            error_log("Update Staff Role API - Invalid role: " . $data['role']);
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid role']);
            exit;
        }


        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            error_log("Update Staff Role API - Database connection failed");
            throw new Exception("Failed to connect to database");
        }


        $result = $db->staff_activity->updateOne(
            ['user_id' => $data['user_id']],
            ['$set' => ['role' => $data['role']]]
        );

        if ($result->getModifiedCount() === 0 && $result->getMatchedCount() === 0) {
            error_log("Update Staff Role API - Staff member not found: " . $data['user_id']);
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Staff member not found']);
            exit;
        }


        $staff_member = $db->staff_activity->findOne(['user_id' => $data['user_id']]);
        $staff_username = $staff_member['username'] ?? 'Unknown';


        $db->staff_activity_log->insertOne([
            'user_id' => $_SESSION['discord_user_id'],
            'username' => $_SESSION['discord_username'],
            'action' => "Updated role for $staff_username",
            'type' => 'role_update',
            'target' => $staff_username,
            'details' => "Changed role to " . $data['role'],
            'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
            'description' => "Updated staff role"
        ]);


        echo json_encode([
            'success' => true,
            'message' => "Successfully updated role for $staff_username to " . $data['role']
        ]);

    } catch (Exception $e) {

        error_log("Update Staff Role API Error: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());


        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Server error: ' . $e->getMessage()
        ]);
    }
} else {

    error_log("Update Staff Role API - Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
