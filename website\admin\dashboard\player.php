<?php

require_once '../includes/auth.php';
require_once __DIR__ . '/../includes/theme-handler.php'; 
require_once '../includes/config.php';


require_auth(ROLE_TRAINEE);


$userData = getUserData();
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo get_dark_mode_class(); ?>">
<head>
    <?php output_dark_mode_init(); ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generate_csrf_token(); ?>">
    <title>Player Search - Massacre MC Admin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <h3>Massacre MC</h3>
                <p class="mb-0">Admin Panel</p>
            </div>

            <ul class="list-unstyled components">
                <li>
                    <a href="dashboard">
                        <i class="fas fa-home"></i>
                        Dashboard
                    </a>
                </li>
                <li class="active">
                    <a href="player">
                        <i class="fas fa-users"></i>
                        Players
                    </a>
                </li>
                <li>
                    <a href="factions">
                        <i class="fas fa-flag"></i>
                        Factions
                    </a>
                </li>
                <li>
                    <a href="punishments">
                        <i class="fas fa-gavel"></i>
                        Punishments
                    </a>
                </li>
                <li>
                    <a href="reports">
                        <i class="fas fa-exclamation-triangle"></i>
                        Reports
                    </a>
                </li>
                <li>
                    <a href="appeals">
                        <i class="fas fa-balance-scale"></i>
                        Appeals
                    </a>
                </li>
                <li>
                    <a href="settings">
                        <i class="fas fa-cog"></i>
                        Settings
                    </a>
                </li>
                <li>
                    <a href="#" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid">
                    <button type="button" id="sidebarToggle" class="btn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="ms-auto d-flex align-items-center">
                        <div class="me-3">
                            <span class="text-muted">Welcome,</span>
                            <span class="fw-bold"><?php echo htmlspecialchars($userData['username']); ?></span>
                        </div>
                        <div class="dropdown">
                            <button class="btn dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="<?php echo htmlspecialchars($userData['avatar']); ?>" alt="User Avatar" class="rounded-circle" width="32" height="32">
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile"><i class="fas fa-user me-2"></i> Profile</a></li>
                                <li><a class="dropdown-item" href="settings"><i class="fas fa-cog me-2"></i> Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h2 class="mb-4">Player Search</h2>

                        <!-- Search Form -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="search-container">
                                    <div class="search-input-container">
                                        <div class="input-group">
                                            <select class="form-select" id="searchType">
                                                <option value="player" selected>Player</option>
                                                <option value="faction">Faction</option>
                                            </select>
                                            <div class="position-relative flex-grow-1">
                                                <div class="search-modern w-100">
                                                    <i class="fas fa-search"></i>
                                                    <input type="text" class="form-control" id="searchInput"
                                                        placeholder="Search for players or factions..."
                                                        autocomplete="off"
                                                        title="Type a name and press Enter to search">
                                                </div>
                                            </div>
                                            <button class="btn btn-primary" type="button" id="searchButton">
                                                <i class="fas fa-search"></i> Search
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Search History -->
                                <div class="search-history mt-3">
                                    <h6 class="text-muted mb-2">Recent Searches</h6>
                                    <div id="historyTags" class="history-tags">
                                        <!-- Will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search Results -->
                        <div id="playerData">
                            <div class="empty-state">
                                <i class="fas fa-search"></i>
                                <h4>Search for a Player or Faction</h4>
                                <p>Enter a player name or faction name in the search box above to view their information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="loading-spinner">
        <div class="spinner-container">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="spinner-text">Loading data...</div>
        </div>
    </div>

    <!-- Punishment Modal -->
    <div class="modal fade" id="punishmentModal" tabindex="-1" aria-labelledby="punishmentModalLabel">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="punishmentModalLabel">Add Punishment</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="punishmentForm">
                        <input type="hidden" id="punishmentPlayerXuid" name="xuid">
                        <input type="hidden" id="punishmentPlayerName" name="username">

                        <div class="mb-3">
                            <label for="punishmentType" class="form-label">Punishment Type</label>
                            <select class="form-select" id="punishmentType" name="type" required>
                                <option value="ban">Ban</option>
                                <option value="mute">Mute</option>
                                <option value="kick">Kick</option>
                                <option value="warning">Warning</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="punishmentReason" class="form-label">Reason</label>
                            <textarea class="form-control" id="punishmentReason" name="reason" rows="3" required></textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="punishmentDuration" class="form-label">Duration</label>
                                <input type="number" class="form-control" id="punishmentDuration" name="duration" min="1" value="1">
                            </div>
                            <div class="col-md-6">
                                <label for="punishmentDurationUnit" class="form-label">Duration Unit</label>
                                <select class="form-select" id="punishmentDurationUnit" name="duration_unit">
                                    <option value="minutes">Minutes</option>
                                    <option value="hours">Hours</option>
                                    <option value="days" selected>Days</option>
                                    <option value="weeks">Weeks</option>
                                    <option value="months">Months</option>
                                    <option value="permanent">Permanent</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="punishmentPublic" name="public">
                            <label class="form-check-label" for="punishmentPublic">
                                Make punishment public
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="submitPunishment">Apply Punishment</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Note Modal -->
    <div class="modal fade" id="noteModal" tabindex="-1" aria-labelledby="noteModalLabel">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="noteModalLabel">Add Note</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="noteForm">
                        <input type="hidden" id="notePlayerXuid" name="xuid">
                        <input type="hidden" id="notePlayerName" name="username">

                        <div class="mb-3">
                            <label for="noteType" class="form-label">Note Type</label>
                            <select class="form-select" id="noteType" name="type" required>
                                <option value="behavior">Behavior</option>
                                <option value="support">Support</option>
                                <option value="payment">Payment</option>
                                <option value="bug">Bug</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="noteContent" class="form-label">Content</label>
                            <textarea class="form-control" id="noteContent" name="content" rows="5" required></textarea>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="noteImportant" name="important">
                            <label class="form-check-label" for="noteImportant">
                                Mark as important
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="submitNote">Save Note</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Rank Change Modal -->
    <div class="modal fade" id="rankModal" tabindex="-1" aria-labelledby="rankModalLabel">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rankModalLabel">Change Player Rank</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Warning Alert -->
                    <div class="alert alert-warning d-flex align-items-center mb-3" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            <strong>Important:</strong> The player must be offline before changing their rank.
                            Rank changes will not take effect if the player is currently online.
                        </div>
                    </div>

                    <form id="rankForm">
                        <input type="hidden" id="rankPlayerXuid" name="xuid">
                        <input type="hidden" id="rankPlayerName" name="username">

                        <div class="mb-3">
                            <label class="form-label">Player</label>
                            <input type="text" class="form-control" id="rankPlayerNameDisplay" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Current Rank</label>
                            <div class="form-control" id="rankCurrentRank" style="background-color: #2d3748; border-color: #4a5568;"></div>
                        </div>

                        <div class="mb-3">
                            <label for="rankSelect" class="form-label">New Rank</label>
                            <select class="form-select" id="rankSelect" name="new_rank" required>
                                <option value="player">Player</option>
                                <option value="vip">VIP</option>
                                <option value="mvp">MVP</option>
                                <option value="mmp">MMP</option>
                                <option value="mgp">MGP</option>
                                <option value="mlp">MLP</option>
                                <option value="cc">CC</option>
                                <option value="builder">Builder</option>
                                <option value="trainee">Trainee</option>
                                <option value="support">Support</option>
                                <option value="moderator">Moderator</option>
                                <option value="admin">Admin</option>
                                <option value="owner">Owner</option>
                                <option value="yt">YT</option>
                                <option value="youtuber">YouTuber</option>
                                <option value="secretary">Secretary</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="rankReason" class="form-label">Reason for Rank Change</label>
                            <textarea class="form-control" id="rankReason" name="reason" rows="3" placeholder="Explain why this rank change is being made..." required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="submitRankChange" onclick="submitRankChange()">Change Rank</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Custom JS -->
    <script>

        window.AUTH_DATA = {
            userId: '<?php echo htmlspecialchars($userData['id']); ?>',
            username: '<?php echo htmlspecialchars($userData['username']); ?>',
            role: '<?php echo htmlspecialchars($userData['role']); ?>',
            avatar: '<?php echo htmlspecialchars($userData['avatar']); ?>',
            sessionId: '<?php echo htmlspecialchars(session_id()); ?>'
        };
    </script>
    <script src="js/playerinfo.min.js?v=3"></script>

    <script>

        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                document.getElementById('sidebar').classList.toggle('active');
                document.getElementById('content').classList.toggle('active');
            });


            const punishmentModal = document.getElementById('punishmentModal');
            const noteModal = document.getElementById('noteModal');


            if (punishmentModal) {
                punishmentModal.addEventListener('show.bs.modal', function() {

                    setTimeout(() => {
                        punishmentModal.removeAttribute('aria-hidden');
                    }, 100);
                });


                punishmentModal.addEventListener('hidden.bs.modal', function() {

                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }


                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                });
            }


            if (noteModal) {
                noteModal.addEventListener('show.bs.modal', function() {

                    setTimeout(() => {
                        noteModal.removeAttribute('aria-hidden');
                    }, 100);
                });


                noteModal.addEventListener('hidden.bs.modal', function() {

                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }


                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                });
            }
        });
    </script>
</body>
</html>
