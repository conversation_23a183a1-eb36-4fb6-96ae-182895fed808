/* Enhanced Appeal Details Styling */

/* Appeal Details Panel */
#appealDetails {
    position: fixed;
    top: 0;
    right: -500px;
    width: 500px;
    max-width: 100%;
    height: 100vh;
    background-color: #fff;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    overflow-y: auto;
    transition: right 0.3s ease;
    padding: 0;
}

/* Dark mode for appeal details panel */
body.dark-mode #appealDetails {
    background-color: var(--secondary-color);
}

#appealDetails.active {
    right: 0;
}

/* Report modal styling */
.report-modal {
    background-color: white;
    position: relative;
    width: 100%;
    height: 100%;
}

body.dark-mode .report-modal {
    background-color: var(--secondary-color);
    color: var(--text-light);
}

/* Appeal header */
.appeal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.appeal-icon-container {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.appeal-icon-container.text-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.appeal-icon-container.text-success {
    background-color: rgba(25, 135, 84, 0.1);
}

.appeal-icon-container.text-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

body.dark-mode .appeal-header {
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

/* Quick info cards */
.appeal-quick-info .quick-info-card {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.appeal-quick-info .quick-info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.quick-info-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 1rem;
}

.quick-info-content {
    flex: 1;
}

.quick-info-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.quick-info-value {
    font-weight: 600;
    font-size: 0.9rem;
}

body.dark-mode .appeal-quick-info .quick-info-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .quick-info-icon {
    background-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .quick-info-label {
    color: #adb5bd;
}

/* Status indicators */
.bg-warning-subtle {
    background-color: rgba(255, 193, 7, 0.15);
    color: #856404;
}

.bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.15);
    color: #155724;
}

.bg-danger-subtle {
    background-color: rgba(220, 53, 69, 0.15);
    color: #721c24;
}

.bg-secondary-subtle {
    background-color: rgba(108, 117, 125, 0.15);
    color: #383d41;
}

.bg-info-subtle {
    background-color: rgba(13, 202, 240, 0.15);
    color: #0c5460;
}

/* Dark mode for status indicators */
body.dark-mode .bg-warning-subtle {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

body.dark-mode .bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.2);
    color: #28a745;
}

body.dark-mode .bg-danger-subtle {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

body.dark-mode .bg-secondary-subtle {
    background-color: rgba(108, 117, 125, 0.2);
    color: #adb5bd;
}

body.dark-mode .bg-info-subtle {
    background-color: rgba(13, 202, 240, 0.2);
    color: #0dcaf0;
}

/* Report sections */
.report-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
    color: var(--text-dark);
}

.report-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.report-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-dark);
    display: flex;
    align-items: center;
}

.report-section-title i {
    margin-right: 10px;
    color: var(--primary-color);
}

/* Dark mode for report sections */
body.dark-mode .report-section {
    border-bottom-color: var(--border-color-dark);
    color: var(--text-light);
}

body.dark-mode .report-section-title {
    color: var(--text-light);
}

/* Punishment card */
.punishment-card {
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.punishment-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.punishment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.punishment-id {
    font-weight: 600;
    display: flex;
    align-items: center;
}

.punishment-id code,
.card-body code {
    color: #FFD700 !important;
    background-color: rgba(255, 215, 0, 0.1) !important;
    border: 1px solid rgba(255, 215, 0, 0.3) !important;
    font-weight: 700;
    text-shadow: 0 0 2px rgba(255, 215, 0, 0.3);
}

.punishment-type {
    display: flex;
    align-items: center;
}

.punishment-details-content {
    padding: 16px;
}

.punishment-detail-item {
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px dashed #e9ecef;
}

.punishment-detail-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.detail-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
}

.detail-value {
    font-weight: 500;
}

body.dark-mode .punishment-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .punishment-header {
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .punishment-detail-item {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .detail-label {
    color: #adb5bd;
}

/* Appeal reason box */
.appeal-reason-box {
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.appeal-reason-content {
    white-space: pre-wrap;
    word-break: break-word;
    font-size: 0.9rem;
    line-height: 1.5;
}

body.dark-mode .appeal-reason-box {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

/* Resolution card */
.resolution-card {
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.resolution-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
}

.resolution-status {
    font-weight: 600;
    display: flex;
    align-items: center;
}

.resolution-details {
    padding: 16px;
}

.resolution-detail-item {
    margin-bottom: 8px;
}

.resolution-approved .resolution-header {
    background-color: rgba(25, 135, 84, 0.1);
    color: #155724;
    border-bottom-color: rgba(25, 135, 84, 0.2);
}

.resolution-denied .resolution-header {
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-bottom-color: rgba(220, 53, 69, 0.2);
}

.staff-notes {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    margin-top: 12px;
}

.staff-notes-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.staff-notes-content {
    font-size: 0.9rem;
    white-space: pre-wrap;
    word-break: break-word;
}

body.dark-mode .resolution-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .resolution-approved .resolution-header {
    background-color: rgba(25, 135, 84, 0.2);
    color: #28a745;
    border-bottom-color: rgba(25, 135, 84, 0.3);
}

body.dark-mode .resolution-denied .resolution-header {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border-bottom-color: rgba(220, 53, 69, 0.3);
}

body.dark-mode .staff-notes {
    background-color: rgba(0, 0, 0, 0.2);
}

body.dark-mode .staff-notes-label {
    color: #adb5bd;
}

/* Appeal actions */
.appeal-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 24px;
    margin-bottom: 12px;
}

.appeal-actions .btn {
    padding: 10px 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 160px;
    transition: all 0.2s;
    border-radius: 6px;
}

.appeal-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Timeline */
.timeline {
    position: relative;
    padding-left: 30px;
    margin-top: 20px;
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    left: 15px;
    height: 100%;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-radius: 8px;
    padding: 10px;
}

.timeline-item.bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.15);
    border-left: 3px solid #28a745;
}

.timeline-item.bg-danger-subtle {
    background-color: rgba(220, 53, 69, 0.15);
    border-left: 3px solid #dc3545;
}

.timeline-item.bg-warning-subtle {
    background-color: rgba(255, 193, 7, 0.15);
    border-left: 3px solid #ffc107;
}

.timeline-item.bg-primary-subtle {
    background-color: rgba(13, 110, 253, 0.15);
    border-left: 3px solid #0d6efd;
}

/* Punishment removal timeline item */
.timeline-item.punishment-removed {
    background-color: rgba(25, 135, 84, 0.15);
    border-left: 3px solid #28a745;
}

.timeline-item-last {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    top: 0;
    left: -30px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.timeline-marker.text-info {
    border-color: #0dcaf0 !important;
    color: #0dcaf0 !important;
}

.timeline-marker.text-success {
    border-color: #28a745 !important;
    color: #28a745 !important;
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.timeline-marker.text-danger {
    border-color: #dc3545 !important;
    color: #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.timeline-marker.text-primary {
    border-color: #0d6efd !important;
    color: #0d6efd !important;
}

.timeline-marker.text-warning {
    border-color: #ffc107 !important;
    color: #ffc107 !important;
    background-color: rgba(255, 193, 7, 0.1) !important;
}

.timeline-marker.text-secondary {
    border-color: #6c757d !important;
    color: #6c757d !important;
    background-color: rgba(108, 117, 125, 0.1) !important;
}

/* Punishment removal marker */
.timeline-marker.punishment-removed {
    border-color: #28a745;
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.timeline-content {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.timeline-title {
    font-weight: 600;
    font-size: 0.9rem;
}

.timeline-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.timeline-body {
    font-size: 0.85rem;
    color: #6c757d;
}

body.dark-mode .timeline:before {
    background-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .timeline-marker {
    background-color: var(--secondary-color);
    border-color: rgba(255, 255, 255, 0.2);
}

body.dark-mode .timeline-marker.text-success {
    border-color: #28a745 !important;
    color: #28a745 !important;
    background-color: rgba(40, 167, 69, 0.2) !important;
}

body.dark-mode .timeline-marker.text-danger {
    border-color: #dc3545 !important;
    color: #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.2) !important;
}

body.dark-mode .timeline-marker.text-info {
    border-color: #0dcaf0 !important;
    color: #0dcaf0 !important;
    background-color: rgba(13, 202, 240, 0.2) !important;
}

body.dark-mode .timeline-marker.text-primary {
    border-color: #0d6efd !important;
    color: #0d6efd !important;
    background-color: rgba(13, 110, 253, 0.2) !important;
}

body.dark-mode .timeline-marker.text-warning {
    border-color: #ffc107 !important;
    color: #ffc107 !important;
    background-color: rgba(255, 193, 7, 0.2) !important;
}

body.dark-mode .timeline-marker.text-secondary {
    border-color: #6c757d !important;
    color: #6c757d !important;
    background-color: rgba(108, 117, 125, 0.2) !important;
}

body.dark-mode .timeline-item.bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.2);
    border-left: 3px solid #28a745;
}

body.dark-mode .timeline-item.bg-danger-subtle {
    background-color: rgba(220, 53, 69, 0.2);
    border-left: 3px solid #dc3545;
}

body.dark-mode .timeline-item.bg-warning-subtle {
    background-color: rgba(255, 193, 7, 0.2);
    border-left: 3px solid #ffc107;
}

body.dark-mode .timeline-item.bg-primary-subtle {
    background-color: rgba(13, 110, 253, 0.2);
    border-left: 3px solid #0d6efd;
}

body.dark-mode .timeline-content {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .timeline-date,
body.dark-mode .timeline-body {
    color: #adb5bd;
}

body.dark-mode .punishment-id code,
body.dark-mode .card-body code {
    color: #FFD700 !important;
    background-color: rgba(255, 215, 0, 0.15) !important;
    border: 1px solid rgba(255, 215, 0, 0.4) !important;
    text-shadow: 0 0 3px rgba(255, 215, 0, 0.5);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #appealDetails {
        width: 100%;
        right: -100%;
    }

    .appeal-actions {
        flex-direction: column;
        gap: 10px !important;
    }

    .appeal-actions .btn {
        width: 100%;
    }

    .timeline {
        padding-left: 25px;
    }

    .timeline:before {
        left: 12px;
    }

    .timeline-marker {
        width: 24px;
        height: 24px;
        left: -25px;
    }

    .timeline-marker i {
        font-size: 0.8rem;
    }
}
