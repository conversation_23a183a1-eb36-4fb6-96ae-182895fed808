================================
MongoDB\\Collection::updateOne()
================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Collection::updateOne()

   Update at most one document that matches the filter criteria. If multiple
   documents match the filter criteria, only the :term:`first <natural order>`
   matching document will be updated.

   .. code-block:: php

      function updateOne($filter, $update, array $options = []): MongoDB\UpdateResult

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBCollection-method-updateOne-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBCollection-method-updateOne-option.rst

Return Values
-------------

A :phpclass:`MongoDB\\UpdateResult` object, which encapsulates a
:php:`MongoDB\\Driver\\WriteResult <class.mongodb-driver-writeresult>` object.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-bulkwriteexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Behavior
--------

.. include:: /includes/extracts/note-bson-comparison.rst
.. include:: /includes/extracts/bulkwriteexception-result.rst

Examples
--------

The following example updates one document with the ``restaurant_id`` of
``"40356151"`` by setting the ``name`` field to ``"Brunos on Astoria"``:

.. code-block:: php

   $collection = (new MongoDB\Client)->test->restaurants;

   $updateResult = $collection->updateOne(
       [ 'restaurant_id' => '40356151' ],
       [ '$set' => [ 'name' => 'Brunos on Astoria' ]]
   );

   printf("Matched %d document(s)\n", $updateResult->getMatchedCount());
   printf("Modified %d document(s)\n", $updateResult->getModifiedCount());

The output would then resemble::

   Matched 1 document(s)
   Modified 1 document(s)

See Also
--------

- :phpmethod:`MongoDB\\Collection::replaceOne()`
- :phpmethod:`MongoDB\\Collection::updateMany()`
- :phpmethod:`MongoDB\\Collection::bulkWrite()`
- :doc:`/tutorial/crud`
- :manual:`update </reference/command/update>` command reference in the MongoDB
  manual
