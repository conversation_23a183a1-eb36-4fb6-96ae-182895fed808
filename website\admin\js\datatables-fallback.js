/**
 * DataTables Fallback Script
 * This script checks if DataTables is loaded and loads it if not
 */

(function() {

    if (typeof jQuery === 'undefined') {

        const jqueryScript = document.createElement('script');
        jqueryScript.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
        jqueryScript.onload = function() {
            loadDataTables();
        };
        jqueryScript.onerror = function() {

        };
        document.head.appendChild(jqueryScript);
        return;
    }


    if (typeof $.fn.DataTable === 'undefined') {
        loadDataTables();
    }

    function loadDataTables() {

        if (!document.querySelector('link[href*="datatables"]')) {
            const dtCSS = document.createElement('link');
            dtCSS.rel = 'stylesheet';
            dtCSS.href = 'https://cdn.jsdelivr.net/npm/datatables.net-bs5@1.13.6/css/dataTables.bootstrap5.min.css';
            document.head.appendChild(dtCSS);
        }


        const dtScript = document.createElement('script');
        dtScript.src = 'https://cdn.jsdelivr.net/npm/datatables.net@1.13.6/js/jquery.dataTables.min.js';
        dtScript.onload = function() {

            const dtBsScript = document.createElement('script');
            dtBsScript.src = 'https://cdn.jsdelivr.net/npm/datatables.net-bs5@1.13.6/js/dataTables.bootstrap5.min.js';
            dtBsScript.onload = function() {

                jQuery(document).trigger('datatables:loaded');
            };
            dtBsScript.onerror = function() {

            };
            document.head.appendChild(dtBsScript);
        };
        dtScript.onerror = function() {

        };
        document.head.appendChild(dtScript);
    }
})();
