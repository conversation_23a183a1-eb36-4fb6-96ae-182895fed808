=======================================
MongoDB\\DeleteResult::isAcknowledged()
=======================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\DeleteResult::isAcknowledged()

   Return whether the write was acknowledged.

   .. code-block:: php

      function isAcknowledged(): boolean

Return Values
-------------

A boolean indicating whether the write was acknowledged.

See Also
--------

- :php:`MongoDB\\Driver\\WriteResult::isAcknowledged()
  <manual/en/mongodb-driver-writeresult.isacknowledged.php>`
- :manual:`Write Concern </reference/write-concern>` in the MongoDB manual
