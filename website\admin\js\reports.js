/**
 * Format a date in Eastern Time
 * @param {string|Date|Object} dateInput - Date to format
 * @param {string} timeZone - Timezone to use (default: America/New_York)
 * @returns {string} Formatted date string with ET timezone
 */
function formatDate(dateInput, timeZone = 'America/New_York') {
    if (!dateInput) return 'Unknown';


    if (typeof dateInput === 'string' && dateInput.endsWith(' ET')) {
        return dateInput;
    }

    try {
        let date;


        if (typeof dateInput === 'object') {
            if (dateInput.$date) {

                if (typeof dateInput.$date === 'string') {
                    date = new Date(dateInput.$date);
                } else if (dateInput.$date.$numberLong) {
                    date = new Date(parseInt(dateInput.$date.$numberLong));
                } else {
                    date = new Date(dateInput.$date);
                }
            } else if (dateInput instanceof Date) {
                date = dateInput;
            } else {

                date = new Date(String(dateInput));
            }
        } else if (typeof dateInput === 'number') {

            date = new Date(dateInput);
        } else {

            date = new Date(dateInput);
        }


        if (isNaN(date.getTime())) {
            return 'Unknown';
        }


        const options = {
            timeZone,
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };

        return date.toLocaleString('en-US', options) + ' ET';
    } catch (e) {
        return 'Unknown'; // Return Unknown for invalid dates
    }
}

document.addEventListener('DOMContentLoaded', function() {

    const authData = {
        userId: window.AUTH_DATA ? window.AUTH_DATA.userId : '',
        username: window.AUTH_DATA ? window.AUTH_DATA.username : '',
        role: window.AUTH_DATA ? window.AUTH_DATA.role : '',
        avatar: window.AUTH_DATA ? window.AUTH_DATA.avatar : ''
    };


    let allReports = [];
    let filteredReports = [];
    let currentPage = 1;
    let reportsPerPage = 12;
    let reportModal;


    initializePage();

    function initializePage() {

        reportModal = new bootstrap.Modal(document.getElementById('reportModal'));


        loadReports();


        setupEventListeners();
    }

    function loadReports(offset = 0, append = false) {
        if (!append) {
            // Show loading indicator only for initial load
            document.getElementById('reportsContainer').innerHTML = `
                <div class="col-12 text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading reports...</p>
                </div>
            `;
        }

        // Build URL with pagination parameters
        const url = `/adminapi/reports.php?limit=100&offset=${offset}`; // Added offset parameter

        fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Discord-ID': authData.userId,
                'X-Discord-Username': authData.username,
                'X-Discord-Role': authData.role,
                'X-Discord-Avatar': authData.avatar || '',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
        })
        .then(response => {
            if (!response.ok) {
                if (response.status === 403) {
                    throw new Error('Permission denied. You may not have the required role to access reports.');
                } else if (response.status === 401) {
                    throw new Error('Authentication required. Please log in again.');
                } else if (response.status === 500) {
                    throw new Error('Server error. The reports database may be experiencing high load. Please try again in a moment.');
                } else if (response.status === 503) {
                    throw new Error('Database is initializing. Please try again in a moment.');
                } else {
                    throw new Error(`Network response was not ok (Status: ${response.status})`);
                }
            }
            return response.json();
        })
        .then(data => {

            if (data.reports) {

                allReports = data.reports;
            } else if (Array.isArray(data)) {

                allReports = data;
            } else {

                allReports = [];
            }


            allReports.sort((a, b) => {

                if (a.status === 'Pending' && b.status !== 'Pending') {
                    return -1;
                }
                if (a.status !== 'Pending' && b.status === 'Pending') {
                    return 1;
                }


                if (a.status === 'Pending' && b.status === 'Pending') {

                    const priorityA = parseInt(a.priority) || 4;
                    const priorityB = parseInt(b.priority) || 4;


                    if (priorityA !== priorityB) {
                        return priorityA - priorityB;
                    }
                }


                const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
                const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
                return dateB - dateA;
            });


            updateCounters();


            populateRuleFilter();


            applyFilters();
        })
        .catch(error => {


            const isAuthError = error.message.includes('Authentication required') ||
                               error.message.includes('Permission denied');

            document.getElementById('reportsContainer').innerHTML = `
                <div class="col-12">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <h3 class="empty-state-title">Error Loading Reports</h3>
                        <p class="empty-state-text">
                            ${error.message || 'There was a problem loading the reports. Please try again later.'}
                        </p>
                        ${isAuthError ? `
                            <a href="/login" class="btn btn-primary mt-3">
                                <i class="fas fa-sign-in-alt me-2"></i> Log In Again
                            </a>
                        ` : `
                            <button class="btn btn-primary mt-3" onclick="location.reload()">
                                <i class="fas fa-sync-alt me-2"></i> Retry
                            </button>
                        `}
                        <p class="mt-3 text-muted small">If the problem persists, please contact an administrator.</p>
                    </div>
                </div>
            `;
        });
    }

    function updateCounters() {
        const totalCount = allReports.length;
        const pendingCount = allReports.filter(r => r.status === 'Pending').length;

        document.getElementById('totalCount').textContent = totalCount;
        document.getElementById('pendingCount').textContent = pendingCount;
    }

    function populateRuleFilter() {

        const ruleSelect = document.getElementById('ruleFilter');
        const uniqueRules = [...new Set(allReports.map(r => r.rule_broken).filter(Boolean))];


        uniqueRules.sort();


        uniqueRules.forEach(rule => {
            const option = document.createElement('option');
            option.value = rule;
            option.textContent = rule;
            ruleSelect.appendChild(option);
        });
    }

    function setupEventListeners() {

        document.getElementById('statusFilter').addEventListener('change', applyFilters);
        document.getElementById('ruleFilter').addEventListener('change', applyFilters);
        document.getElementById('priorityFilter').addEventListener('change', applyFilters);
        document.getElementById('searchFilter').addEventListener('input', debounce(applyFilters, 300));


        document.getElementById('resetFilters').addEventListener('click', resetFilters);


        document.getElementById('prevPage').addEventListener('click', () => changePage(currentPage - 1));
        document.getElementById('nextPage').addEventListener('click', () => changePage(currentPage + 1));


        document.getElementById('reportsContainer').addEventListener('click', function(e) {

            if (e.target.closest('.view-report')) {
                const reportId = e.target.closest('.view-report').dataset.id;
                showReportModal(reportId);
            }


            if (e.target.closest('.accept-report')) {
                const reportId = e.target.closest('.accept-report').dataset.id;
                acceptReport(reportId);
            }


            if (e.target.closest('.reject-report')) {
                const reportId = e.target.closest('.reject-report').dataset.id;
                rejectReport(reportId);
            }
        });


        document.getElementById('reportModal').addEventListener('click', function(e) {

            if (e.target.closest('.modal-accept-report')) {
                const reportId = e.target.closest('.modal-accept-report').dataset.id;
                acceptReport(reportId);
            }


            if (e.target.closest('.modal-reject-report')) {
                const reportId = e.target.closest('.modal-reject-report').dataset.id;
                rejectReport(reportId);
            }
        });
    }

    function applyFilters() {
        const statusFilter = document.getElementById('statusFilter').value;
        const ruleFilter = document.getElementById('ruleFilter').value;
        const priorityFilter = document.getElementById('priorityFilter').value;
        const searchFilter = document.getElementById('searchFilter').value.toLowerCase();


        filteredReports = allReports.filter(report => {

            if (statusFilter !== 'all' && report.status !== statusFilter) {
                return false;
            }


            if (ruleFilter !== 'all' && report.rule_broken !== ruleFilter) {
                return false;
            }


            if (priorityFilter !== 'all') {
                const priorityValue = parseInt(priorityFilter);

                const reportPriority = parseInt(report.priority);
                if (isNaN(reportPriority) || reportPriority !== priorityValue) {
                    return false;
                }
            }


            if (searchFilter && !(
                (report.offender_name && report.offender_name.toLowerCase().includes(searchFilter)) ||
                (report.report_id && report.report_id.toLowerCase().includes(searchFilter))
            )) {
                return false;
            }

            return true;
        });


        filteredReports.sort((a, b) => {

            if (a.status === 'Pending' && b.status !== 'Pending') {
                return -1;
            }
            if (a.status !== 'Pending' && b.status === 'Pending') {
                return 1;
            }


            if (a.status === 'Pending' && b.status === 'Pending') {

                const priorityA = parseInt(a.priority) || 4;
                const priorityB = parseInt(b.priority) || 4;


                if (priorityA !== priorityB) {
                    return priorityA - priorityB;
                }
            }


            const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
            const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
            return dateB - dateA;
        });


        currentPage = 1;


        renderReports();


        updatePagination();
    }

    function resetFilters() {
        document.getElementById('statusFilter').value = 'all';
        document.getElementById('ruleFilter').value = 'all';
        document.getElementById('priorityFilter').value = 'all';
        document.getElementById('searchFilter').value = '';

        applyFilters();
    }

    function renderReports() {
        const reportsContainer = document.getElementById('reportsContainer');


        const startIndex = (currentPage - 1) * reportsPerPage;
        const endIndex = startIndex + reportsPerPage;
        const paginatedReports = filteredReports.slice(startIndex, endIndex);


        if (filteredReports.length === 0) {
            reportsContainer.innerHTML = `
                <div class="col-12">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3 class="empty-state-title">No Reports Found</h3>
                        <p class="empty-state-text">
                            No reports match your current filters. Try adjusting your filters or resetting them.
                        </p>
                        <button class="btn btn-primary mt-3" id="emptyStateReset">
                            <i class="fas fa-filter me-2"></i> Reset Filters
                        </button>
                    </div>
                </div>
            `;


            document.getElementById('emptyStateReset').addEventListener('click', resetFilters);
            return;
        }


        let cardsHTML = '';

        paginatedReports.forEach(report => {

            let statusClass = 'secondary';
            let statusText = report.status || 'Unknown';

            if (report.status === 'Pending') {
                statusClass = 'pending';
            } else if (report.status === 'Accepted') {
                statusClass = 'accepted';
            } else if (report.status === 'Denied') {
                statusClass = 'denied';
            }


            let priorityClass = 'secondary';
            let priorityText = 'Low';

            if (report.priority === 1) {
                priorityClass = 'danger';
                priorityText = 'High';
            } else if (report.priority === 2) {
                priorityClass = 'warning';
                priorityText = 'Urgent';
            } else if (report.priority === 3) {
                priorityClass = 'info';
                priorityText = 'Medium';
            } else if (report.priority === 4 || !report.priority) {
                priorityClass = 'secondary';
                priorityText = 'Low';
            }


            const formattedDate = report.created_at_formatted || formatDate(report.created_at);


            const evidencePreview = report.evidence
                ? (report.evidence.length > 100 ? report.evidence.substring(0, 100) + '...' : report.evidence)
                : 'No evidence provided';


            cardsHTML += `
                <div class="col-md-6 col-lg-4 col-xl-3">
                    <div class="card report-card ${report.status === 'Pending' && (report.priority === 1 || report.priority === '1') ? 'high-priority-card' : ''}">
                        <div class="card-header">
                            <span class="report-id">#${report.report_id || 'Unknown'}</span>
                            <span class="status-badge ${statusClass}">${statusText}</span>
                            ${report.status === 'Pending' && (report.priority === 1 || report.priority === '1') ?
                                '<span class="priority-indicator"><i class="fas fa-exclamation-triangle"></i></span>' : ''}
                        </div>
                        <div class="card-body">
                            <div class="player-name">${report.offender_name || 'Unknown Player'}</div>
                            <div class="rule-violation">
                                <i class="fas fa-gavel me-1"></i> ${report.rule_broken || 'Unspecified Rule'}
                            </div>
                            <div class="report-date">
                                <i class="far fa-calendar-alt me-1"></i> ${formattedDate}
                            </div>
                            ${report.status === 'Pending' ? `
                            <div class="report-priority mt-2">
                                <span class="badge bg-${priorityClass}">
                                    <i class="fas fa-exclamation-circle me-1"></i> ${priorityText} Priority
                                </span>
                            </div>
                            ` : ''}
                            <div class="evidence-preview">
                                ${evidencePreview}
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="card-actions">
                                <button class="btn btn-sm btn-primary btn-action view-report" data-id="${report.report_id || report._id}">
                                    <i class="fas fa-eye me-1"></i> View Details
                                </button>
                                ${report.status === 'Pending' ? `
                                <div class="btn-group btn-group-sm" role="group">
                                    <button class="btn btn-success btn-action accept-report" data-id="${report.report_id || report._id}">
                                        <i class="fas fa-check me-1"></i> Accept
                                    </button>
                                    <button class="btn btn-danger btn-action reject-report" data-id="${report.report_id || report._id}">
                                        <i class="fas fa-times me-1"></i> Reject
                                    </button>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });


        reportsContainer.innerHTML = cardsHTML;
    }

    function updatePagination() {
        const totalPages = Math.ceil(filteredReports.length / reportsPerPage);
        const prevButton = document.getElementById('prevPage');
        const nextButton = document.getElementById('nextPage');


        const startIndex = filteredReports.length > 0 ? (currentPage - 1) * reportsPerPage + 1 : 0;
        const endIndex = Math.min(startIndex + reportsPerPage - 1, filteredReports.length);

        document.getElementById('showingStart').textContent = startIndex;
        document.getElementById('showingEnd').textContent = endIndex;
        document.getElementById('showingTotal').textContent = filteredReports.length;


        prevButton.disabled = currentPage <= 1;
        nextButton.disabled = currentPage >= totalPages;
    }

    function changePage(newPage) {
        const totalPages = Math.ceil(filteredReports.length / reportsPerPage);

        if (newPage >= 1 && newPage <= totalPages) {
            currentPage = newPage;
            renderReports();
            updatePagination();


            document.getElementById('reportsContainer').scrollIntoView({ behavior: 'smooth' });
        }
    }

    function showReportModal(reportId) {

        const report = allReports.find(r =>
            String(r.report_id) === String(reportId) ||
            (r._id && String(r._id) === String(reportId))
        );

        if (!report) {
            Swal.fire({
                icon: 'error',
                title: 'Report Not Found',
                text: `Could not find details for report #${reportId}.`
            });
            return;
        }


        let statusClass = 'secondary';
        let statusText = report.status || 'Unknown';

        if (report.status === 'Pending') {
            statusClass = 'pending';
        } else if (report.status === 'Accepted') {
            statusClass = 'accepted';
        } else if (report.status === 'Denied') {
            statusClass = 'denied';
        }


        let priorityClass = 'secondary';
        let priorityText = 'Low';

        if (report.priority === 1) {
            priorityClass = 'danger';
            priorityText = 'High';
        } else if (report.priority === 2) {
            priorityClass = 'warning';
            priorityText = 'Urgent';
        } else if (report.priority === 3) {
            priorityClass = 'info';
            priorityText = 'Medium';
        } else if (report.priority === 4 || !report.priority) {
            priorityClass = 'secondary';
            priorityText = 'Low';
        }


        const createdDate = report.created_at_formatted || formatDate(report.created_at);
        const processedDate = report.status !== 'Pending'
            ? (report.processed_at_formatted || formatDate(report.processed_at))
            : null;


        const modalContent = `
            <div class="report-modal-header">
                <div class="report-id">Report #${report.report_id || reportId}</div>
                <span class="status-badge ${statusClass}">${statusText}</span>
            </div>

            <div class="report-modal-section">
                <div class="report-modal-section-title">
                    <i class="fas fa-user-times"></i> Reported Player
                </div>
                <div class="player-info-box">
                    <div class="player-name">${report.offender_name || 'Unknown Player'}</div>
                    <div class="rule-violation">
                        <strong>Rule Violation:</strong> ${report.rule_broken || 'Not specified'}
                    </div>
                    ${report.status === 'Pending' ? `
                    <div class="report-priority mt-2">
                        <strong>Priority:</strong>
                        <span class="badge bg-${priorityClass}">
                            <i class="fas fa-exclamation-circle me-1"></i> ${priorityText}
                        </span>
                    </div>
                    ` : ''}
                </div>
            </div>

            <div class="report-modal-section">
                <div class="report-modal-section-title">
                    <i class="fas fa-calendar-alt"></i> Timeline
                </div>
                <ul class="timeline-list">
                    <li class="timeline-item">
                        <span class="timeline-label">Submitted:</span>
                        ${createdDate || 'Unknown'}
                    </li>
                    ${report.status !== 'Pending' ? `
                    <li class="timeline-item">
                        <span class="timeline-label">Processed By:</span>
                        ${report.processed_by_name || 'Unknown'}
                    </li>
                    <li class="timeline-item">
                        <span class="timeline-label">Processed On:</span>
                        ${processedDate || 'Unknown'}
                    </li>
                    ` : ''}
                </ul>
            </div>

            <div class="report-modal-section">
                <div class="report-modal-section-title text-center">
                    <i class="fas fa-search"></i> Evidence
                </div>
                <div class="evidence-box">
                    ${report.evidence
                        ? formatEvidenceText(report.evidence)
                        : '<div class="text-muted text-center py-3"><i class="fas fa-info-circle me-2"></i>No evidence provided</div>'}
                </div>
            </div>
        `;


        let footerContent = '';

        if (report.status === 'Pending') {
            footerContent = `
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success modal-accept-report" data-id="${report.report_id || reportId}">
                    <i class="fas fa-check me-1"></i> Accept Report
                </button>
                <button type="button" class="btn btn-danger modal-reject-report" data-id="${report.report_id || reportId}">
                    <i class="fas fa-times me-1"></i> Reject Report
                </button>
            `;
        } else {
            footerContent = `
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            `;
        }


        document.getElementById('reportModalContent').innerHTML = modalContent;
        document.getElementById('reportModalFooter').innerHTML = footerContent;


        document.getElementById('reportModalLabel').textContent = `Report #${report.report_id || reportId}`;


        reportModal.show();
    }

    function acceptReport(reportId) {

        if (reportModal) {
            reportModal.hide();
        }

        Swal.fire({
            title: 'Accept Report',
            html: `
                <div class="text-start">
                    <p>Are you sure you want to accept this report?</p>
                    <p class="mt-2"><strong>Accepting means:</strong></p>
                    <ul class="text-start">
                        <li>You agree that the reported behavior violates server rules</li>
                        <li>Appropriate action should be taken against the reported player</li>
                        <li>The reporter will be notified that their report was accepted</li>
                    </ul>
                </div>
            `,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, accept it'
        }).then((result) => {
            if (result.isConfirmed) {

                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');





                fetch(`/adminapi/reports/accept?id=${reportId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Discord-ID': authData.userId,
                        'X-Discord-Username': authData.username,
                        'X-Discord-Role': authData.role,
                        'X-Discord-Avatar': authData.avatar || '',
                        'X-CSRF-TOKEN': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'include'
                })
                .then(response => {


                    if (!response.ok) {
                        if (response.status === 404) {
                            throw new Error('API endpoint not found. Please check server configuration.');
                        } else if (response.status === 403) {
                            throw new Error('Permission denied. You may not have the required role.');
                        } else if (response.status === 401) {
                            throw new Error('Authentication required. Please log in again.');
                        } else {
                            throw new Error(`Network response was not ok (Status: ${response.status})`);
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    let message = data.message || 'Report has been accepted successfully.';


                    let htmlMessage = `
                        <div class="text-start">
                            <p>${message}</p>
                            <p class="mt-2 text-success"><i class="fas fa-check-circle"></i> The reporter will be notified that appropriate action has been taken.</p>
                        </div>
                    `;

                    Swal.fire({
                        icon: 'success',
                        title: 'Report Accepted',
                        html: htmlMessage
                    });


                    if (reportModal._isShown) {
                        reportModal.hide();
                    }


                    loadReports();
                })
                .catch(error => {
                    console.error('Error accepting report:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Failed to Accept Report',
                        text: 'There was a problem accepting the report. Please try again.'
                    });
                });
            }
        });
    }

    function rejectReport(reportId) {

        if (reportModal) {
            reportModal.hide();
        }

        Swal.fire({
            title: 'Reject Report',
            html: `
                <div class="text-start">
                    <p>Are you sure you want to reject this report?</p>
                    <p class="mt-2"><strong>Rejecting means:</strong></p>
                    <ul class="text-start">
                        <li>You've determined that no server rules were violated</li>
                        <li>No action needs to be taken against the reported player</li>
                        <li>The reporter will be notified that their report was denied</li>
                        <li>They will be encouraged to provide more evidence if the issue persists</li>
                    </ul>
                </div>
            `,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, reject it'
        }).then((result) => {
            if (result.isConfirmed) {

                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');





                fetch(`/adminapi/reports/reject?id=${reportId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Discord-ID': authData.userId,
                        'X-Discord-Username': authData.username,
                        'X-Discord-Role': authData.role,
                        'X-Discord-Avatar': authData.avatar || '',
                        'X-CSRF-TOKEN': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'include'
                })
                .then(response => {


                    if (!response.ok) {
                        if (response.status === 404) {
                            throw new Error('API endpoint not found. Please check server configuration.');
                        } else if (response.status === 403) {
                            throw new Error('Permission denied. You may not have the required role.');
                        } else if (response.status === 401) {
                            throw new Error('Authentication required. Please log in again.');
                        } else {
                            throw new Error(`Network response was not ok (Status: ${response.status})`);
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    let message = data.message || 'Report has been rejected successfully.';


                    let emailStatus = '';
                    if (data.notification_sent === true) {
                        emailStatus = '<p class="mt-2 text-success"><i class="fas fa-envelope"></i> A notification email has been sent to the reporter.</p>';
                    } else if (data.notification_sent === false) {
                        emailStatus = '<p class="mt-2 text-warning"><i class="fas fa-exclamation-triangle"></i> No notification email was sent.</p>';
                    }


                    let htmlMessage = `
                        <div class="text-start">
                            <p>${message}</p>
                            <p class="mt-2 text-info"><i class="fas fa-info-circle"></i> The reporter will be notified that no action was deemed necessary.</p>
                            ${emailStatus}
                        </div>
                    `;

                    Swal.fire({
                        icon: 'success',
                        title: 'Report Rejected',
                        html: htmlMessage
                    });


                    if (reportModal._isShown) {
                        reportModal.hide();
                    }


                    loadReports();
                })
                .catch(error => {
                    console.error('Error rejecting report:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Failed to Reject Report',
                        text: 'There was a problem rejecting the report. Please try again.'
                    });
                });
            }
        });
    }


    function debounce(func, wait) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }


    function formatEvidenceText(text) {
        if (!text) return '';


        return text.trim().replace(/\s+/g, ' ');
    }
});
