<?php

header('Content-Type: application/json');


if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/db_access.php';
require_once __DIR__ . '/../includes/auth.php';


header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-TOKEN, X-Discord-ID, X-Discord-Username, X-Discord-Role, X-Discord-Avatar, X-Session-ID, X-Requested-With');
header('Access-Control-Allow-Credentials: true');


if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}


$headers = getallheaders();
error_log("Punishments API: Headers: " . json_encode($headers));


if (!is_authenticated()) {

    if (isset($headers['X-Discord-ID']) && !empty($headers['X-Discord-ID'])) {

        $_SESSION['discord_user_id'] = $headers['X-Discord-ID'];
        $_SESSION['discord_username'] = $headers['X-Discord-Username'] ?? 'Unknown User';
        $_SESSION['discord_user_role'] = $headers['X-Discord-Role'] ?? ROLE_TRAINEE;
        $_SESSION['auth_time'] = time();

        error_log("Punishments API: Using header information for authentication");
    } else {
        http_response_code(401);
        echo json_encode(['error' => 'Not authenticated']);
        exit;
    }
}


refresh_session();


if (!has_role(ROLE_TRAINEE)) {
    error_log("Punishments API: Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
    http_response_code(403);
    echo json_encode(['error' => 'Permission denied. You do not have the required role to access this resource.']);
    exit;
}


$headers = getallheaders();
update_staff_activity(
    $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? null,
    $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? null,
    $headers['X-Discord-Role'] ?? $_SESSION['discord_user_role'] ?? null,
    $headers['X-Discord-Avatar'] ?? $_SESSION['discord_avatar'] ?? null
);


if ($_SERVER['REQUEST_METHOD'] === 'GET') {

    if (isset($_GET['predefined_types'])) {

        echo json_encode(get_predefined_punishments());
        exit;
    }

    $username = $_GET['username'] ?? $_GET['player'] ?? null;




    try {

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('punishments', $collections)) {

            echo json_encode([]);
            exit;
        }


        $filter = [];


        if ($username) {
            $filter['player_name'] = ['$regex' => $username, '$options' => 'i'];
        }


        $options = [
            'sort' => ['issued_at' => -1], // Sort by issued date descending (newest first)
            'limit' => 100 // Limit to 100 most recent punishments
        ];


        $cursor = $db->punishments->find($filter, $options);


        $punishments = [];
        foreach ($cursor as $document) {

            $punishment = [
                'punishment_id' => $document['punishment_id'] ?? ('PUN-' . substr((string)$document['_id'], -8)),
                'punishment_type' => $document['punishment_type'] ?? 'unknown',
                'player_name' => $document['player_name'] ?? 'Unknown Player',
                'player_xuid' => $document['player_xuid'] ?? '',
                'reason' => $document['reason'] ?? '',
                'staff_name' => $document['staff_name'] ?? 'Unknown Staff',
                'staff_id' => $document['staff_id'] ?? '',
                'active' => $document['active'] ?? false,
                'issued_at' => isset($document['issued_at']) ? $document['issued_at']->toDateTime()->format('c') : null,
                'expires_at' => isset($document['expires_at']) ? $document['expires_at']->toDateTime()->format('c') : null,
                'removed_at' => isset($document['removed_at']) ? $document['removed_at']->toDateTime()->format('c') : null,
                'removed_by' => $document['removed_by'] ?? null,
                'removed_by_name' => $document['removed_by_name'] ?? null,
                'removed_reason' => $document['removed_reason'] ?? null,
                'evidence' => $document['evidence'] ?? '',
                'staff_notes' => $document['staff_notes'] ?? '',
                'offense_type' => $document['offense_type'] ?? '',
                'offense_count' => $document['offense_count'] ?? 1
            ];

            $punishments[] = $punishment;
        }


        echo json_encode($punishments);
        exit;
    } catch (Exception $e) {
        error_log("Error getting player punishments: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
        exit;
    }
}


if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    $uri_parts = explode('/', $_SERVER['REQUEST_URI']);


    error_log("Punishments API: URI parts: " . json_encode($uri_parts) . ", Full URI: " . $_SERVER['REQUEST_URI']);


    if (strpos($_SERVER['REQUEST_URI'], '/add') !== false && count($uri_parts) >= 3) {

        if (!has_role(ROLE_MODERATOR)) {
            error_log("Punishments API (Add): Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set'));
            http_response_code(403);
            echo json_encode(['error' => 'Permission denied. You do not have the required role to add punishments.']);
            exit;
        }

        $type = $uri_parts[count($uri_parts) - 2]; // warnings, bans, mutes


        $json_data = file_get_contents('php://input');
        $data = json_decode($json_data, true);

        $player = $data['player'] ?? null;
        $reason = $data['reason'] ?? null;
        $duration = $data['duration'] ?? null;

        if (!$player || !$reason) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing required parameters']);
            exit;
        }

        $staff_id = $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
        $staff_name = $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? 'unknown';


        $player_xuid = $data['player_xuid'] ?? '';
        $offense_type = $data['offense_type'] ?? '';
        $evidence = $data['evidence'] ?? '';
        $notes = $data['notes'] ?? $data['staff_notes'] ?? '';


        $result = add_punishment($type, $player, $reason, $staff_id, $staff_name, $duration, $player_xuid, $offense_type, $evidence, $notes);
        echo json_encode($result);
        exit;
    }


    if (strpos($_SERVER['REQUEST_URI'], '/remove') !== false && count($uri_parts) >= 4) {

        // Get the punishment type from the URI
        $type = $uri_parts[count($uri_parts) - 3]; // This should be 'bans', 'mutes', or 'warnings'

        // Allow ADMIN or higher to remove any punishment type
        $requiredRole = ROLE_ADMIN;

        if (!has_role($requiredRole)) {
            error_log("Punishments API (Remove): Permission denied - User does not have required role. User role: " . ($_SESSION['discord_user_role'] ?? 'Not set') . ", Required role: " . $requiredRole);
            http_response_code(403);
            echo json_encode(['error' => 'Permission denied. You do not have the required role to remove punishments.']);
            exit;
        }


        if (!in_array($type, ['bans', 'mutes', 'warnings', 'ban', 'mute', 'warning'])) {
            error_log("Invalid punishment type in URI: $type. URI parts: " . json_encode($uri_parts));
            $type = 'unknown'; // Set a default value to prevent undefined variable errors
        }


        $json_data = file_get_contents('php://input');
        $data = json_decode($json_data, true);
        $punishment_id = $data['punishment_id'] ?? '';

        if (empty($punishment_id)) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing punishment ID']);
            exit;
        }

        $staff_id = $headers['X-Discord-ID'] ?? $_SESSION['discord_user_id'] ?? 'unknown';
        $staff_name = $headers['X-Discord-Username'] ?? $_SESSION['discord_username'] ?? 'unknown';


        error_log("Punishment removal request: Type=$type, ID=$punishment_id, Staff=$staff_name");
        $reason = $data['reason'] ?? 'Manually removed by staff';


        $result = remove_punishment($punishment_id, $staff_id, $staff_name, $reason);
        echo json_encode($result);
        exit;
    }

    http_response_code(400);
    echo json_encode(['error' => 'Invalid request']);
    exit;
}

http_response_code(405);
echo json_encode(['error' => 'Method not allowed']);
?>