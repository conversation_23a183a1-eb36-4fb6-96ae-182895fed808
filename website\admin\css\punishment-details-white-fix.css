/* Fix for white backgrounds in punishment details modal in dark mode */

/* Fix for card backgrounds in punishment details */
body.dark-mode #punishmentDetailsModal .card {
    background-color: var(--secondary-color) !important;
    border-color: var(--border-color-dark) !important;
    color: var(--text-light) !important;
}

/* Fix for card body in punishment details */
body.dark-mode #punishmentDetailsModal .card-body {
    background-color: var(--secondary-color) !important;
    color: var(--text-light) !important;
}

/* Fix for the white sections in punishment details */
body.dark-mode #punishmentDetailsModal .punishment-reason,
body.dark-mode #punishmentDetailsModal .punishment-evidence,
body.dark-mode #punishmentDetailsModal .punishment-notes {
    background-color: rgba(30, 42, 56, 0.7) !important;
    color: var(--text-light) !important;
    border: 1px solid var(--border-color-dark) !important;
}

/* Fix for p-3 rounded sections that might be white */
body.dark-mode #punishmentDetailsModal .p-3.rounded {
    background-color: rgba(30, 42, 56, 0.7) !important;
    color: var(--text-light) !important;
    border: 1px solid var(--border-color-dark) !important;
}

/* Fix for text in punishment details */
body.dark-mode #punishmentDetailsModal .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Fix for links in punishment details */
body.dark-mode #punishmentDetailsModal a {
    color: var(--accent-color) !important;
}

body.dark-mode #punishmentDetailsModal a:hover {
    color: var(--accent-hover) !important;
    text-decoration: underline;
}

/* Fix for badges in punishment details */
body.dark-mode #punishmentDetailsModal .badge.bg-light {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: var(--text-light) !important;
}

/* Fix for strong text in punishment details */
body.dark-mode #punishmentDetailsModal strong {
    color: var(--text-light) !important;
}

/* Fix for Player & Staff Information sections */
body.dark-mode #punishmentDetailsModal .row .card {
    background-color: var(--secondary-color) !important;
}

/* Fix for Reason, Evidence, Staff Notes sections */
body.dark-mode #punishmentDetailsModal .card .rounded {
    background-color: rgba(30, 42, 56, 0.7) !important;
    color: var(--text-light) !important;
}

/* Fix for Offense Information section */
body.dark-mode #punishmentDetailsModal .card:last-child {
    background-color: var(--secondary-color) !important;
}

/* Fix for any remaining white backgrounds */
body.dark-mode #punishmentDetailsModal .bg-light,
body.dark-mode #punishmentDetailsModal .bg-white,
body.dark-mode #punishmentDetailsModal [class*="bg-light"],
body.dark-mode #punishmentDetailsModal [class*="bg-white"] {
    background-color: var(--secondary-color) !important;
    color: var(--text-light) !important;
}

/* Force all elements in the modal to have proper colors */
body.dark-mode #punishmentDetailsModal * {
    color: var(--text-light);
}

body.dark-mode #punishmentDetailsModal *[style*="background-color: white"],
body.dark-mode #punishmentDetailsModal *[style*="background-color: #fff"],
body.dark-mode #punishmentDetailsModal *[style*="background-color:#fff"],
body.dark-mode #punishmentDetailsModal *[style*="background: white"],
body.dark-mode #punishmentDetailsModal *[style*="background:#fff"] {
    background-color: var(--secondary-color) !important;
}
